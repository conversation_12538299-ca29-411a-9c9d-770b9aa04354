{"version": 3, "file": "static/js/584.26f2e396.chunk.js", "mappings": "sJAQA,MAWA,EAX8CA,IAGvC,IAHwC,SAC7CC,EAAQ,UACRC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,MAAID,UAAW,kCAAkCA,IAAYD,SAC1DA,GACE,C,uDCCT,MAmCA,EAnCkCD,IAK3B,IAL4B,KACjCI,EAAI,UACJC,EAAS,SACTC,EAAQ,UACRJ,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4BAA4BA,IAAYD,UACtDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SACnCG,EAAKG,KAAKC,IACT,MAAMC,EAAWD,EAAIE,KAAOL,EAE5B,OACEF,EAAAA,EAAAA,KAAA,UAEEQ,QAASA,KAAOH,EAAII,UAAYN,EAASE,EAAIE,IAC7CR,UAAW,iNAGPO,EACE,8BACA,iGACFD,EAAII,SAAW,gCAAkC,mCAErDA,SAAUJ,EAAII,SAASX,SAEtBO,EAAIK,OAZAL,EAAIE,GAaF,OAIX,C,mECxBV,MAAMI,EAA0BC,IAC9B,IAAKC,EAAAA,eAAqBD,GAAO,MAAO,2BAGxC,MAGME,GAHYF,EAAKG,MAAMhB,WAAa,IAGbiB,MAAM,kBACnC,GAAIF,EAAY,CAEd,MAAO,MADOA,EAAW,OAE3B,CAEA,MAAO,0BAA0B,EAkDnC,EA/C8CjB,IAKvC,IALwC,MAC7CoB,EAAK,KACLC,EAAI,KACJN,EAAI,YACJO,EAAeC,GAAUA,EAAMC,YAChCxB,EACC,OACEG,EAAAA,EAAAA,KAACsB,EAAAA,EAAI,CAAAxB,UACHyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,oBAAoBY,EAAuBC,KAAQd,SAEhEe,EAAAA,eAAqBD,GACpB,MACE,MAAMY,EAAcZ,EAEdE,GADoBU,EAAYT,MAAMhB,WAAa,IACpBiB,MAAM,mBACrCS,EAAaX,EAAaA,EAAW,GAAK,gBAGhD,OAAOD,EAAAA,aAAmBW,EAAa,CACrCzB,UAAW,WAAgB0B,KAE9B,EAVD,GAYAb,KAGJW,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,cAAaD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oCAAmCD,SAAEmB,KAClDM,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,sBAAqBD,SAAA,EAClCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAChDqB,EAAYD,EAAKQ,cAEHC,IAAhBT,EAAKU,SACJL,EAAAA,EAAAA,MAAA,KAAGxB,UAAW,mDACZmB,EAAKU,QAAU,EAAI,iBAAmB,gBACrC9B,SAAA,CACAoB,EAAKU,QAAU,EAAI,IAAM,GAAIV,EAAKU,OAAOC,QAAQ,GAAG,iBAM1D,ECvCX,EArBsDhC,IAG/C,IAHgD,QACrDiC,EAAO,UACP/B,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yCAAyCA,IAAYD,SAClEgC,EAAQ1B,KAAI,CAAC2B,EAAQC,KACpBhC,EAAAA,EAAAA,KAACiC,EAAU,CAEThB,MAAOc,EAAOd,MACdC,KAAM,CACJQ,MAA+B,kBAAjBK,EAAOX,MAAqBc,WAAWH,EAAOX,QAAU,EAAIW,EAAOX,MACjFQ,OAAQG,EAAOI,QAAU,GAE3BvB,KAAMmB,EAAOnB,MANRoB,MASL,C,gDCtCV,SAASI,EAAkBvC,EAIxBwC,GAAQ,IAJiB,MAC1BpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,+OAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBuB,E,8ICvBlD,SAASc,EAAYrD,EAIlBwC,GAAQ,IAJW,MACpBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,uGAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBqC,G,iHCvBlD,SAASC,EAAUtD,EAIhBwC,GAAQ,IAJS,MAClBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,0CACYpC,EAAAA,cAAoB,OAAQ,CAC3CkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,mFAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBsC,GC3BlD,SAASC,EAAYvD,EAIlBwC,GAAQ,IAJW,MACpBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,geAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBuC,G,aCKlD,MAsMA,EAtMkEvD,IAAmB,IAAlB,SAAEwD,GAAUxD,EA2B7E,OACE0B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EAExBE,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,oBACNsC,YAAY,qDAAoDzD,UAEhEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACxBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,UAChDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACwD,EAAAA,EAAM,IACAH,EAASI,MAAQ,CAAEC,IAAKL,EAASI,MACtCE,IAAKN,EAASO,KACdA,KAAMP,EAASO,KACfC,KAAK,QAEPtC,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEuD,EAASO,QAC5DrC,EAAAA,EAAAA,MAAA,KAAGxB,UAAU,wBAAuBD,SAAA,CAAC,OAAKuD,EAAS9C,OACnDgB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,mCAAkCD,SAAA,EAC/CE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAW,CAACC,OAAQV,EAASU,OAAQC,KAAK,cAC3ChE,EAAAA,EAAAA,KAAA,QAAMD,UAAW,4EACiB,aAAhCsD,EAASY,mBACL,8BACgC,YAAhCZ,EAASY,mBACP,gCACA,2BACLnE,SACAuD,EAASY,mBACRZ,EAASY,mBAAmBC,OAAO,GAAGC,cAAgBd,EAASY,mBAAmBG,MAAM,GACxF,4BAWhBpE,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,sBACNsC,YAAY,wDAAuDzD,UAEnEyB,EAAAA,EAAAA,MAAC8C,EAAAA,EAAU,CAAAvE,SAAA,EACTE,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,iBACNU,OACEG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACuE,EAAAA,EAAQ,CAACxE,UAAU,+BACnBsD,EAASmB,oBAIhBxE,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,gBACNU,OACEG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACyE,EAAAA,EAAY,CAAC1E,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,KACE0E,KAAM,UAAUrB,EAASsB,QACzB5E,UAAU,uCAAsCD,SAE/CuD,EAASsB,cAKlB3E,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,eACNU,OACEG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CAAC7E,UAAU,gCACrBC,EAAAA,EAAAA,KAAA,KACE0E,KAAM,OAAOrB,EAASwB,QACtB9E,UAAU,uCAAsCD,SAE/CuD,EAASwB,cAKlB7E,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,UACNU,OACEG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAACmD,EAAU,CAACpD,UAAU,uCACtBC,EAAAA,EAAAA,KAAA,QAAAF,SAAOuD,EAASyB,eAIrBzB,EAAS0B,UACR/E,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,UACNU,OACEG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACoD,EAAY,CAACrD,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,KACE0E,KAAMrB,EAAS0B,QACfC,OAAO,SACPC,IAAI,sBACJlF,UAAU,uCAAsCD,SAE/CuD,EAAS0B,qBAUxB/E,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,uBACNsC,YAAY,8CAA6CzD,UAEzDyB,EAAAA,EAAAA,MAAC8C,EAAAA,EAAU,CAAAvE,SAAA,EAETE,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,sBACNU,OACEpB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uBAAsBD,SAClCuD,EAAS6B,YAAc7B,EAAS6B,WAAWC,OAAS,EACnD9B,EAAS6B,WAAW9E,KAAI,CAACgF,EAAUpD,KACjChC,EAAAA,EAAAA,KAAA,QAEED,UAAU,oGAAmGD,SAE5GsF,GAHIpD,MAOThC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SAAC,gCAKhDE,EAAAA,EAAAA,KAACsE,EAAAA,EAAU,CACT5D,MAAM,iBACNU,OAAOpB,EAAAA,EAAAA,KAAC8D,EAAAA,EAAW,CAACC,OAAQV,EAASU,OAAQC,KAAK,qBAMxDhE,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,sBACNsC,YAAY,0CAAyCzD,UAErDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACxByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,CAlLvBiE,KAC3B,OAAQA,GACN,IAAK,WACH,OAAO/D,EAAAA,EAAAA,KAACqF,EAAAA,EAAe,CAACtF,UAAU,2BACpC,IAAK,UACH,OAAOC,EAAAA,EAAAA,KAACsF,EAAAA,EAAS,CAACvF,UAAU,4BAC9B,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAACuF,EAAAA,EAAW,CAACxF,UAAU,yBAChC,QACE,OAAOC,EAAAA,EAAAA,KAACsF,EAAAA,EAAS,CAACvF,UAAU,0BAChC,EAyKSyF,CAAoBnC,EAASY,oBAAsB,YACpD1C,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SACf,aAAhCuD,EAASY,mBAAoC,oBACb,YAAhCZ,EAASY,mBAAmC,uBAC5C,2BAEHjE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SA7KrBiE,KAC3B,OAAQA,GACN,IAAK,WACH,MAAO,WACT,IAAK,UACH,MAAO,uBACT,IAAK,WACH,MAAO,WACT,QACE,MAAO,iBACX,EAoKa0B,CAAoBpC,EAASY,oBAAsB,yBAM1D,EC9NV,SAASyB,EAAY7F,EAIlBwC,GAAQ,IAJW,MACpBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,sQAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB6E,GCvBlD,SAASC,EAAgB9F,EAItBwC,GAAQ,IAJe,MACxBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,+RAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB8E,G,4CCGlD,MA4LA,EA5L4D9F,IAGrD,IAHsD,UAC3D+F,EACAC,WAAYC,GACbjG,EACC,MAAM,SAAEkG,IAAaC,EAAAA,EAAAA,MACdC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAkC,OAC3EC,EAAoBC,IAAyBF,EAAAA,EAAAA,WAAS,GAkBvDG,EAA0BC,IAE9BR,EAAS,eAAeQ,EAAS3C,WACjC4C,QAAQC,IAAI,qBAAsBF,EAAS,EAG7C,OAAyB,IAArBX,EAAUT,QAEVnF,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,yBACNsC,YAAY,gDAA+CzD,UAE3DyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,EACpCE,EAAAA,EAAAA,KAAC0F,EAAY,CAAC3F,UAAU,qCACxBC,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,2BACvDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,yEAShDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,yBACNsC,YAAY,+DAA8DzD,UAE1EE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC9ByB,EAAAA,EAAAA,MAAA,SAAOxB,UAAU,sCAAqCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,UAC3ByB,EAAAA,EAAAA,MAAA,MAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAI0G,MAAM,MAAM3G,UAAU,iFAAgFD,SAAC,cAG3GE,EAAAA,EAAAA,KAAA,MAAI0G,MAAM,MAAM3G,UAAU,iFAAgFD,SAAC,UAG3GE,EAAAA,EAAAA,KAAA,MAAI0G,MAAM,MAAM3G,UAAU,iFAAgFD,SAAC,iBAG3GE,EAAAA,EAAAA,KAAA,MAAI0G,MAAM,MAAM3G,UAAU,iFAAgFD,SAAC,kBAK/GE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,oCAAmCD,SACjD8F,EAAUxF,KAAKmG,IACdhF,SAAAA,EAAAA,MAAA,MAAsBxB,UAAU,mBAAkBD,SAAA,EAChDE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAC2F,EAAgB,CAAC5F,UAAU,gCAC5BwB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SAC/CyG,EAAS3C,QAEZrC,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,CACnCyG,EAASI,SAAS,YAAIC,EAAAA,EAAAA,IAAeL,EAASM,sBAKvD7G,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oGAAmGD,UAhFvGkE,EAiFYuC,EAASvC,KAhFN,CACzC8C,iBAAkB,mBAClBC,gBAAiB,kBACjBC,UAAW,mBACXC,cAAe,gBACfC,MAAO,kBAESlD,IAAS,yBA4EbhE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,0CAAyCD,SAAA,EACtDE,EAAAA,EAAAA,KAACmH,EAAAA,EAAY,CAACpH,UAAU,gCACvBqH,EAAAA,EAAAA,IAAWb,EAASc,kBAGzBrH,EAAAA,EAAAA,KAAA,MAAID,UAAU,kDAAiDD,UAC7DyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR1D,KAAK,KACLrD,QAASA,IApFH+F,KAC1BL,EAAoBK,GACpBF,GAAsB,EAAK,EAkFQmB,CAAmBjB,GAClC3F,MAAMZ,EAAAA,EAAAA,KAACyH,EAAAA,EAAO,CAAC1H,UAAU,YAAaD,SACvC,UAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR1D,KAAK,KACLrD,QAASA,IAAM8F,EAAuBC,GACtC3F,MAAMZ,EAAAA,EAAAA,KAAC0H,EAAAA,EAAiB,CAAC3H,UAAU,YAAaD,SACjD,oBAxCEyG,EAAShG,IAjEFyD,KA+GjB,aAORiC,IACCjG,EAAAA,EAAAA,KAAC2H,EAAAA,EAAK,CACJC,OAAQxB,EACRyB,QAASA,IAAMxB,GAAsB,GACrCpF,MAAO,qBAAqBgF,EAAiBrC,OAC7CC,KAAK,KACLiE,QACEvG,EAAAA,EAAAA,MAAAwG,EAAAA,SAAA,CAAAjI,SAAA,EACEE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR/G,QAASA,IAAM6F,GAAsB,GAAOvG,SAC7C,WAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR/G,QAASA,IAAM8F,EAAuBL,GACtCrF,MAAMZ,EAAAA,EAAAA,KAAC0H,EAAAA,EAAiB,CAAC3H,UAAU,YAAaD,SACjD,gBAIJA,UAEDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EACxByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,4BAA2BD,SAAA,EACxCyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,iCAAgCD,SAAA,EAC7CyB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAC,gBAC5CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,SAAEmG,EAAiBU,eAEnDpF,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAC,gBAC5CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAAE8G,EAAAA,EAAAA,IAAeX,EAAiBY,gBAElEtF,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAC,kBAC5CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAAEsH,EAAAA,EAAAA,IAAWnB,EAAiBoB,oBAG/DpB,EAAiB+B,QAChBzG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAC,YAC5CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qBAAoBD,SAAEmG,EAAiB+B,eAM5DzG,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oEAAmED,SAAA,EAChFE,EAAAA,EAAAA,KAAC2F,EAAgB,CAAC5F,UAAU,qCAC5BC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,+EAO9C,E,qDClLV,MAyPA,EAzP0DD,IAInD,IAJoD,SACzDoI,EACApC,WAAYC,EAAW,gBACvBoC,GACDrI,EACC,MAAMsI,GAAWC,EAAAA,EAAAA,OACX,YAAEC,EAAW,UAAEC,EAAS,SAAEvC,IAAaC,EAAAA,EAAAA,MACtCuC,EAAiBC,IAAsBrC,EAAAA,EAAAA,UAAiC,OACxEsC,EAAmBC,IAAwBvC,EAAAA,EAAAA,WAAS,GAErDwC,EAAwB5E,IAC5B,OAAQA,GACN,IAAK,SACH,MAAO,SACT,IAAK,WAIL,QACE,MAAO,UAHT,IAAK,eACH,MAAO,WAGX,EAaI6E,EAAqBC,IACzBV,EAASW,EAAAA,EAAOC,uBAAuBF,EAAQtI,IAAI,EA4B/CyI,EAAqC,CACzC,CACEC,IAAK,OACLvI,MAAO,eACPwI,UAAU,EACVC,OAAQA,CAACC,EAAgBP,KACvBtH,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,CAC/B+I,EAAQQ,OACPrJ,EAAAA,EAAAA,KAAA,OACE0D,IAAKmF,EAAQQ,MACb1F,IAAKkF,EAAQjF,KACb7D,UAAU,4CAGZC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yEAAwED,UACrFE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAQ,CAACvJ,UAAU,6BAGxBwB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAE+I,EAAQjF,QACpDrC,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,CAAC,OAAK+I,EAAQtI,aAK5D,CACE0I,IAAK,MACLvI,MAAO,MACPwI,UAAU,EACVC,OAAS/H,IACPpB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,kCAAiCD,SAAEsB,KAGvD,CACE6H,IAAK,WACLvI,MAAO,WACPwI,UAAU,EACVC,OAAS/H,IACPpB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oGAAmGD,SAChHsB,KAIP,CACE6H,IAAK,QACLvI,MAAO,QACPwI,UAAU,EACVC,OAAS/H,IACPpB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,UAAEyJ,EAAAA,EAAAA,IAAenI,MAGhE,CACE6H,IAAK,QACLvI,MAAO,QACPwI,UAAU,EACVC,OAAQA,CAAC/H,EAAeyH,KACtB,MAAMW,GA/FYC,EA+FiBrI,EA/FFsI,EA+FSb,EAAQa,cAAgB,GA9FvD,iBA8F2Db,EAAQ9E,QA9FvC,IAAV0F,EACxB,CAAEE,KAAM,eAAgBC,MAAO,gBAC7BH,GAASC,EACX,CAAEC,KAAM,YAAaC,MAAO,mBAE5B,CAAED,KAAM,WAAYC,MAAO,mBANfC,IAACJ,EAAeC,EAgGjC,OACEnI,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAEsB,KAC5CpB,EAAAA,EAAAA,KAAA,OAAKD,UAAW,WAAWyJ,EAAYI,QAAQ9J,SAAE0J,EAAYG,SACzD,GAIZ,CACEV,IAAK,SACLvI,MAAO,SACPwI,UAAU,EACVC,OAAS/H,IACPpB,EAAAA,EAAAA,KAAC8D,EAAAA,EAAW,CAACC,OAAQ4E,EAAqBvH,GAAQ4C,KAAK,cAG3D,CACEiF,IAAK,UACLvI,MAAO,UACPwI,UAAU,EACVC,OAAQA,CAACC,EAAaP,KACpBtH,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR1D,KAAK,KACLrD,QAASA,IAAMoI,EAAkBC,GACjCjI,MAAMZ,EAAAA,EAAAA,KAACyH,EAAAA,EAAO,CAAC1H,UAAU,YACzBkB,MAAM,uBAAsBnB,SAC7B,UAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR1D,KAAK,KACLrD,QAASA,IApHQqI,KAEzB9C,EAAS,iBAAiB8C,EAAQjF,QAClC4C,QAAQC,IAAI,gBAAiBoC,EAAQ,EAiHdiB,CAAkBjB,GACjCjI,MAAMZ,EAAAA,EAAAA,KAAC+J,EAAAA,EAAU,CAAChK,UAAU,YAC5BkB,MAAM,eAAcnB,SACrB,UAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,SACR1D,KAAK,KACLrD,QAASA,IAvHUqI,KAC3BL,EAAmBK,GACnBH,GAAqB,EAAK,EAqHHsB,CAAoBnB,GACnCjI,MAAMZ,EAAAA,EAAAA,KAACiK,EAAAA,EAAS,CAAClK,UAAU,YAC3BkB,MAAM,iBAAgBnB,SACvB,gBAQT,OAAwB,IAApBmI,EAAS9C,QAETnF,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,WACNsC,YAAY,oCAAmCzD,UAE/CyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,EACpCE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAQ,CAACvJ,UAAU,qCACpBC,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,uBACvDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,mDAG1CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR3G,MAAMZ,EAAAA,EAAAA,KAACkK,EAAAA,EAAQ,CAACnK,UAAU,YAC1BS,QAASA,IAAMuF,EAAS,yCAAyCjG,SAClE,wBAUTyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACsD,EAAAA,EAAa,CACZrC,MAAM,WACNsC,YAAa,GAAG0E,EAAS9C,2CAA2CrF,UAEpEE,EAAAA,EAAAA,KAACmK,EAAAA,EAAS,CACRnB,QAASA,EACT9H,KAAM+G,EACNmC,WAAYxB,EACZyB,YAAY,EACZC,SAAU,GACVC,aAAa,oBACbxK,UAAU,eAKbwI,IACCvI,EAAAA,EAAAA,KAAC2H,EAAAA,EAAK,CACJC,OAAQa,EACRZ,QAASA,IAAMa,GAAqB,GACpCzH,MAAM,iBACN4C,KAAK,KACLiE,QACEvG,EAAAA,EAAAA,MAAAwG,EAAAA,SAAA,CAAAjI,SAAA,EACEE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR/G,QAASA,IAAMkI,GAAqB,GAAO5I,SAC5C,YAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,SACR/G,QAzLegK,UAC3B,GAAKjC,EAEL,IAEEF,EAAY,YAAYE,EAAgB3E,8BACxC8E,GAAqB,GACrBF,EAAmB,MACJ,OAAfN,QAAe,IAAfA,GAAAA,GACF,CAAE,MAAOuC,GACPnC,EAAU,2BACZ,GA8K0CxI,SAC/B,sBAIJA,UAEDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,CAAC,oCACHyI,EAAgB3E,KAAK,0CAIzD,E,4CC3QV,MAqBA,EArBkD/D,IAK3C,IAL4C,MACjDoB,EAAK,YACLsC,EAAW,SACXzD,EAAQ,UACRC,EAAY,IACbF,EACC,OACE0B,EAAAA,EAAAA,MAACD,EAAAA,EAAI,CAACvB,UAAWA,EAAUD,SAAA,EACzByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAEmB,IAC5DsC,IACCvD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAEyD,QAGzDvD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mBAAkBD,SAC9BA,MAEE,EC8BX,EAzCsDD,IAK/C,IALgD,MACrDoB,EAAK,OACLyJ,EAAM,KACNxJ,EAAI,MACJ0I,EAAQ,WACT/J,EACC,MAAM8K,EAAWC,KAAKC,OAAO3J,GAE7B,OACElB,EAAAA,EAAAA,KAACsB,EAAAA,EAAI,CAACL,MAAOA,EAAMnB,UACjBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAC5ByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SACvC4K,EAAOtK,KAAI,CAACM,EAAOsB,KAClBhC,EAAAA,EAAAA,KAAA,OAAiBD,UAAU,wBAAuBD,SAAEY,GAA1CsB,QAGdhC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SACnCoB,EAAKd,KAAI,CAACgB,EAAOY,KAChB,MAAM8I,EAASH,EAAW,EAAQvJ,EAAQuJ,EAAY,IAAxB,IAAiC,KAC/D,OACE3K,EAAAA,EAAAA,KAAA,OAAiBD,UAAU,cAAaD,UACtCE,EAAAA,EAAAA,KAAA,OACED,UAAU,kDACVgL,MAAO,CACLD,SACAE,gBAAiBpB,MALb5H,EAQJ,cAOb,ECsBX,EArDsDnC,IAG/C,IAHgD,MACrDoB,EAAK,KACLC,GACDrB,EACC,MAAMoL,EAAU/J,EAAKgK,SAAS,GAG9B,IAAKD,IAAYA,EAAQ/J,MAAgC,IAAxB+J,EAAQ/J,KAAKiE,OAC5C,OACEnF,EAAAA,EAAAA,KAACsB,EAAAA,EAAI,CAACL,MAAOA,EAAMnB,UACjBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAC,0BAQnD,MAAM4B,EAAQuJ,EAAQ/J,KAAKiK,QAAO,CAACC,EAAKhK,IAAUgK,EAAMhK,GAAO,GAE/D,OACEpB,EAAAA,EAAAA,KAACsB,EAAAA,EAAI,CAACL,MAAOA,EAAMnB,UACjBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8BAA6BD,SAAA,EAE1CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,SAClBoB,EAAKwJ,OAAOtK,KAAI,CAACM,EAAOsB,KAAK,IAAAqJ,EAAA,OAC5B9J,EAAAA,EAAAA,MAAA,OAAiBxB,UAAU,yBAAwBD,SAAA,EACjDE,EAAAA,EAAAA,KAAA,OACED,UAAU,uBACVgL,MAAO,CAAEC,iBAAwC,QAAvBK,EAAAJ,EAAQD,uBAAe,IAAAK,OAAA,EAAvBA,EAA0BrJ,KAAU,WAEhET,EAAAA,EAAAA,MAAA,QAAMxB,UAAU,wBAAuBD,SAAA,CACpCY,EAAM,KAAGuK,EAAQ/J,KAAKc,IAAU,EAAE,OAAKiJ,EAAQ/J,KAAKc,IAAU,GAAKN,EAAQ,KAAKG,QAAQ,GAAG,UANtFG,EAQJ,OAKVhC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,0CAAyCD,UACtDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,cAAaD,SAAA,EAC1ByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wCAAuCD,SAAA,CAAC,UAAQ4B,MAC/D1B,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SAAC,iCAK1C,ECoEX,EArH4DD,IAAgD,IAA/C,aAAEyL,EAAczF,WAAYC,GAAajG,EAEpG,MAAMiC,EAAoB,CACxB,CACEb,MAAO,eACPG,MAAOkK,EAAaC,YACpB3K,MAAMZ,EAAAA,EAAAA,KAACwL,EAAAA,EAAgB,CAACzL,UAAU,2BAEpC,CACEkB,MAAO,gBACPG,OAAOmI,EAAAA,EAAAA,IAAe+B,EAAaG,cACnC7K,MAAMZ,EAAAA,EAAAA,KAACoC,EAAAA,EAAkB,CAACrC,UAAU,4BAEtC,CACEkB,MAAO,WACPG,MAAOkK,EAAaI,aACpB9K,MAAMZ,EAAAA,EAAAA,KAACsJ,EAAAA,EAAQ,CAACvJ,UAAU,8BAMxB4L,EAAmBL,EAAaM,eAAexL,KAAIyL,IAAI,CAC3DnL,MAAOmL,EAAKC,KACZ1K,MAAOyK,EAAKE,WAGRC,EAAmBV,EAAaW,eAAe7L,KAAIyL,IAAI,CAC3DnL,MAAOmL,EAAKK,YACZ9K,MAAOyK,EAAKE,WAGRI,EAAkBb,EAAac,YAAYhM,KAAIyL,IAAI,CACvDnL,MAAOmL,EAAKC,KACZ1K,MAAOyK,EAAKQ,WAGd,OACE9K,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EAExBE,EAAAA,EAAAA,KAACsM,EAAAA,EAAc,CAACxK,QAASA,KAGzBP,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wCAAuCD,SAAA,EACpDE,EAAAA,EAAAA,KAACuM,EAAY,CAACtL,MAAM,kBAAkBsC,YAAY,+BAA8BzD,UAC9EE,EAAAA,EAAAA,KAACwM,EAAc,CACbtL,KAAMyK,EAAiBvL,KAAIyL,GAAQA,EAAKzK,QACxCsJ,OAAQiB,EAAiBvL,KAAIyL,GAAQA,EAAKnL,QAC1CO,MAAM,kBACN2I,MAAM,eAIV5J,EAAAA,EAAAA,KAACuM,EAAY,CAACtL,MAAM,eAAesC,YAAY,6BAA4BzD,UACzEE,EAAAA,EAAAA,KAACwM,EAAc,CACbtL,KAAMiL,EAAgB/L,KAAIyL,GAAQA,EAAKzK,QACvCsJ,OAAQyB,EAAgB/L,KAAIyL,GAAQA,EAAKnL,QACzCO,MAAM,eACN2I,MAAM,kBAKZrI,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wCAAuCD,SAAA,EACpDE,EAAAA,EAAAA,KAACuM,EAAY,CAACtL,MAAM,mBAAmBsC,YAAY,kCAAiCzD,UAClFE,EAAAA,EAAAA,KAACyM,EAAc,CACbxL,MAAM,mBACNC,KAAM,CACJwJ,OAAQsB,EAAiB5L,KAAIyL,GAAQA,EAAKnL,QAC1CwK,SAAU,CAAC,CACThK,KAAM8K,EAAiB5L,KAAIyL,GAAQA,EAAKzK,QACxC4J,gBAAiB,CAAC,UAAW,UAAW,UAAW,UAAW,mBAMtEhL,EAAAA,EAAAA,KAACuM,EAAY,CAACtL,MAAM,iBAAiBsC,YAAY,8BAA6BzD,UAC5EE,EAAAA,EAAAA,KAACyM,EAAc,CACbxL,MAAM,iBACNC,KAAM,CACJwJ,OAAQY,EAAaoB,cAActM,KAAIyL,GAAQA,EAAKzG,WACpD8F,SAAU,CAAC,CACThK,KAAMoK,EAAaoB,cAActM,KAAIyL,GAAQA,EAAKc,UAClD3B,gBAAiB,CAAC,UAAW,UAAW,UAAW,UAAW,uBA6BpE,E,eCzGV,MAgXA,GAhXsC4B,KACpC,MAAQrM,GAAIsF,IAAegH,EAAAA,EAAAA,KACrB1E,GAAWC,EAAAA,EAAAA,OACX,UAAEE,EAAS,YAAED,IAAgBrC,EAAAA,EAAAA,KAG7B8G,GAAeC,EAAAA,EAAAA,QAAOzE,GACtB0E,GAAiBD,EAAAA,EAAAA,QAAO1E,IAG9B4E,EAAAA,EAAAA,YAAU,KACRH,EAAaI,QAAU5E,EACvB0E,EAAeE,QAAU7E,CAAW,GACnC,CAACC,EAAWD,IAGf,MAAM8E,GAAgBJ,EAAAA,EAAAA,QAAOlH,IAC7BoH,EAAAA,EAAAA,YAAU,KACRE,EAAcD,QAAUrH,CAAU,GACjC,CAACA,IAGJ,MAAO3F,EAAWkN,IAAgBjH,EAAAA,EAAAA,UAA8D,aACzF9C,EAAUgK,IAAelH,EAAAA,EAAAA,UAA0B,OACnDmH,EAAkBC,IAAuBpH,EAAAA,EAAAA,UAA4B,KACrEqH,EAAmBC,IAAwBtH,EAAAA,EAAAA,UAA6B,KACxEuH,EAAmBC,IAAwBxH,EAAAA,EAAAA,UAAuC,OAClFyH,EAAWC,IAAgB1H,EAAAA,EAAAA,WAAS,IACpCsE,EAAOqD,IAAY3H,EAAAA,EAAAA,UAAwB,OAC3CsC,EAAmBC,IAAwBvC,EAAAA,EAAAA,WAAS,IACpD4H,EAAYC,IAAiB7H,EAAAA,EAAAA,WAAS,IACtC8H,EAAgBC,IAAqB/H,EAAAA,EAAAA,WAAS,IAC9CgI,EAAWC,IAAgBjI,EAAAA,EAAAA,WAAS,IAGrC,gBACJkI,EAAe,oBACfC,EAAmB,qBACnBC,EAAoB,qBACpBC,EAAoB,eACpBC,EAAc,YACdC,EAAW,cACXC,EACAf,UAAWgB,IACTC,EAAAA,GAAAA,KAKEC,IAAoB/B,EAAAA,EAAAA,SAAOvC,UAC/B,MAAMuE,EAAoB5B,EAAcD,QAExC,IAAK6B,EAGH,OAFAjB,EAAS,gCACTD,GAAa,GAIf,IACEA,GAAa,GACbC,EAAS,MAGT,MAAMxC,QAAqB+C,EAAgBU,GAC3C1B,EAAY/B,GAGZ,MAAOrD,EAAUrC,EAAWoJ,SAAmBC,QAAQC,IAAI,CACzDZ,EAAoBS,GACpBR,EAAqBQ,GACrBP,EAAqBO,KAGvBxB,EAAoBtF,GACpBwF,EAAqB7H,GACrB+H,EAAqBqB,EAEvB,CAAE,MAAOvE,GACPjE,QAAQiE,MAAM,gCAAiCA,GAC/C,MAAM0E,EAAe1E,aAAiB2E,MAAQ3E,EAAM4E,QAAU,gCAC9DvB,EAASqB,GACTrC,EAAaI,QAAQ,+BACvB,CAAC,QACCW,GAAa,EACf,MAIFZ,EAAAA,EAAAA,YAAU,KACR6B,GAAkB5B,SAAS,GAC1B,CAACrH,IA8EJ,OAAI+H,GAAagB,GAEb5O,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gDAA+CD,UAC5DE,EAAAA,EAAAA,KAACsP,EAAAA,EAAc,CAACzL,KAAK,SAMvB4G,IAAUpH,GAEV9B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACuP,EAAAA,EAAU,CACTtO,MAAM,mBACNsC,YAAY,qBACZiM,YAAa,CACX,CAAE9O,MAAO,YAAa+O,KAAM3G,EAAAA,EAAO4G,WACnC,CAAEhP,MAAO,eAGbV,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oBAAmBD,UAChCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeD,SAAE2K,GAAS,6BAO7ClJ,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACuP,EAAAA,EAAU,CACTtO,MAAO,aAAaoC,EAASO,OAC7BL,YAAY,gDACZiM,YAAa,CACX,CAAE9O,MAAO,YAAa+O,KAAM3G,EAAAA,EAAO4G,WACnC,CAAEhP,MAAO2C,EAASO,OAEpB+L,SACEpO,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,aAAYD,SAAA,CACJ,WAApBuD,EAASU,QACR/D,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR1D,KAAK,KACLrD,QArEcgK,UAC1B,GAAKnH,EAEL,IACE+K,GAAa,SAEPO,EAActL,EAAS9C,IAE7ByM,EAAeE,QAAQ,aAAa7J,EAASO,8CAGvCkL,GAAkB5B,SAE1B,CAAE,MAAOzC,GACPjE,QAAQiE,MAAM,4BAA6BA,GAC3CqC,EAAaI,QAAQ,2BACvB,CAAC,QACCkB,GAAa,EACf,GAoDYxN,MAAMZ,EAAAA,EAAAA,KAACqF,EAAAA,EAAe,CAACtF,UAAU,YACjCU,SAAUmN,GAAaO,GAAaJ,EACpC6B,QAASzB,EAAUrO,SACpB,oBAIDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,YACR1D,KAAK,KACLrD,QAASA,IAAM0N,GAAkB,GACjCtN,MAAMZ,EAAAA,EAAAA,KAACkD,EAAY,CAACnD,UAAU,YAC9BU,SAAUmN,GAAaO,GAAaJ,EAAWjO,SAChD,kBAIHE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,SACR1D,KAAK,KACLrD,QAASA,IAAMkI,GAAqB,GACpC9H,MAAMZ,EAAAA,EAAAA,KAACiK,EAAAA,EAAS,CAAClK,UAAU,YAC3BU,SAAUmN,GAAaG,GAAcI,EAAUrO,SAChD,0BAOPE,EAAAA,EAAAA,KAAC6P,EAAAA,EAAI,CACH5P,KAAM,CACJ,CAAEM,GAAI,WAAYG,MAAO,wBACzB,CAAEH,GAAI,YAAaG,MAAO,0BAC1B,CAAEH,GAAI,WAAYG,MAAO,YACzB,CAAEH,GAAI,YAAaG,MAAO,cAE5BR,UAAWA,EACXC,SAtFmB2P,IACvB1C,EAAa0C,EAA6D,IAwFzD,aAAd5P,IACCF,EAAAA,EAAAA,KAAC+P,EAAoB,CAAC1M,SAAUA,IAInB,cAAdnD,IACCF,EAAAA,EAAAA,KAACgQ,EAAiB,CAChBpK,UAAW4H,EACX3H,WAAYxC,EAAS9C,KAIV,aAAdL,IACCF,EAAAA,EAAAA,KAACiQ,EAAgB,CACfhI,SAAUqF,EACVzH,WAAYxC,EAAS9C,GACrB2H,gBAAiB4G,GAAkB5B,UAIxB,cAAdhN,GAA6BwN,IAC5B1N,EAAAA,EAAAA,KAACkQ,EAAiB,CAChB5E,aAAcoC,EACd7H,WAAYxC,EAAS9C,MAKzBP,EAAAA,EAAAA,KAAC2H,EAAAA,EAAK,CACJC,OAAQqG,EACRpG,QAASA,IAAMqG,GAAkB,GACjCjN,MAAM,eACN4C,KAAK,KACLiE,QACEvG,EAAAA,EAAAA,MAAAwG,EAAAA,SAAA,CAAAjI,SAAA,EACEE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR/G,QAASA,IAAM0N,GAAkB,GACjCzN,SAAU0N,EAAUrO,SACrB,YAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,YACR/G,QAlLcgK,UACxB,GAAKnH,EAEL,IACE+K,GAAa,SAEPM,EAAYrL,EAAS9C,IAE3ByM,EAAeE,QAAQ,aAAa7J,EAASO,sCAC7CsK,GAAkB,SAGZY,GAAkB5B,SAE1B,CAAE,MAAOzC,GACPjE,QAAQiE,MAAM,0BAA2BA,GACzCqC,EAAaI,QAAQ,yBACvB,CAAC,QACCkB,GAAa,EACf,GAgKUwB,QAASzB,EACTvN,MAAMZ,EAAAA,EAAAA,KAACkD,EAAY,CAACnD,UAAU,YAAaD,SAC5C,oBAIJA,UAEDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,EACpCyB,EAAAA,EAAAA,MAAA,KAAGxB,UAAU,OAAMD,SAAA,CAAC,iCACWyB,EAAAA,EAAAA,MAAA,UAAAzB,SAAA,CAAQ,IAAEuD,EAASO,KAAK,OAAU,QAEjE5D,EAAAA,EAAAA,KAAA,KAAGD,UAAU,8BAA6BD,SAAC,uBAG3CyB,EAAAA,EAAAA,MAAA,MAAIxB,UAAU,6CAA4CD,SAAA,EACxDE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,8CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,4CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,2CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,gDAENE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,qBAAoBD,SAAC,2GAOtCE,EAAAA,EAAAA,KAAC2H,EAAAA,EAAK,CACJC,OAAQa,EACRZ,QAASA,IAAMa,GAAqB,GACpCzH,MAAM,kBACN4C,KAAK,KACLiE,QACEvG,EAAAA,EAAAA,MAAAwG,EAAAA,SAAA,CAAAjI,SAAA,EACEE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,UACR/G,QAASA,IAAMkI,GAAqB,GACpCjI,SAAUsN,EAAWjO,SACtB,YAGDE,EAAAA,EAAAA,KAACsH,EAAAA,EAAM,CACLC,QAAQ,SACR/G,QAtPiBgK,UAC3B,GAAKnH,EAEL,IACE2K,GAAc,SAERS,EAAepL,EAAS9C,IAE9ByM,EAAeE,QAAQ,aAAa7J,EAASO,uCAC7C8E,GAAqB,GAGrBP,EAASW,EAAAA,EAAO4G,UAElB,CAAE,MAAOjF,GACPjE,QAAQiE,MAAM,2BAA4BA,GAC1CqC,EAAaI,QAAQ,4BACvB,CAAC,QACCc,GAAc,EAChB,GAoOU4B,QAAS7B,EACTnN,MAAMZ,EAAAA,EAAAA,KAACiK,EAAAA,EAAS,CAAClK,UAAU,YAAaD,SACzC,uBAIJA,UAEDyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wBAAuBD,SAAA,EACpCyB,EAAAA,EAAAA,MAAA,KAAGxB,UAAU,OAAMD,SAAA,CAAC,oCACcyB,EAAAA,EAAAA,MAAA,UAAAzB,SAAA,CAAQ,IAAEuD,EAASO,KAAK,OAAU,QAEpE5D,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2BAA0BD,SAAC,+DAGxCyB,EAAAA,EAAAA,MAAA,MAAIxB,UAAU,0CAAyCD,SAAA,EACrDE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,8BACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,uCACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,0CAIN,C,uFClXV,MAAMyP,EAAwC1P,IAOvC,IAPwC,MAC7CoB,EAAK,YACLsC,EAAW,QACXoM,EAAO,YACPH,EAAW,UACXzP,EAAY,GAAE,OACdoQ,GACDtQ,EACC,OACE0B,EAAAA,EAAAA,MAAA,OACExB,UAAW,QAAQA,IACnB,cAAaoQ,EAAOrQ,SAAA,CAGnB0P,GAAeA,EAAYrK,OAAS,IACnCnF,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAY,aAAW,aAAYD,UAChDyB,EAAAA,EAAAA,MAAA,MAAIxB,UAAU,oDAAmDD,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAACoQ,EAAAA,GAAI,CACHC,GAAG,IACHtQ,UAAU,uCACV,aAAW,OAAMD,UAEjBE,EAAAA,EAAAA,KAACsQ,EAAAA,EAAQ,CAACvQ,UAAU,gBAIvByP,EAAYpP,KAAI,CAACyL,EAAM7J,KACtBT,EAAAA,EAAAA,MAAA,MAAgBxB,UAAU,oBAAmBD,SAAA,EAC3CE,EAAAA,EAAAA,KAACuQ,EAAAA,EAAgB,CAACxQ,UAAU,+BAC3B8L,EAAK4D,MAAQzN,EAAQwN,EAAYrK,OAAS,GACzCnF,EAAAA,EAAAA,KAACoQ,EAAAA,GAAI,CACHC,GAAIxE,EAAK4D,KACT1P,UAAU,qBAAoBD,SAE7B+L,EAAKnL,SAGRV,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAE+L,EAAKnL,UAV7CsB,WAmBjBT,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,8EAA6ED,SAAA,EAC1FyB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,mCAAkCD,SAAEmB,IACjDsC,GAAsC,kBAAhBA,GACrBvD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAEyD,IAE3CA,KAIHoM,IACC3P,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SAC/C6P,SAIH,EAIV,GAAea,EAAAA,EAAAA,MAAKjB,E,gDC3FpB,SAASxF,EAAUlK,EAIhBwC,GAAQ,IAJS,MAClBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBkJ,E,qDCf3C,MAAM3C,EAAa,SAACqJ,GAA0E,IAAtDC,EAAmCC,UAAAxL,OAAA,QAAAxD,IAAAgP,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKF,EAAY,MAAO,IAExB,IACE,MAAM3E,EAAO,IAAI8E,KAAKH,GAGhBI,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACFN,GAGL,OAAO,IAAIO,KAAKC,eAAe,QAASL,GAAgBM,OAAOrF,EACjE,CAAE,MAAOrB,GAEP,OADAjE,QAAQiE,MAAM,yBAA0BA,GACjCgG,CACT,CACF,EAkBalH,EAAiB,SAC5BwC,GAGY,IAFZqF,EAAgBT,UAAAxL,OAAA,QAAAxD,IAAAgP,UAAA,GAAAA,UAAA,GAAG,MACnBU,EAAcV,UAAAxL,OAAA,QAAAxD,IAAAgP,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAIM,KAAKK,aAAaD,EAAQ,CACnCtG,MAAO,WACPqG,WACAG,sBAAuB,EACvBC,sBAAuB,IACtBL,OAAOpF,EACZ,CAAE,MAAOtB,GAEP,OADAjE,QAAQiE,MAAM,6BAA8BA,GACrC,GAAG2G,KAAYrF,EAAOlK,QAAQ,IACvC,CACF,EAkDa+E,EAAkB6K,IAC7B,GAAc,IAAVA,EAAa,MAAO,UAExB,MAEMC,EAAI9G,KAAK+G,MAAM/G,KAAKnE,IAAIgL,GAAS7G,KAAKnE,IAFlC,OAIV,MAAO,GAAGvE,YAAYuP,EAAQ7G,KAAKgH,IAJzB,KAIgCF,IAAI7P,QAAQ,OAHxC,CAAC,QAAS,KAAM,KAAM,KAAM,MAGyB6P,IAAI,C,gDCtHzE,SAASnN,EAAQ1E,EAIdwC,GAAQ,IAJO,MAChBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,4JAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB0D,E,yGCPlD,MAiDA,EAjDgD1E,IAIzC,IAJ0C,OAC/CkE,EACAC,KAAM6N,EAAQ,OAAM,UACpB9R,EAAY,IACbF,EAEC,IAAKkE,EACH,OACE/D,EAAAA,EAAAA,KAAA,QAAMD,UAAW,qGAAqGA,IAAYD,SAAC,YAMvI,MAAMgS,EAAY/N,EAAOgO,cACzB,IAAItQ,EAAa,GACbb,EAAO,KAGO,WAAdkR,GAAwC,aAAdA,GAA0C,cAAdA,GACxDrQ,EAAa,8BACbb,GAAOZ,EAAAA,EAAAA,KAACqF,EAAAA,EAAe,CAACtF,UAAU,kBACX,YAAd+R,GAAyC,eAAdA,GACpCrQ,EAAa,4BACbb,GAAOZ,EAAAA,EAAAA,KAACsF,EAAAA,EAAS,CAACvF,UAAU,kBACL,WAAd+R,GAAwC,aAAdA,GACnCrQ,EAAa,0BACbb,GAAOZ,EAAAA,EAAAA,KAACuF,EAAAA,EAAW,CAACxF,UAAU,kBACP,YAAd+R,GACTrQ,EAAa,gCACbb,GAAOZ,EAAAA,EAAAA,KAACgS,EAAAA,EAAS,CAACjS,UAAU,kBACL,YAAd+R,GACTrQ,EAAa,gCACbb,GAAOZ,EAAAA,EAAAA,KAACiS,EAAAA,EAAqB,CAAClS,UAAU,kBAExC0B,EAAa,4BAIf,MAAMyQ,EAAkBnO,EAASA,EAAOG,OAAO,GAAGC,cAAgBJ,EAAOK,MAAM,GAAK,UAEpF,OACE7C,EAAAA,EAAAA,MAAA,QAAMxB,UAAW,2EAA2E0B,KAAc1B,IAAYD,SAAA,CACnHc,EACAsR,IACI,C,gDC7DX,SAASxK,EAAiB7H,EAIvBwC,GAAQ,IAJgB,MACzBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,iHAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB6G,E,uDCflD,MAaA,EAb8C7H,IAIvC,IAJwC,MAC7Ca,EAAK,MACLU,EAAK,UACLrB,EAAY,IACbF,EACC,OACE0B,EAAAA,EAAAA,MAAA,OAAKxB,UAAW,wDAAwDA,IAAYD,SAAA,EAClFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEY,KACnDV,EAAAA,EAAAA,KAAA,MAAID,UAAU,mDAAkDD,SAAEsB,MAC9D,C,gDCjBV,SAAS4Q,EAASnS,EAIfwC,GAAQ,IAJQ,MACjBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBmR,E,gDCvBlD,SAASzB,EAAgB1Q,EAItBwC,GAAQ,IAJe,MACxBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB0P,E,uDCdlD,MAqBA,EArBoD1Q,IAK7C,IAL8C,MACnDoB,EAAK,YACLsC,EAAW,SACXzD,EAAQ,UACRC,EAAY,IACbF,EACC,OACE0B,EAAAA,EAAAA,MAAA,OAAKxB,UAAW,iDAAiDA,IAAYD,SAAA,EAC3EyB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAEmB,IAC5DsC,IACCvD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAEyD,QAGzDvD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,SACtCA,MAEC,C,gDC1BV,SAASqH,EAAYtH,EAIlBwC,GAAQ,IAJW,MACpBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,sOAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBsG,E,6ECMlD,MAAMQ,EAA8B9H,IAiB7B,IAjB8B,OACnC+H,EAAM,QACNC,EAAO,MACP5G,EAAK,SACLnB,EAAQ,KACR+D,EAAO,KAAI,OACXiE,EAAM,WACNqK,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfvS,EAAY,GAAE,cACdwS,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtBvC,GACDtQ,EACC,MAAM8S,GAAW5F,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAE,EAAAA,EAAAA,YAAU,KACR,MAAM2F,EAAgBC,IAChBV,GAAwB,WAAVU,EAAE5J,KAClBpB,GACF,EASF,OANID,IACFrB,SAASuM,iBAAiB,UAAWF,GAErCrM,SAASwM,KAAKhI,MAAMiI,SAAW,UAG1B,KACLzM,SAAS0M,oBAAoB,UAAWL,GACxCrM,SAASwM,KAAKhI,MAAMiI,SAAW,MAAM,CACtC,GACA,CAACpL,EAAQC,EAASsK,KAGrBlF,EAAAA,EAAAA,YAAU,KACR,IAAKrF,IAAW+K,EAASzF,QAAS,OAElC,MAAMgG,EAAoBP,EAASzF,QAAQiG,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkB/N,OAAc,OAEpC,MAAMiO,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkB/N,OAAS,GAE3DmO,EAAgBT,IACN,QAAVA,EAAE5J,MAEF4J,EAAEU,SACAhN,SAASiN,gBAAkBJ,IAC7BC,EAAYI,QACZZ,EAAEa,kBAGAnN,SAASiN,gBAAkBH,IAC7BD,EAAaK,QACbZ,EAAEa,kBAEN,EAMF,OAHAnN,SAASuM,iBAAiB,UAAWQ,GACrCF,EAAaK,QAEN,KACLlN,SAAS0M,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAAC1L,KAECA,EAAQ,OAAO,KAGpB,MAUM+L,GACJpS,EAAAA,EAAAA,MAACqS,EAAAA,SAAQ,CAAA9T,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACED,UAAW,gEAAgE2S,IAC3ElS,QAAS4R,EAAuBvK,OAAUlG,EAC1C,cAAa,GAAGwO,gBAIlBnQ,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UACjDE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yBAAyBuS,EAAW,SAAW,yCAAyCxS,UACtGyB,EAAAA,EAAAA,MAAA,OACEuB,IAAK6P,EACL5S,UAAW,GAxBD,CAClB8T,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BrQ,2GAA8G9D,IACxIS,QAAUqS,GAAMA,EAAEsB,kBAClB,cAAahE,EAAOrQ,SAAA,EAGpByB,EAAAA,EAAAA,MAAA,OAAKxB,UAAW,wEAAwEyS,IAAkB1S,SAAA,CACtF,kBAAVmB,GACNjB,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAEmB,IAErDA,EAEDoR,IACCrS,EAAAA,EAAAA,KAAA,UACEgE,KAAK,SACLjE,UAAU,wGACVS,QAASqH,EACT,aAAW,cACX,cAAa,GAAGsI,iBAAsBrQ,UAEtCE,EAAAA,EAAAA,KAACoU,EAAAA,EAAS,CAACrU,UAAU,kBAM3BC,EAAAA,EAAAA,KAAA,OAAKD,UAAW,aAAawS,IAAgBzS,SAC1CA,IAIFgI,IACC9H,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4EAA4E0S,IAAkB3S,SAC3GgI,cAUf,OAAOuM,EAAAA,EAAAA,cAAaV,EAAcpN,SAASwM,KAAK,EAGlD,GAAevC,EAAAA,EAAAA,MAAK7I,E,gDClLpB,SAAS2B,EAAQzJ,EAIdwC,GAAQ,IAJO,MAChBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,wFAEP,CACA,MACA,EADiCpC,EAAAA,WAAiByI,E,gDCvBlD,SAASY,EAAQrK,EAIdwC,GAAQ,IAJO,MAChBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,2BAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBqJ,E,gDCvBlD,SAASzF,EAAY5E,EAIlBwC,GAAQ,IAJW,MACpBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB4D,E,gDCvBlD,SAASwF,EAASpK,EAIfwC,GAAQ,IAJQ,MACjBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCpC,EAAAA,WAAiBoJ,E,gDCvBlD,SAASxC,EAAO5H,EAIbwC,GAAQ,IAJM,MACfpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYpC,EAAAA,cAAoB,OAAQ,CAC3CkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB4G,E,gDC3BlD,SAAS7C,EAAS/E,EAIfwC,GAAQ,IAJQ,MACjBpB,EAAK,QACLqB,KACGvB,GACJlB,EACC,OAAoBgB,EAAAA,cAAoB,MAAO0B,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKT,EACL,kBAAmBC,GAClBvB,GAAQE,EAAqBJ,EAAAA,cAAoB,QAAS,CAC3DN,GAAI+B,GACHrB,GAAS,KAAmBJ,EAAAA,cAAoB,OAAQ,CACzDkC,cAAe,QACfC,eAAgB,QAChBC,EAAG,sWAEP,CACA,MACA,EADiCpC,EAAAA,WAAiB+D,E", "sources": ["components/common/DetailList.tsx", "components/common/Tabs.tsx", "components/common/MetricCard.tsx", "components/common/MetricsSection.tsx", "../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js", "../node_modules/@heroicons/react/24/outline/esm/NoSymbolIcon.js", "../node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js", "../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js", "features/suppliers/components/SupplierPersonalInfo.tsx", "../node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js", "../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js", "features/suppliers/components/SupplierDocuments.tsx", "features/suppliers/components/SupplierProducts.tsx", "components/analytics/ChartSection.tsx", "components/common/SimpleBarChart.tsx", "components/common/SimplePieChart.tsx", "features/suppliers/components/SupplierAnalytics.tsx", "pages/SupplierProfilePage.tsx", "components/layout/PageHeader.tsx", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "utils/formatters.ts", "../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "components/common/StatusBadge.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js", "components/common/DetailItem.tsx", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "components/common/DetailSection.tsx", "../node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/CubeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js"], "sourcesContent": ["import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailListProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailList: React.FC<DetailListProps> = ({\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <dl className={`sm:divide-y sm:divide-gray-200 ${className}`}>\r\n      {children}\r\n    </dl>\r\n  );\r\n};\r\n\r\nexport default DetailList;", "// src/components/common/Tabs.tsx\r\nimport React from 'react';\r\n\r\nexport interface Tab {\r\n  id: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface TabsProps {\r\n  tabs: Tab[];\r\n  activeTab: string;\r\n  onChange: (tabId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nconst Tabs: React.FC<TabsProps> = ({\r\n  tabs,\r\n  activeTab,\r\n  onChange,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`border-b border-gray-200 ${className}`}>\r\n      <nav className=\"-mb-px flex space-x-8\">\r\n        {tabs.map((tab) => {\r\n          const isActive = tab.id === activeTab;\r\n          \r\n          return (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => !tab.disabled && onChange(tab.id)}\r\n              className={`\r\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\r\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\r\n                ${isActive\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}\r\n                ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\r\n              `}\r\n              disabled={tab.disabled}\r\n            >\r\n              {tab.label}\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Tabs;", "/**\n * Metric Card Component\n *\n * A simple metric card component for displaying key metrics with icons.\n * This is a replacement for the analytics MetricCard to avoid dependencies.\n */\n\nimport React from 'react';\nimport Card from './Card';\n\ninterface MetricData {\n  total: number;\n  growth?: number;\n}\n\ninterface MetricCardProps {\n  title: string;\n  data: MetricData;\n  icon?: React.ReactNode;\n  formatValue?: (value: number) => string;\n}\n\n// Helper function to get appropriate background class for icon\nconst getIconBackgroundClass = (icon: React.ReactNode): string => {\n  if (!React.isValidElement(icon)) return 'bg-primary bg-opacity-10';\n\n  // Get the className from the icon props\n  const className = icon.props.className || '';\n  \n  // Extract color from className (e.g., \"text-blue-500\" -> \"blue\")\n  const colorMatch = className.match(/text-([a-z]+)-/);\n  if (colorMatch) {\n    const color = colorMatch[1];\n    return `bg-${color}-50`;\n  }\n  \n  return 'bg-primary bg-opacity-10';\n};\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  data,\n  icon,\n  formatValue = (value) => value.toString()\n}) => {\n  return (\n    <Card>\n      <div className=\"flex items-center\">\n        <div className={`p-3 rounded-full ${getIconBackgroundClass(icon)}`}>\n          {/* Ensure consistent icon styling while preserving color */}\n          {React.isValidElement(icon) ? (\n            (() => {\n              const iconElement = icon as React.ReactElement;\n              const existingClassName = iconElement.props.className || '';\n              const colorMatch = existingClassName.match(/text-[a-z0-9-]+/);\n              const colorClass = colorMatch ? colorMatch[0] : 'text-gray-600';\n              const sizeClass = 'h-6 w-6';\n              \n              return React.cloneElement(iconElement, {\n                className: `${sizeClass} ${colorClass}`\n              });\n            })()\n          ) : (\n            icon\n          )}\n        </div>\n        <div className=\"ml-4 flex-1\">\n          <p className=\"text-sm font-medium text-gray-500\">{title}</p>\n          <div className=\"flex items-baseline\">\n            <p className=\"text-2xl font-semibold text-gray-900\">\n              {formatValue(data.total)}\n            </p>\n            {data.growth !== undefined && (\n              <p className={`ml-2 flex items-baseline text-sm font-semibold ${\n                data.growth >= 0 ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {data.growth >= 0 ? '+' : ''}{data.growth.toFixed(1)}%\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default MetricCard;\n", "/**\n * Metrics Section Component\n *\n * A simple metrics section component for displaying multiple metrics.\n * This is a replacement for the analytics MetricsSection to avoid dependencies.\n */\n\nimport React from 'react';\nimport MetricCard from './MetricCard';\n\nexport interface Metric {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon?: React.ReactNode;\n}\n\ninterface MetricsSectionProps {\n  metrics: Metric[];\n  className?: string;\n}\n\nconst MetricsSection: React.FC<MetricsSectionProps> = ({\n  metrics,\n  className = ''\n}) => {\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      {metrics.map((metric, index) => (\n        <MetricCard\n          key={index}\n          title={metric.title}\n          data={{ \n            total: typeof metric.value === 'string' ? parseFloat(metric.value) || 0 : metric.value, \n            growth: metric.change || 0 \n          }}\n          icon={metric.icon}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default MetricsSection;\n", "import * as React from \"react\";\nfunction CurrencyDollarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CurrencyDollarIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction NoSymbolIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(NoSymbolIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction GlobeAltIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(GlobeAltIcon);\nexport default ForwardRef;", "/**\r\n * Supplier Personal Information Component\r\n *\r\n * This component displays supplier's basic details using the same design pattern\r\n * as UserDetails component with DetailSection, DetailList, and DetailItem.\r\n */\r\n\r\nimport React from 'react';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport DetailList from '../../../components/common/DetailList';\r\nimport DetailItem from '../../../components/common/DetailItem';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type { Supplier } from '../types';\r\nimport {\r\n  EnvelopeIcon,\r\n  PhoneIcon,\r\n  MapPinIcon,\r\n  GlobeAltIcon,\r\n  UserIcon,\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface SupplierPersonalInfoProps {\r\n  supplier: Supplier;\r\n}\r\n\r\nconst SupplierPersonalInfo: React.FC<SupplierPersonalInfoProps> = ({ supplier }) => {\r\n  const getVerificationIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'verified':\r\n        return <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />;\r\n      case 'pending':\r\n        return <ClockIcon className=\"w-5 h-5 text-yellow-500\" />;\r\n      case 'rejected':\r\n        return <XCircleIcon className=\"w-5 h-5 text-red-500\" />;\r\n      default:\r\n        return <ClockIcon className=\"w-5 h-5 text-gray-500\" />;\r\n    }\r\n  };\r\n\r\n  const getVerificationText = (status: string) => {\r\n    switch (status) {\r\n      case 'verified':\r\n        return 'Verified';\r\n      case 'pending':\r\n        return 'Pending verification';\r\n      case 'rejected':\r\n        return 'Rejected';\r\n      default:\r\n        return 'Unknown status';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Supplier Header */}\r\n      <DetailSection\r\n        title=\"Supplier Overview\"\r\n        description=\"Basic supplier information and verification status\"\r\n      >\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Avatar\r\n                {...(supplier.logo && { src: supplier.logo })}\r\n                alt={supplier.name}\r\n                name={supplier.name}\r\n                size=\"xl\"\r\n              />\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-gray-900\">{supplier.name}</h3>\r\n                <p className=\"text-sm text-gray-500\">ID: {supplier.id}</p>\r\n                <div className=\"mt-2 flex items-center space-x-3\">\r\n                  <StatusBadge status={supplier.status} type=\"supplier\" />\r\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                    supplier.verificationStatus === 'verified'\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : supplier.verificationStatus === 'pending'\r\n                        ? 'bg-yellow-100 text-yellow-800'\r\n                        : 'bg-red-100 text-red-800'\r\n                  }`}>\r\n                    {supplier.verificationStatus ?\r\n                      supplier.verificationStatus.charAt(0).toUpperCase() + supplier.verificationStatus.slice(1) :\r\n                      'Unknown'\r\n                    }\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n\r\n      {/* Contact Information */}\r\n      <DetailSection\r\n        title=\"Contact Information\"\r\n        description=\"Primary contact details and communication preferences\"\r\n      >\r\n        <DetailList>\r\n          <DetailItem \r\n            label=\"Contact Person\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <UserIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                {supplier.contactPerson}\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Email Address\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                <a \r\n                  href={`mailto:${supplier.email}`}\r\n                  className=\"text-primary hover:text-primary-dark\"\r\n                >\r\n                  {supplier.email}\r\n                </a>\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Phone Number\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <PhoneIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                <a \r\n                  href={`tel:${supplier.phone}`}\r\n                  className=\"text-primary hover:text-primary-dark\"\r\n                >\r\n                  {supplier.phone}\r\n                </a>\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Address\" \r\n            value={\r\n              <div className=\"flex items-start\">\r\n                <MapPinIcon className=\"w-4 h-4 text-gray-400 mr-2 mt-0.5\" />\r\n                <span>{supplier.address}</span>\r\n              </div>\r\n            } \r\n          />\r\n          {supplier.website && (\r\n            <DetailItem \r\n              label=\"Website\" \r\n              value={\r\n                <div className=\"flex items-center\">\r\n                  <GlobeAltIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                  <a \r\n                    href={supplier.website}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"text-primary hover:text-primary-dark\"\r\n                  >\r\n                    {supplier.website}\r\n                  </a>\r\n                </div>\r\n              } \r\n            />\r\n          )}\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      {/* Business Information */}\r\n      <DetailSection\r\n        title=\"Business Information\"\r\n        description=\"Business categories and operational details\"\r\n      >\r\n        <DetailList>\r\n\r\n          <DetailItem\r\n            label=\"Business Categories\"\r\n            value={\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {supplier.categories && supplier.categories.length > 0 ? (\r\n                  supplier.categories.map((category, index) => (\r\n                    <span\r\n                      key={index}\r\n                      className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\r\n                    >\r\n                      {category}\r\n                    </span>\r\n                  ))\r\n                ) : (\r\n                  <span className=\"text-gray-500 text-sm\">No categories assigned</span>\r\n                )}\r\n              </div>\r\n            }\r\n          />\r\n          <DetailItem \r\n            label=\"Account Status\" \r\n            value={<StatusBadge status={supplier.status} type=\"supplier\" />} \r\n          />\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      {/* Verification Status */}\r\n      <DetailSection\r\n        title=\"Verification Status\"\r\n        description=\"Current verification status and history\"\r\n      >\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            {getVerificationIcon(supplier.verificationStatus || 'pending')}\r\n            <div>\r\n              <div className=\"text-sm font-medium text-gray-900\">\r\n                {supplier.verificationStatus === 'verified' ? 'Verified Supplier' : \r\n                 supplier.verificationStatus === 'pending' ? 'Verification Pending' : \r\n                 'Verification Rejected'}\r\n              </div>\r\n              <div className=\"text-sm text-gray-500\">\r\n                {getVerificationText(supplier.verificationStatus || 'pending')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierPersonalInfo;\r\n", "import * as React from \"react\";\nfunction DocumentIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(DocumentIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction DocumentTextIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(DocumentTextIcon);\nexport default ForwardRef;", "/**\r\n * Supplier Documents Component\r\n *\r\n * This component displays verification documents submitted by the supplier\r\n * with document preview/download functionality.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport Button from '../../../components/common/Button';\r\nimport Modal from '../../../components/common/Modal';\r\nimport useNotification from '../../../hooks/useNotification';\r\nimport type { SupplierDocument } from '../types';\r\nimport {\r\n  DocumentTextIcon,\r\n  EyeIcon,\r\n  ArrowDownTrayIcon,\r\n  CalendarIcon,\r\n  DocumentIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { formatDate, formatFileSize } from '../../../utils/formatters';\r\n\r\ninterface SupplierDocumentsProps {\r\n  documents: SupplierDocument[];\r\n  supplierId?: string;\r\n}\r\n\r\nconst SupplierDocuments: React.FC<SupplierDocumentsProps> = ({\r\n  documents,\r\n  supplierId: _supplierId\r\n}) => {\r\n  const { showInfo } = useNotification();\r\n  const [selectedDocument, setSelectedDocument] = useState<SupplierDocument | null>(null);\r\n  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);\r\n\r\n  const getDocumentTypeLabel = (type: string) => {\r\n    const typeLabels: Record<string, string> = {\r\n      business_license: 'Business License',\r\n      tax_certificate: 'Tax Certificate',\r\n      insurance: 'Insurance Policy',\r\n      certification: 'Certification',\r\n      other: 'Other Document'\r\n    };\r\n    return typeLabels[type] || 'Unknown Document';\r\n  };\r\n\r\n  const handleViewDocument = (document: SupplierDocument) => {\r\n    setSelectedDocument(document);\r\n    setIsPreviewModalOpen(true);\r\n  };\r\n\r\n  const handleDownloadDocument = (document: SupplierDocument) => {\r\n    // In a real implementation, this would trigger a download\r\n    showInfo(`Downloading ${document.name}...`);\r\n    console.log('Download document:', document);\r\n  };\r\n\r\n  if (documents.length === 0) {\r\n    return (\r\n      <DetailSection\r\n        title=\"Verification Documents\"\r\n        description=\"Documents submitted for supplier verification\"\r\n      >\r\n        <div className=\"px-6 py-8 text-center\">\r\n          <DocumentIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No documents uploaded</h3>\r\n          <p className=\"mt-1 text-sm text-gray-500\">\r\n            This supplier has not uploaded any verification documents yet.\r\n          </p>\r\n        </div>\r\n      </DetailSection>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <DetailSection\r\n        title=\"Verification Documents\"\r\n        description=\"Documents submitted for supplier verification and compliance\"\r\n      >\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Document\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Type\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Upload Date\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {documents.map((document) => (\r\n                <tr key={document.id} className=\"hover:bg-gray-50\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <DocumentTextIcon className=\"h-5 w-5 text-gray-400 mr-3\" />\r\n                      <div>\r\n                        <div className=\"text-sm font-medium text-gray-900\">\r\n                          {document.name}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500\">\r\n                          {document.fileName} • {formatFileSize(document.fileSize)}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n                      {getDocumentTypeLabel(document.type)}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center text-sm text-gray-900\">\r\n                      <CalendarIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\r\n                      {formatDate(document.uploadDate)}\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"xs\"\r\n                        onClick={() => handleViewDocument(document)}\r\n                        icon={<EyeIcon className=\"w-4 h-4\" />}\r\n                      >\r\n                        View\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"xs\"\r\n                        onClick={() => handleDownloadDocument(document)}\r\n                        icon={<ArrowDownTrayIcon className=\"w-4 h-4\" />}\r\n                      >\r\n                        Download\r\n                      </Button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </DetailSection>\r\n\r\n      {/* Document Preview Modal */}\r\n      {selectedDocument && (\r\n        <Modal\r\n          isOpen={isPreviewModalOpen}\r\n          onClose={() => setIsPreviewModalOpen(false)}\r\n          title={`Document Preview: ${selectedDocument.name}`}\r\n          size=\"lg\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsPreviewModalOpen(false)}\r\n              >\r\n                Close\r\n              </Button>\r\n              <Button\r\n                variant=\"primary\"\r\n                onClick={() => handleDownloadDocument(selectedDocument)}\r\n                icon={<ArrowDownTrayIcon className=\"w-4 h-4\" />}\r\n              >\r\n                Download\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"space-y-4\">\r\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                <div>\r\n                  <span className=\"font-medium text-gray-500\">File Name:</span>\r\n                  <div className=\"text-gray-900\">{selectedDocument.fileName}</div>\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-medium text-gray-500\">File Size:</span>\r\n                  <div className=\"text-gray-900\">{formatFileSize(selectedDocument.fileSize)}</div>\r\n                </div>\r\n                <div>\r\n                  <span className=\"font-medium text-gray-500\">Upload Date:</span>\r\n                  <div className=\"text-gray-900\">{formatDate(selectedDocument.uploadDate)}</div>\r\n                </div>\r\n              </div>\r\n              {selectedDocument.notes && (\r\n                <div className=\"mt-4\">\r\n                  <span className=\"font-medium text-gray-500\">Notes:</span>\r\n                  <div className=\"text-gray-900 mt-1\">{selectedDocument.notes}</div>\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            {/* Document preview placeholder */}\r\n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\">\r\n              <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n              <p className=\"mt-2 text-sm text-gray-500\">\r\n                Document preview not available. Click download to view the file.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierDocuments;\r\n", "/**\r\n * Supplier Products Component\r\n *\r\n * This component displays supplier's products in a data table format\r\n * with action icons for view, edit, and delete operations.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport type { Column } from '../../../components/common/DataTable';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport Button from '../../../components/common/Button';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport Modal from '../../../components/common/Modal';\r\nimport useNotification from '../../../hooks/useNotification';\r\nimport type { SupplierProduct } from '../types';\r\nimport { ROUTES } from '../../../constants/routes';\r\nimport {\r\n  EyeIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  CubeIcon,\r\n  PlusIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\n\r\ninterface SupplierProductsProps {\r\n  products: SupplierProduct[];\r\n  supplierId?: string;\r\n  onProductUpdate?: () => void;\r\n}\r\n\r\nconst SupplierProducts: React.FC<SupplierProductsProps> = ({\r\n  products,\r\n  supplierId: _supplierId,\r\n  onProductUpdate\r\n}) => {\r\n  const navigate = useNavigate();\r\n  const { showSuccess, showError, showInfo } = useNotification();\r\n  const [selectedProduct, setSelectedProduct] = useState<SupplierProduct | null>(null);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n\r\n  const getStatusBadgeStatus = (status: string): string => {\r\n    switch (status) {\r\n      case 'active':\r\n        return 'active';\r\n      case 'inactive':\r\n        return 'pending';\r\n      case 'out_of_stock':\r\n        return 'rejected';\r\n      default:\r\n        return 'pending';\r\n    }\r\n  };\r\n\r\n  const getStockStatus = (stock: number, minimumStock: number, status: string) => {\r\n    if (status === 'out_of_stock' || stock === 0) {\r\n      return { text: 'Out of Stock', color: 'text-red-600' };\r\n    } else if (stock <= minimumStock) {\r\n      return { text: 'Low Stock', color: 'text-yellow-600' };\r\n    } else {\r\n      return { text: 'In Stock', color: 'text-green-600' };\r\n    }\r\n  };\r\n\r\n  const handleViewProduct = (product: SupplierProduct) => {\r\n    navigate(ROUTES.getProductDetailsRoute(product.id));\r\n  };\r\n\r\n  const handleEditProduct = (product: SupplierProduct) => {\r\n    // In a real implementation, this would navigate to edit page or open edit modal\r\n    showInfo(`Edit product: ${product.name}`);\r\n    console.log('Edit product:', product);\r\n  };\r\n\r\n  const handleDeleteProduct = (product: SupplierProduct) => {\r\n    setSelectedProduct(product);\r\n    setIsDeleteModalOpen(true);\r\n  };\r\n\r\n  const confirmDeleteProduct = async () => {\r\n    if (!selectedProduct) return;\r\n\r\n    try {\r\n      // In a real implementation, this would call an API\r\n      showSuccess(`Product \"${selectedProduct.name}\" deleted successfully`);\r\n      setIsDeleteModalOpen(false);\r\n      setSelectedProduct(null);\r\n      onProductUpdate?.();\r\n    } catch (error) {\r\n      showError('Failed to delete product');\r\n    }\r\n  };\r\n\r\n  const columns: Column<SupplierProduct>[] = [\r\n    {\r\n      key: 'name',\r\n      label: 'Product Name',\r\n      sortable: true,\r\n      render: (_value: string, product: SupplierProduct) => (\r\n        <div className=\"flex items-center\">\r\n          {product.image ? (\r\n            <img\r\n              src={product.image}\r\n              alt={product.name}\r\n              className=\"h-10 w-10 rounded-lg object-cover mr-3\"\r\n            />\r\n          ) : (\r\n            <div className=\"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3\">\r\n              <CubeIcon className=\"h-5 w-5 text-gray-400\" />\r\n            </div>\r\n          )}\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{product.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {product.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'sku',\r\n      label: 'SKU',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"font-mono text-sm text-gray-600\">{value}</span>\r\n      )\r\n    },\r\n    {\r\n      key: 'category',\r\n      label: 'Category',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n          {value}\r\n        </span>\r\n      )\r\n    },\r\n    {\r\n      key: 'price',\r\n      label: 'Price',\r\n      sortable: true,\r\n      render: (value: number) => (\r\n        <span className=\"font-medium text-gray-900\">{formatCurrency(value)}</span>\r\n      )\r\n    },\r\n    {\r\n      key: 'stock',\r\n      label: 'Stock',\r\n      sortable: true,\r\n      render: (value: number, product: SupplierProduct) => {\r\n        const stockStatus = getStockStatus(value, product.minimumStock || 10, product.status);\r\n        return (\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{value}</div>\r\n            <div className={`text-xs ${stockStatus.color}`}>{stockStatus.text}</div>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <StatusBadge status={getStatusBadgeStatus(value)} type=\"supplier\" />\r\n      )\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (_value: any, product: SupplierProduct) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"xs\"\r\n            onClick={() => handleViewProduct(product)}\r\n            icon={<EyeIcon className=\"w-4 h-4\" />}\r\n            title=\"View product details\"\r\n          >\r\n            View\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"xs\"\r\n            onClick={() => handleEditProduct(product)}\r\n            icon={<PencilIcon className=\"w-4 h-4\" />}\r\n            title=\"Edit product\"\r\n          >\r\n            Edit\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            size=\"xs\"\r\n            onClick={() => handleDeleteProduct(product)}\r\n            icon={<TrashIcon className=\"w-4 h-4\" />}\r\n            title=\"Delete product\"\r\n          >\r\n            Delete\r\n          </Button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  if (products.length === 0) {\r\n    return (\r\n      <DetailSection\r\n        title=\"Products\"\r\n        description=\"Products offered by this supplier\"\r\n      >\r\n        <div className=\"px-6 py-8 text-center\">\r\n          <CubeIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\r\n          <p className=\"mt-1 text-sm text-gray-500\">\r\n            This supplier has not added any products yet.\r\n          </p>\r\n          <div className=\"mt-6\">\r\n            <Button\r\n              variant=\"primary\"\r\n              icon={<PlusIcon className=\"w-4 h-4\" />}\r\n              onClick={() => showInfo('Add product functionality coming soon')}\r\n            >\r\n              Add Product\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <DetailSection\r\n        title=\"Products\"\r\n        description={`${products.length} products offered by this supplier`}\r\n      >\r\n        <DataTable<SupplierProduct>\r\n          columns={columns}\r\n          data={products}\r\n          onRowClick={handleViewProduct}\r\n          pagination={true}\r\n          pageSize={10}\r\n          emptyMessage=\"No products found\"\r\n          className=\"border-0\"\r\n        />\r\n      </DetailSection>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {selectedProduct && (\r\n        <Modal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          title=\"Delete Product\"\r\n          size=\"sm\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsDeleteModalOpen(false)}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={confirmDeleteProduct}\r\n              >\r\n                Delete Product\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"text-sm text-gray-500\">\r\n            Are you sure you want to delete \"{selectedProduct.name}\"? This action cannot be undone.\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierProducts;\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport Card from '../common/Card';\r\n\r\ninterface ChartSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst ChartSection: React.FC<ChartSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <Card className={className}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"px-4 py-5 sm:p-6\">\r\n        {children}\r\n      </div>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default ChartSection;", "/**\n * Simple Bar Chart Component\n *\n * A simple bar chart component using CSS for basic data visualization.\n * This is a replacement for the analytics BarChart to avoid dependencies.\n */\n\nimport React from 'react';\nimport Card from './Card';\n\ninterface SimpleBarChartProps {\n  title: string;\n  labels: string[];\n  data: number[];\n  color?: string;\n}\n\nconst SimpleBarChart: React.FC<SimpleBarChartProps> = ({\n  title,\n  labels,\n  data,\n  color = '#F28B22' // Primary color\n}) => {\n  const maxValue = Math.max(...data);\n\n  return (\n    <Card title={title}>\n      <div className=\"h-80 flex items-center justify-center\">\n        <div className=\"w-full h-full\">\n          <div className=\"w-full h-full flex flex-col\">\n            <div className=\"flex justify-between mb-4\">\n              {labels.map((label, index) => (\n                <div key={index} className=\"text-xs text-gray-500\">{label}</div>\n              ))}\n            </div>\n            <div className=\"flex-1 flex items-end\">\n              {data.map((value, index) => {\n                const height = maxValue > 0 ? `${(value / maxValue) * 100}%` : '0%';\n                return (\n                  <div key={index} className=\"flex-1 mx-1\">\n                    <div\n                      className=\"rounded-t-md w-full transition-all duration-500\"\n                      style={{ \n                        height,\n                        backgroundColor: color\n                      }}\n                    ></div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default SimpleBarChart;\n", "/**\n * Simple Pie Chart Component\n *\n * A simple pie chart component using CSS for basic data visualization.\n * This is a replacement for the analytics <PERSON><PERSON><PERSON> to avoid dependencies.\n */\n\nimport React from 'react';\nimport Card from './Card';\n\ninterface PieChartData {\n  labels: string[];\n  datasets: {\n    data: number[];\n    backgroundColor: string[];\n  }[];\n}\n\ninterface SimplePieChartProps {\n  title: string;\n  data: PieChartData;\n}\n\nconst SimplePieChart: React.FC<SimplePieChartProps> = ({\n  title,\n  data\n}) => {\n  const dataset = data.datasets[0];\n\n  // Handle case where dataset might be undefined\n  if (!dataset || !dataset.data || dataset.data.length === 0) {\n    return (\n      <Card title={title}>\n        <div className=\"h-80 flex items-center justify-center\">\n          <div className=\"text-center text-gray-500\">\n            No data available\n          </div>\n        </div>\n      </Card>\n    );\n  }\n\n  const total = dataset.data.reduce((sum, value) => sum + value, 0);\n\n  return (\n    <Card title={title}>\n      <div className=\"h-80 flex items-center justify-center\">\n        <div className=\"w-full h-full flex flex-col\">\n          {/* Simple legend */}\n          <div className=\"mb-4\">\n            {data.labels.map((label, index) => (\n              <div key={index} className=\"flex items-center mb-2\">\n                <div\n                  className=\"w-4 h-4 rounded mr-2\"\n                  style={{ backgroundColor: dataset.backgroundColor?.[index] || '#ccc' }}\n                ></div>\n                <span className=\"text-sm text-gray-600\">\n                  {label}: {dataset.data[index] || 0} ({((dataset.data[index] || 0) / total * 100).toFixed(1)}%)\n                </span>\n              </div>\n            ))}\n          </div>\n\n          {/* Simple visual representation */}\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-gray-900 mb-2\">Total: {total}</div>\n              <div className=\"text-sm text-gray-500\">Data Distribution</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default SimplePieChart;\n", "/**\r\n * Supplier Analytics Component\r\n *\r\n * This component displays supplier performance metrics and charts\r\n * following the same analytics layout as other analytics pages.\r\n */\r\n\r\nimport React from 'react';\r\nimport { CurrencyDollarIcon, ShoppingCartIcon, CubeIcon } from '@heroicons/react/24/outline';\r\nimport MetricsSection from '../../../components/common/MetricsSection';\r\nimport type { Metric } from '../../../components/common/MetricsSection';\r\nimport ChartSection from '../../../components/analytics/ChartSection';\r\nimport SimpleBarChart from '../../../components/common/SimpleBarChart';\r\nimport SimplePieChart from '../../../components/common/SimplePieChart';\r\n// import DetailSection from '../../../components/common/DetailSection';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\nimport type { SupplierAnalyticsData } from '../types';\r\n\r\ninterface SupplierAnalyticsProps {\r\n  supplierData: SupplierAnalyticsData;\r\n  supplierId: string;\r\n}\r\n\r\nconst SupplierAnalytics: React.FC<SupplierAnalyticsProps> = ({ supplierData, supplierId: _supplierId }) => {\r\n  // Prepare metrics\r\n  const metrics: Metric[] = [\r\n    {\r\n      title: 'Total Orders',\r\n      value: supplierData.totalOrders,\r\n      icon: <ShoppingCartIcon className=\"w-6 h-6 text-blue-500\" />\r\n    },\r\n    {\r\n      title: 'Total Revenue',\r\n      value: formatCurrency(supplierData.totalRevenue),\r\n      icon: <CurrencyDollarIcon className=\"w-6 h-6 text-green-500\" />\r\n    },\r\n    {\r\n      title: 'Products',\r\n      value: supplierData.productCount,\r\n      icon: <CubeIcon className=\"w-6 h-6 text-purple-500\" />\r\n    },\r\n    \r\n  ];\r\n\r\n  // Prepare chart data\r\n  const revenueChartData = supplierData.revenueHistory.map(item => ({\r\n    label: item.date,\r\n    value: item.amount\r\n  }));\r\n\r\n  const productChartData = supplierData.salesByProduct.map(item => ({\r\n    label: item.productName,\r\n    value: item.amount\r\n  }));\r\n\r\n  const orderTrendsData = supplierData.orderTrends.map(item => ({\r\n    label: item.date,\r\n    value: item.orders\r\n  }));\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Key Metrics */}\r\n      <MetricsSection metrics={metrics} />\r\n\r\n      {/* Charts Grid */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <ChartSection title=\"Revenue History\" description=\"Supplier's revenue over time\">\r\n          <SimpleBarChart\r\n            data={revenueChartData.map(item => item.value)}\r\n            labels={revenueChartData.map(item => item.label)}\r\n            title=\"Revenue History\"\r\n            color=\"#F28B22\" // Primary color\r\n          />\r\n        </ChartSection>\r\n\r\n        <ChartSection title=\"Order Trends\" description=\"Number of orders over time\">\r\n          <SimpleBarChart\r\n            data={orderTrendsData.map(item => item.value)}\r\n            labels={orderTrendsData.map(item => item.label)}\r\n            title=\"Order Trends\"\r\n            color=\"#3B82F6\" // Blue color\r\n          />\r\n        </ChartSection>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <ChartSection title=\"Sales by Product\" description=\"Revenue distribution by product\">\r\n          <SimplePieChart\r\n            title=\"Sales by Product\"\r\n            data={{\r\n              labels: productChartData.map(item => item.label),\r\n              datasets: [{\r\n                data: productChartData.map(item => item.value),\r\n                backgroundColor: ['#F28B22', '#F9B16F', '#D17311', '#FFC380', '#A85A0D']\r\n              }]\r\n            }}\r\n          />\r\n        </ChartSection>\r\n\r\n        <ChartSection title=\"Top Categories\" description=\"Revenue by product category\">\r\n          <SimplePieChart\r\n            title=\"Top Categories\"\r\n            data={{\r\n              labels: supplierData.topCategories.map(item => item.category),\r\n              datasets: [{\r\n                data: supplierData.topCategories.map(item => item.revenue),\r\n                backgroundColor: ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444']\r\n              }]\r\n            }}\r\n          />\r\n        </ChartSection>\r\n      </div>\r\n\r\n      {/* Performance Summary\r\n      <DetailSection\r\n        title=\"Performance Summary\"\r\n        description=\"Key performance indicators and insights\"\r\n      >\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary\">{supplierData.totalOrders}</div>\r\n              <div className=\"text-sm text-gray-500\">Total Orders</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-green-600\">{formatCurrency(supplierData.totalRevenue)}</div>\r\n              <div className=\"text-sm text-gray-500\">Total Revenue</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-blue-600\">{formatCurrency(supplierData.averageOrderValue)}</div>\r\n              <div className=\"text-sm text-gray-500\">Average Order Value</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DetailSection> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierAnalytics;", "/**\r\n * Supplier Profile Page\r\n *\r\n * This page displays comprehensive supplier information with multiple tabs/sections,\r\n * following the exact design pattern and styling used in UserEditPage and UserDetails.\r\n */\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Tabs from '../components/common/Tabs';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport Button from '../components/common/Button';\r\nimport Modal from '../components/common/Modal';\r\nimport useNotification from '../hooks/useNotification';\r\nimport { TrashIcon, NoSymbolIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\r\nimport SupplierPersonalInfo from '../features/suppliers/components/SupplierPersonalInfo';\r\nimport SupplierDocuments from '../features/suppliers/components/SupplierDocuments';\r\nimport SupplierProducts from '../features/suppliers/components/SupplierProducts';\r\nimport SupplierAnalytics from '../features/suppliers/components/SupplierAnalytics';\r\nimport { useSuppliers } from '../features/suppliers/hooks/useSuppliers';\r\nimport type {\r\n  Supplier,\r\n  SupplierProduct,\r\n  SupplierDocument,\r\n  SupplierAnalyticsData\r\n} from '../features/suppliers/types';\r\nimport { ROUTES } from '../constants/routes';\r\n\r\n\r\n\r\nconst SupplierProfilePage: React.FC = () => {\r\n  const { id: supplierId } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const { showError, showSuccess } = useNotification();\r\n\r\n  // Use refs to store stable references to notification functions\r\n  const showErrorRef = useRef(showError);\r\n  const showSuccessRef = useRef(showSuccess);\r\n\r\n  // Update refs when functions change\r\n  useEffect(() => {\r\n    showErrorRef.current = showError;\r\n    showSuccessRef.current = showSuccess;\r\n  }, [showError, showSuccess]);\r\n\r\n  // Use ref to store supplierId to avoid recreating fetchSupplierData\r\n  const supplierIdRef = useRef(supplierId);\r\n  useEffect(() => {\r\n    supplierIdRef.current = supplierId;\r\n  }, [supplierId]);\r\n  \r\n  // State\r\n  const [activeTab, setActiveTab] = useState<'personal' | 'documents' | 'products' | 'analytics'>('personal');\r\n  const [supplier, setSupplier] = useState<Supplier | null>(null);\r\n  const [supplierProducts, setSupplierProducts] = useState<SupplierProduct[]>([]);\r\n  const [supplierDocuments, setSupplierDocuments] = useState<SupplierDocument[]>([]);\r\n  const [supplierAnalytics, setSupplierAnalytics] = useState<SupplierAnalyticsData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isBanModalOpen, setIsBanModalOpen] = useState(false);\r\n  const [isBanning, setIsBanning] = useState(false);\r\n\r\n  // Use the useSuppliers hook for API integration\r\n  const {\r\n    getSupplierById,\r\n    getSupplierProducts,\r\n    getSupplierDocuments,\r\n    getSupplierAnalytics,\r\n    deleteSupplier,\r\n    banSupplier,\r\n    unbanSupplier,\r\n    isLoading: hookLoading\r\n  } = useSuppliers();\r\n\r\n\r\n\r\n  // Fetch supplier data - using useRef to prevent recreation\r\n  const fetchSupplierData = useRef(async () => {\r\n    const currentSupplierId = supplierIdRef.current;\r\n\r\n    if (!currentSupplierId) {\r\n      setError('No supplier ID provided');\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      // Fetch supplier data from API\r\n      const supplierData = await getSupplierById(currentSupplierId);\r\n      setSupplier(supplierData);\r\n\r\n      // Fetch related data in parallel\r\n      const [products, documents, analytics] = await Promise.all([\r\n        getSupplierProducts(currentSupplierId),\r\n        getSupplierDocuments(currentSupplierId),\r\n        getSupplierAnalytics(currentSupplierId)\r\n      ]);\r\n\r\n      setSupplierProducts(products);\r\n      setSupplierDocuments(documents);\r\n      setSupplierAnalytics(analytics);\r\n\r\n    } catch (error) {\r\n      console.error('Error fetching supplier data:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch supplier data';\r\n      setError(errorMessage);\r\n      showErrorRef.current('Failed to load supplier data');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  });\r\n\r\n  // Effect to fetch data when supplierId changes\r\n  useEffect(() => {\r\n    fetchSupplierData.current();\r\n  }, [supplierId]);\r\n\r\n\r\n\r\n  // Handle supplier deletion\r\n  const handleDeleteSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsDeleting(true);\r\n\r\n      await deleteSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been deleted successfully`);\r\n      setIsDeleteModalOpen(false);\r\n\r\n      // Navigate back to suppliers list\r\n      navigate(ROUTES.SUPPLIERS);\r\n\r\n    } catch (error) {\r\n      console.error('Error deleting supplier:', error);\r\n      showErrorRef.current('Failed to delete supplier');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Handle supplier ban\r\n  const handleBanSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsBanning(true);\r\n\r\n      await banSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been banned successfully`);\r\n      setIsBanModalOpen(false);\r\n\r\n      // Refresh supplier data to show updated status\r\n      await fetchSupplierData.current();\r\n\r\n    } catch (error) {\r\n      console.error('Error banning supplier:', error);\r\n      showErrorRef.current('Failed to ban supplier');\r\n    } finally {\r\n      setIsBanning(false);\r\n    }\r\n  };\r\n\r\n  // Handle supplier unban\r\n  const handleUnbanSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsBanning(true);\r\n\r\n      await unbanSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been unbanned successfully`);\r\n\r\n      // Refresh supplier data to show updated status\r\n      await fetchSupplierData.current();\r\n\r\n    } catch (error) {\r\n      console.error('Error unbanning supplier:', error);\r\n      showErrorRef.current('Failed to unban supplier');\r\n    } finally {\r\n      setIsBanning(false);\r\n    }\r\n  };\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (tabId: string) => {\r\n    setActiveTab(tabId as 'personal' | 'documents' | 'products' | 'analytics');\r\n  };\r\n\r\n  // Loading state\r\n  if (isLoading || hookLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center min-h-screen\">\r\n        <LoadingSpinner size=\"lg\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error || !supplier) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <PageHeader\r\n          title=\"Supplier Profile\"\r\n          description=\"Supplier not found\"\r\n          breadcrumbs={[\r\n            { label: 'Suppliers', path: ROUTES.SUPPLIERS },\r\n            { label: 'Profile' }\r\n          ]}\r\n        />\r\n        <div className=\"text-center py-12\">\r\n          <p className=\"text-gray-500\">{error || 'Supplier not found'}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title={`Supplier: ${supplier.name}`}\r\n        description=\"Comprehensive supplier profile and management\"\r\n        breadcrumbs={[\r\n          { label: 'Suppliers', path: ROUTES.SUPPLIERS },\r\n          { label: supplier.name }\r\n        ]}\r\n        actions={\r\n          <div className=\"flex gap-2\">\r\n            {supplier.status === 'banned' ? (\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleUnbanSupplier}\r\n                icon={<CheckCircleIcon className=\"h-4 w-4\" />}\r\n                disabled={isLoading || isBanning || isDeleting}\r\n                loading={isBanning}\r\n              >\r\n                Unban Supplier\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n                onClick={() => setIsBanModalOpen(true)}\r\n                icon={<NoSymbolIcon className=\"h-4 w-4\" />}\r\n                disabled={isLoading || isBanning || isDeleting}\r\n              >\r\n                Ban Supplier\r\n              </Button>\r\n            )}\r\n            <Button\r\n              variant=\"danger\"\r\n              size=\"sm\"\r\n              onClick={() => setIsDeleteModalOpen(true)}\r\n              icon={<TrashIcon className=\"h-4 w-4\" />}\r\n              disabled={isLoading || isDeleting || isBanning}\r\n            >\r\n              Delete Supplier\r\n            </Button>\r\n          </div>\r\n        }\r\n      />\r\n      \r\n      <Tabs\r\n        tabs={[\r\n          { id: 'personal', label: 'Personal Information' },\r\n          { id: 'documents', label: 'Verification Documents' },\r\n          { id: 'products', label: 'Products' },\r\n          { id: 'analytics', label: 'Analytics' }\r\n        ]}\r\n        activeTab={activeTab}\r\n        onChange={handleTabChange}\r\n      />\r\n      \r\n      {activeTab === 'personal' && (\r\n        <SupplierPersonalInfo supplier={supplier} />\r\n      )}\r\n\r\n\r\n      {activeTab === 'documents' && (\r\n        <SupplierDocuments\r\n          documents={supplierDocuments}\r\n          supplierId={supplier.id}\r\n        />\r\n      )}\r\n      \r\n      {activeTab === 'products' && (\r\n        <SupplierProducts\r\n          products={supplierProducts}\r\n          supplierId={supplier.id}\r\n          onProductUpdate={fetchSupplierData.current}\r\n        />\r\n      )}\r\n      \r\n      {activeTab === 'analytics' && supplierAnalytics && (\r\n        <SupplierAnalytics\r\n          supplierData={supplierAnalytics}\r\n          supplierId={supplier.id}\r\n        />\r\n      )}\r\n\r\n      {/* Ban Confirmation Modal */}\r\n      <Modal\r\n        isOpen={isBanModalOpen}\r\n        onClose={() => setIsBanModalOpen(false)}\r\n        title=\"Ban Supplier\"\r\n        size=\"sm\"\r\n        footer={\r\n          <>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setIsBanModalOpen(false)}\r\n              disabled={isBanning}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={handleBanSupplier}\r\n              loading={isBanning}\r\n              icon={<NoSymbolIcon className=\"h-4 w-4\" />}\r\n            >\r\n              Ban Supplier\r\n            </Button>\r\n          </>\r\n        }\r\n      >\r\n        <div className=\"text-sm text-gray-500\">\r\n          <p className=\"mb-3\">\r\n            Are you sure you want to ban <strong>\"{supplier.name}\"</strong>?\r\n          </p>\r\n          <p className=\"text-orange-600 font-medium\">\r\n            This action will:\r\n          </p>\r\n          <ul className=\"mt-2 list-disc list-inside text-orange-600\">\r\n            <li>Change the supplier's status to 'banned'</li>\r\n            <li>Prevent them from receiving new orders</li>\r\n            <li>Restrict their access to the platform</li>\r\n            <li>Allow for potential future reactivation</li>\r\n          </ul>\r\n          <p className=\"mt-3 text-gray-600\">\r\n            Unlike deletion, this action can be reversed by changing the supplier's status back to 'active'.\r\n          </p>\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal\r\n        isOpen={isDeleteModalOpen}\r\n        onClose={() => setIsDeleteModalOpen(false)}\r\n        title=\"Delete Supplier\"\r\n        size=\"sm\"\r\n        footer={\r\n          <>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setIsDeleteModalOpen(false)}\r\n              disabled={isDeleting}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"danger\"\r\n              onClick={handleDeleteSupplier}\r\n              loading={isDeleting}\r\n              icon={<TrashIcon className=\"h-4 w-4\" />}\r\n            >\r\n              Delete Supplier\r\n            </Button>\r\n          </>\r\n        }\r\n      >\r\n        <div className=\"text-sm text-gray-500\">\r\n          <p className=\"mb-3\">\r\n            Are you sure you want to delete <strong>\"{supplier.name}\"</strong>?\r\n          </p>\r\n          <p className=\"text-red-600 font-medium\">\r\n            This action cannot be undone and will permanently remove:\r\n          </p>\r\n          <ul className=\"mt-2 list-disc list-inside text-red-600\">\r\n            <li>All supplier information</li>\r\n            <li>Associated products and documents</li>\r\n            <li>Order history and analytics</li>\r\n          </ul>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierProfilePage;\r\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "import * as React from \"react\";\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownTrayIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailItemProps {\r\n  label: string;\r\n  value: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailItem: React.FC<DetailItemProps> = ({\r\n  label,\r\n  value,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${className}`}>\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{value}</dd>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailItem;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailSection: React.FC<DetailSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-white shadow overflow-hidden sm:rounded-lg ${className}`}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"border-t border-gray-200\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailSection;", "import * as React from \"react\";\nfunction CalendarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CalendarIcon);\nexport default ForwardRef;", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction CubeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CubeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "className", "_jsx", "tabs", "activeTab", "onChange", "map", "tab", "isActive", "id", "onClick", "disabled", "label", "getIconBackgroundClass", "icon", "React", "colorMatch", "props", "match", "title", "data", "formatValue", "value", "toString", "Card", "_jsxs", "iconElement", "colorClass", "total", "undefined", "growth", "toFixed", "metrics", "metric", "index", "MetricCard", "parseFloat", "change", "CurrencyDollarIcon", "svgRef", "titleId", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "NoSymbolIcon", "MapPinIcon", "GlobeAltIcon", "supplier", "DetailSection", "description", "Avatar", "logo", "src", "alt", "name", "size", "StatusBadge", "status", "type", "verificationStatus", "char<PERSON>t", "toUpperCase", "slice", "DetailList", "DetailItem", "UserIcon", "<PERSON><PERSON><PERSON>", "EnvelopeIcon", "href", "email", "PhoneIcon", "phone", "address", "website", "target", "rel", "categories", "length", "category", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "getVerificationIcon", "getVerificationText", "DocumentIcon", "DocumentTextIcon", "documents", "supplierId", "_supplierId", "showInfo", "useNotification", "selectedDocument", "setSelectedDocument", "useState", "isPreviewModalOpen", "setIsPreviewModalOpen", "handleDownloadDocument", "document", "console", "log", "scope", "fileName", "formatFileSize", "fileSize", "business_license", "tax_certificate", "insurance", "certification", "other", "CalendarIcon", "formatDate", "uploadDate", "<PERSON><PERSON>", "variant", "handleViewDocument", "EyeIcon", "ArrowDownTrayIcon", "Modal", "isOpen", "onClose", "footer", "_Fragment", "notes", "products", "onProductUpdate", "navigate", "useNavigate", "showSuccess", "showError", "selectedProduct", "setSelectedProduct", "isDeleteModalOpen", "setIsDeleteModalOpen", "getStatusBadgeStatus", "handleViewProduct", "product", "ROUTES", "getProductDetailsRoute", "columns", "key", "sortable", "render", "_value", "image", "CubeIcon", "formatCurrency", "stockStatus", "stock", "minimumStock", "text", "color", "getStockStatus", "handleEditProduct", "PencilIcon", "handleDeleteProduct", "TrashIcon", "PlusIcon", "DataTable", "onRowClick", "pagination", "pageSize", "emptyMessage", "async", "error", "labels", "maxValue", "Math", "max", "height", "style", "backgroundColor", "dataset", "datasets", "reduce", "sum", "_dataset$backgroundCo", "supplierData", "totalOrders", "ShoppingCartIcon", "totalRevenue", "productCount", "revenueChartData", "revenueHistory", "item", "date", "amount", "productChartData", "salesByProduct", "productName", "orderTrendsData", "orderTrends", "orders", "MetricsSection", "ChartSection", "SimpleBarChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topCategories", "revenue", "SupplierProfilePage", "useParams", "showErrorRef", "useRef", "showSuccessRef", "useEffect", "current", "supplierIdRef", "setActiveTab", "setSupplier", "supplierProducts", "setSupplierProducts", "supplierDocuments", "setSupplierDocuments", "supplierAnalytics", "setSupplierAnalytics", "isLoading", "setIsLoading", "setError", "isDeleting", "setIsDeleting", "isBanModalOpen", "setIsBanModalOpen", "isBanning", "setIsBanning", "getSupplierById", "getSupplierProducts", "getSupplierDocuments", "getSupplierAnalytics", "deleteSupplier", "banSupplier", "unbanSupplier", "hookLoading", "useSuppliers", "fetchSupplierData", "currentSupplierId", "analytics", "Promise", "all", "errorMessage", "Error", "message", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbs", "path", "SUPPLIERS", "actions", "loading", "Tabs", "tabId", "SupplierPersonalInfo", "SupplierDocuments", "SupplierProducts", "SupplierAnalytics", "testId", "Link", "to", "HomeIcon", "ChevronRightIcon", "memo", "dateString", "options", "arguments", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "currency", "locale", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "bytes", "i", "floor", "pow", "_type", "statusKey", "toLowerCase", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "modalRef", "handleEscape", "e", "addEventListener", "body", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "xs", "sm", "md", "lg", "xl", "full", "stopPropagation", "XMarkIcon", "createPortal"], "sourceRoot": ""}