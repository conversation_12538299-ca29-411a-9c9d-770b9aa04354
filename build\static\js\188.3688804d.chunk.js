"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[188],{2659:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{tabs:t,activeTab:s,onChange:r,className:i=""}=e;return(0,a.jsx)("div",{className:`border-b border-gray-200 ${i}`,children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:t.map((e=>{const t=e.id===s;return(0,a.jsx)("button",{onClick:()=>!e.disabled&&r(e.id),className:`\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\n                ${t?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                ${e.disabled?"opacity-50 cursor-not-allowed":"cursor-pointer"}\n              `,disabled:e.disabled,children:e.label},e.id)}))})})}},2806:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(5043),r=s(5475),i=s(5501),n=s(6365),l=s(579);const c=e=>{let{title:t,description:s,actions:a,breadcrumbs:c,className:o="",testId:d}=e;return(0,l.jsxs)("div",{className:`mb-6 ${o}`,"data-testid":d,children:[c&&c.length>0&&(0,l.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,l.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,l.jsx)("li",{children:(0,l.jsx)(r.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})}),c.map(((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<c.length-1?(0,l.jsx)(r.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),s&&"string"===typeof s?(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),a&&(0,l.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:a})]})]})},o=(0,a.memo)(c)},3593:(e,t,s)=>{s.d(t,{A:()=>n});var a=s(5043),r=s(579);const i=e=>{let{title:t,subtitle:s,children:a,className:i="",bodyClassName:n="",headerClassName:l="",footerClassName:c="",icon:o,footer:d,onClick:m,hoverable:u=!1,noPadding:h=!1,bordered:x=!0,loading:f=!1,testId:p}=e;const g=`\n    bg-white rounded-xl ${x?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${i}\n  `,y=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${l}\n  `,b=`\n    ${h?"":"p-6"}\n    ${n}\n  `,v=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${c}\n  `;return f?(0,r.jsxs)("div",{className:g,"data-testid":p,children:[(t||s||o)&&(0,r.jsxs)("div",{className:y,children:[(0,r.jsxs)("div",{className:"w-full",children:[t&&(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),s&&(0,r.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),o&&(0,r.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,r.jsx)("div",{className:b,children:(0,r.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),d&&(0,r.jsx)("div",{className:v,children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,r.jsxs)("div",{className:g,onClick:m,"data-testid":p,children:[(t||s||o)&&(0,r.jsxs)("div",{className:y,children:[(0,r.jsxs)("div",{children:["string"===typeof t?(0,r.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof s?(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),o&&(0,r.jsx)("div",{className:"text-primary",children:o})]}),(0,r.jsx)("div",{className:b,children:a}),d&&(0,r.jsx)("div",{className:v,children:d})]})},n=(0,a.memo)(i)},3927:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{size:t="md",className:s="",variant:r="spinner",color:i="#F28B22",useCurrentColor:n=!1}=e;const l={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},c=n?"currentColor":i;return"spinner"===r?(0,a.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,a.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${l[t].spinner}`,style:{borderTopColor:c,borderRightColor:c}}),(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===r?(0,a.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${s}`,role:"status","aria-label":"Loading",children:[(0,a.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,a.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,a.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===r?(0,a.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,a.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:c}}),(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===r?(0,a.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,a.jsx)("div",{className:`${l[t].ripple} rounded-full ripple-effect`,style:{color:c},children:(0,a.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:c}})}),(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},6188:(e,t,s)=>{s.r(t),s.d(t,{default:()=>z});var a=s(5043),r=s(3593),i=s(7907),n=s(3927),l=s(2806),c=s(7752),o=s(2659),d=s(6009);function m(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const u=a.forwardRef(m);var h=s(2903),x=s(7012),f=s(579);const p=e=>{let{activeTab:t,onTabChange:s}=e;const a=[{id:"profile",label:"Profile",icon:(0,f.jsx)(d.A,{className:"w-5 h-5 mr-2"})},{id:"security",label:"Security",icon:(0,f.jsx)(u,{className:"w-5 h-5 mr-2"})},{id:"notifications",label:"Notifications",icon:(0,f.jsx)(h.A,{className:"w-5 h-5 mr-2"})},{id:"activity",label:"Activity",icon:(0,f.jsx)(x.A,{className:"w-5 h-5 mr-2"})}].map((e=>({id:e.id,label:(0,f.jsxs)("div",{className:"flex items-center",children:[e.icon,e.label]})})));return(0,f.jsx)(o.A,{tabs:a,activeTab:t,onChange:e=>s(e)})};function g(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"}))}const y=a.forwardRef(g);var b=s(8300);const v=e=>{let{profile:t,isEditing:s,onInputChange:r,onAvatarChange:i}=e;const n=(0,a.useRef)(null);return t?(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-6 pb-6 border-b border-gray-100",children:[(0,f.jsxs)("div",{className:"relative",children:[(0,f.jsx)(b.A,{...t.avatar&&t.avatar.trim()&&{src:t.avatar},alt:t.name,name:t.name,size:"2xl",className:"w-24 h-24"}),s&&(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("button",{onClick:()=>{s&&n.current&&n.current.click()},className:"absolute bottom-0 right-0 bg-white rounded-full p-1.5 shadow-md hover:bg-gray-50",children:(0,f.jsx)(y,{className:"w-5 h-5 text-gray-600"})}),(0,f.jsx)("input",{ref:n,type:"file",accept:"image/*",onChange:e=>{var t;const s=null===(t=e.target.files)||void 0===t?void 0:t[0];s&&i&&i(s)},className:"hidden"})]})]}),(0,f.jsxs)("div",{className:"flex-1",children:[(0,f.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:t.name}),(0,f.jsx)("p",{className:"text-gray-500",children:t.role}),(0,f.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Member since ",new Date(t.joinDate).toLocaleDateString()]})]})]}),(0,f.jsx)("div",{className:"space-y-4",children:(0,f.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),s?(0,f.jsx)("input",{type:"text",name:"name",value:t.name,onChange:r,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"}):(0,f.jsx)("p",{className:"text-gray-800",children:t.name})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),s?(0,f.jsx)("input",{type:"email",name:"email",value:t.email,onChange:r,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"}):(0,f.jsx)("p",{className:"text-gray-800",children:t.email})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),s?(0,f.jsx)("input",{type:"tel",name:"phone",value:t.phone,onChange:r,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"}):(0,f.jsx)("p",{className:"text-gray-800",children:t.phone})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),(0,f.jsx)("p",{className:"text-gray-800",children:t.role})]})]})})]}):(0,f.jsx)("div",{className:"space-y-6",children:(0,f.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,f.jsx)("p",{className:"text-gray-500",children:"Profile information not available"})})})},j=e=>{let{enabled:t,onChange:s,size:a="md",disabled:r=!1,className:i=""}=e;const n={sm:{container:"h-5 w-9",toggle:"h-3 w-3",translate:t?"translate-x-4":"translate-x-1"},md:{container:"h-6 w-11",toggle:"h-4 w-4",translate:t?"translate-x-6":"translate-x-1"},lg:{container:"h-7 w-14",toggle:"h-5 w-5",translate:t?"translate-x-8":"translate-x-1"}};return(0,f.jsx)("button",{type:"button",className:`relative inline-flex ${n[a].container} items-center rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 ${t?"bg-primary":"bg-gray-200"} ${r?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${i}`,onClick:r?void 0:s,disabled:r,role:"switch","aria-checked":t,children:(0,f.jsx)("span",{className:`inline-block ${n[a].toggle} transform rounded-full bg-white transition-transform ${n[a].translate}`})})},w=e=>{let{profile:t,onToggleChange:s,onChangePassword:a,onSignOutAllDevices:r}=e;return t?(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Password"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Last changed 30 days ago"})]}),(0,f.jsx)(i.A,{variant:"outline",size:"sm",onClick:a,children:"Change Password"})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Two-Factor Authentication"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:t.twoFactorEnabled?"Enabled - Using Authenticator App":"Disabled - Enable for extra security"})]}),(0,f.jsx)(j,{enabled:t.twoFactorEnabled,onChange:()=>s("twoFactorEnabled")})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Login Sessions"}),(0,f.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Last login: ",t.lastLogin," from IP ",t.lastIp]})]}),(0,f.jsx)(i.A,{variant:"outline",size:"sm",className:"text-red-600 hover:bg-red-50",onClick:r,children:"Sign Out All Devices"})]})]}):(0,f.jsx)("div",{className:"space-y-6",children:(0,f.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,f.jsx)("p",{className:"text-gray-500",children:"Security settings not available"})})})};function N(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const k=a.forwardRef(N),C=e=>{let{profile:t,onToggleChange:s,isAdmin:a=!1}=e;return t?(0,f.jsxs)("div",{className:"space-y-8",children:[(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,f.jsx)(h.A,{className:"h-5 w-5 text-gray-600"}),(0,f.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Personal Notifications"})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Email Notifications"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Receive notifications via email"})]}),(0,f.jsx)(j,{enabled:t.notificationsEnabled.email,onChange:()=>s("notifications.email")})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Push Notifications"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Receive push notifications in browser"})]}),(0,f.jsx)(j,{enabled:t.notificationsEnabled.push,onChange:()=>s("notifications.push")})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"SMS Notifications"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Receive important notifications via SMS"})]}),(0,f.jsx)(j,{enabled:t.notificationsEnabled.sms,onChange:()=>s("notifications.sms")})]})]}),a&&t.adminNotifications&&(0,f.jsxs)("div",{className:"space-y-6 pt-6 border-t border-gray-200",children:[(0,f.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,f.jsx)(k,{className:"h-5 w-5 text-gray-600"}),(0,f.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Admin Notifications"})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"New User Registrations"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Get notified when a new user registers"})]}),(0,f.jsx)(j,{enabled:t.adminNotifications.newUsers,onChange:()=>s("adminNotifications.newUsers")})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"New Orders"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Get notified when a new order is placed"})]}),(0,f.jsx)(j,{enabled:t.adminNotifications.newOrders,onChange:()=>s("adminNotifications.newOrders")})]}),(0,f.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-100",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("h3",{className:"text-base font-medium text-gray-800",children:"Supplier Verification Requests"}),(0,f.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Get notified when a supplier requests verification"})]}),(0,f.jsx)(j,{enabled:t.adminNotifications.supplierVerifications,onChange:()=>s("adminNotifications.supplierVerifications")})]})]})]}):(0,f.jsx)("div",{className:"space-y-6",children:(0,f.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,f.jsx)("p",{className:"text-gray-500",children:"Notification settings not available"})})})};function A(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"}))}const E=a.forwardRef(A);function L(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))}const P=a.forwardRef(L);function S(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const $=a.forwardRef(S);function M(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const F=a.forwardRef(M);var R=s(4538);const I=e=>{let{activities:t,isLoading:s=!1}=e;const a=e=>{switch(e){case"login":return(0,f.jsx)(E,{className:"h-5 w-5"});case"password":return(0,f.jsx)(P,{className:"h-5 w-5"});case"security":return(0,f.jsx)(u,{className:"h-5 w-5"});case"account":return(0,f.jsx)($,{className:"h-5 w-5"});case"profile":return(0,f.jsx)(d.A,{className:"h-5 w-5"});case"settings":return(0,f.jsx)(F,{className:"h-5 w-5"});case"notifications":return(0,f.jsx)(h.A,{className:"h-5 w-5"});default:return(0,f.jsx)(R.A,{className:"h-5 w-5"})}};return s?(0,f.jsx)("div",{className:"space-y-4",children:(0,f.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,f.jsxs)("div",{className:"text-center",children:[(0,f.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"}),(0,f.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Loading activity log..."})]})})}):0===t.length?(0,f.jsx)("div",{className:"space-y-4",children:(0,f.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,f.jsx)("div",{className:"text-center",children:(0,f.jsx)("p",{className:"text-gray-500",children:"No activity found"})})})}):(0,f.jsx)("div",{className:"space-y-4",children:(0,f.jsx)("div",{className:"flow-root",children:(0,f.jsx)("ul",{className:"-mb-8",children:t.map(((e,s)=>(0,f.jsx)("li",{children:(0,f.jsxs)("div",{className:"relative pb-8",children:[s!==t.length-1?(0,f.jsx)("span",{className:"absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,f.jsxs)("div",{className:"relative flex items-start space-x-3",children:[(0,f.jsx)("div",{children:(0,f.jsx)("div",{className:"relative px-1",children:(0,f.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 ring-8 ring-white icon-container",children:(0,f.jsx)("div",{className:"text-gray-600",children:e.icon||a(e.type)})})})}),(0,f.jsx)("div",{className:"min-w-0 flex-1 py-1.5",children:(0,f.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,f.jsx)("div",{className:"font-medium text-gray-900",children:e.content}),(0,f.jsx)("span",{className:"whitespace-nowrap",children:new Date(e.date).toLocaleString()})]})})]})]})},e.id)))})})})};var D=s(1568),O=s(4703),V=s(8479);const B={getProfile:async()=>{try{const e=await D.A.get("/profile");return V.lg.getById(e,"profile","current")}catch(e){throw(0,O.hS)(e)}},updateProfile:async e=>{try{const t=await D.A.put("/profile",e);return V.lg.update(t,"profile","current")}catch(t){throw(0,O.hS)(t)}},updateProfilePicture:async e=>{try{const t=new FormData;t.append("picture",e);const s=await D.A.put("/profile/picture",t,{headers:{"Content-Type":"multipart/form-data"}});return V.lg.update(s,"profile picture","current")}catch(t){throw(0,O.hS)(t)}},changePassword:async e=>{try{return await D.A.put("/profile/password",e),{success:!0}}catch(t){throw(0,O.hS)(t)}},deleteProfilePicture:async()=>{try{const e=await D.A.delete("/profile/picture");return V.lg.update(e,"profile picture","current")}catch(e){throw(0,O.hS)(e)}},updateAvatar:async e=>{try{const t=await B.updateProfilePicture(e);return{avatarUrl:t.avatar||""}}catch(t){throw(0,O.hS)(t)}},updatePreferences:async e=>{try{const t=await D.A.put("/profile/preferences",{preferences:e});return V.lg.update(t,"preferences","current").preferences}catch(t){throw(0,O.hS)(t)}},getActivityLog:async e=>{try{const t=await D.A.get("/profile/activity",{params:e});return V.lg.getList(t,"activity log",!0)}catch(t){throw(0,O.hS)(t)}}},T=B;var W=s(9705);const H=()=>{const[e,t]=(0,a.useState)(null),[s,r]=(0,a.useState)(!1),[i,n]=(0,a.useState)(null),{showNotification:l}=(0,W.A)(),c=(0,a.useRef)(l),o=(0,a.useRef)(!1),d=(0,a.useRef)(null),m=3e5;(0,a.useEffect)((()=>{c.current=l}));const u=(0,a.useCallback)((()=>!!d.current&&Date.now()-d.current.timestamp<m),[m]),h=(0,a.useCallback)((async function(){if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])&&u()&&d.current)return t(d.current.data),d.current.data;r(!0),n(null);try{console.log("[useProfile] Fetching profile...");const e=await T.getProfile();return console.log("[useProfile] Profile fetched successfully:",e),t(e),d.current={data:e,timestamp:Date.now()},e}catch(e){console.error("[useProfile] Error fetching profile:",e);const t=e;n(t);let s="Failed to fetch profile";throw t.message.includes("Network")?s="Network error. Please check your connection and try again.":t.message.includes("401")||t.message.includes("Unauthorized")?s="You are not authorized to view this profile. Please log in again.":t.message.includes("404")||t.message.includes("Not found")?s="Profile not found. Please contact support if this issue persists.":(t.message.includes("500")||t.message.includes("Server"))&&(s="Server error. Please try again later."),c.current({type:"error",title:"Profile Error",message:s}),e}finally{r(!1)}}),[u]),x=(0,a.useCallback)((async e=>{r(!0),n(null);try{const s=await T.updateProfile(e);return t(s),d.current={data:s,timestamp:Date.now()},c.current({type:"success",title:"Success",message:"Profile updated successfully"}),s}catch(s){throw n(s),c.current({type:"error",title:"Error",message:"Failed to update profile"}),s}finally{r(!1)}}),[]),f=(0,a.useCallback)((async s=>{r(!0),n(null);try{const{avatarUrl:a}=await T.updateAvatar(s),r=e?{...e,avatar:a}:null;return t(r),r&&(d.current={data:r,timestamp:Date.now()}),c.current({type:"success",title:"Success",message:"Avatar updated successfully"}),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to update avatar"}),a}finally{r(!1)}}),[e]),p=(0,a.useCallback)((async e=>{r(!0),n(null);try{const t=await T.changePassword(e);return t.success&&c.current({type:"success",title:"Success",message:"Password changed successfully"}),t}catch(t){throw n(t),c.current({type:"error",title:"Error",message:"Failed to change password"}),t}finally{r(!1)}}),[]),g=(0,a.useCallback)((async s=>{r(!0),n(null);try{const a=await T.updatePreferences(s),r=e?{...e,notificationsEnabled:a}:null;return t(r),r&&(d.current={data:r,timestamp:Date.now()}),c.current({type:"success",title:"Success",message:"Preferences updated successfully"}),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to update preferences"}),a}finally{r(!1)}}),[e]),y=(0,a.useCallback)((async e=>{r(!0),n(null);try{return await T.getActivityLog(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:"Failed to fetch activity log"}),t}finally{r(!1)}}),[]);return(0,a.useEffect)((()=>{o.current||(o.current=!0,h())}),[h]),{profile:e,isLoading:s,error:i,fetchProfile:h,updateProfile:x,updateAvatar:f,changePassword:p,updatePreferences:g,getActivityLog:y}},z=()=>{const{user:e}=(0,c.A)(),{profile:t,isLoading:s,error:o,updateProfile:d,updateAvatar:m,getActivityLog:u}=H(),[h,x]=(0,a.useState)("profile"),[g,y]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)([]),[A,E]=(0,a.useState)(!1),[L,P]=(0,a.useState)({}),S=t?{...t,...L}:null;console.log("[ProfilePage] Render state:",{user:e,profileData:t,profileLoading:s,profileError:null===o||void 0===o?void 0:o.message,localProfileChanges:L}),(0,a.useEffect)((()=>{if("activity"===h&&u&&0===N.length){(async()=>{E(!0);try{const e=await u();k(e)}catch(e){console.error("Failed to load activity log:",e),k([])}finally{E(!1)}})()}}),[h,u,N.length]);const $=e=>{const{name:t,value:s}=e.target;P((e=>({...e,[t]:s})))},M=e=>{if(S)if(e.startsWith("notifications.")){const t=e.split(".")[1];if(t&&t in S.notificationsEnabled){const e=t;P((t=>({...t,notificationsEnabled:{...S.notificationsEnabled,...t.notificationsEnabled,[e]:!S.notificationsEnabled[e]}})))}}else if(e.startsWith("adminNotifications.")){const t=e.split(".")[1],s=S.adminNotifications;if(t&&s&&t in s){const e=t;P((t=>({...t,adminNotifications:{...s,...t.adminNotifications,[e]:!s[e]}})))}}else if(e in S){const t=e;P((e=>({...e,[t]:!S[t]})))}},F=()=>{console.log("Change password")},R=()=>{console.log("Sign out all devices")},D=async e=>{if(m)try{await m(e),P((e=>{const{avatar:t,...s}=e;return s}))}catch(t){console.error("Failed to update avatar:",t)}},O="Administrator"===(null===S||void 0===S?void 0:S.role);return s&&!S?(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsx)(l.A,{title:"My Profile",description:"View and manage your profile information and notification preferences"}),(0,f.jsx)(r.A,{children:(0,f.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,f.jsxs)("div",{className:"text-center",children:[(0,f.jsx)(n.A,{size:"md"}),(0,f.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Loading profile..."})]})})})]}):o&&!S?(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsx)(l.A,{title:"My Profile",description:"View and manage your profile information and notification preferences"}),(0,f.jsx)(r.A,{children:(0,f.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,f.jsxs)("div",{className:"text-center",children:[(0,f.jsx)("p",{className:"text-red-600 mb-2",children:"Failed to load profile"}),(0,f.jsx)("p",{className:"text-sm text-gray-500",children:o.message})]})})})]}):S?(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsx)(l.A,{title:"My Profile",description:"View and manage your profile information and notification preferences",actions:"profile"===h&&(g?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(i.A,{variant:"outline",onClick:()=>{y(!1),P({})},children:"Cancel"}),(0,f.jsx)(i.A,{onClick:async()=>{if(d&&S){j(!0);try{const e={name:S.name,email:S.email,phone:S.phone,...S.avatar&&S.avatar.trim()&&{avatar:S.avatar}};await d(e),P({}),y(!1)}catch(e){console.error("Failed to save profile:",e)}finally{j(!1)}}},loading:b,children:"Save Changes"})]}):(0,f.jsx)(i.A,{onClick:()=>y(!0),children:"Edit Profile"}))}),(0,f.jsxs)(r.A,{children:[(0,f.jsx)(p,{activeTab:h,onTabChange:x}),(0,f.jsx)("div",{className:"mt-6",children:(()=>{switch(h){case"security":return(0,f.jsx)(w,{profile:S,onToggleChange:M,onChangePassword:F,onSignOutAllDevices:R});case"notifications":return(0,f.jsx)(C,{profile:S,onToggleChange:M,isAdmin:O});case"activity":return(0,f.jsx)(I,{activities:N,isLoading:A});default:return(0,f.jsx)(v,{profile:S,isEditing:g,onInputChange:$,onAvatarChange:D})}})()})]})]}):null}},6365:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(5043);function r(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const i=a.forwardRef(r)},7012:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(5043);function r(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=a.forwardRef(r)},7907:(e,t,s)=>{s.d(t,{A:()=>n});var a=s(5043),r=s(579);const i=e=>{let{children:t,variant:s="primary",size:a="md",className:i="",onClick:n,disabled:l=!1,type:c="button",icon:o,iconPosition:d="left",fullWidth:m=!1,loading:u=!1,rounded:h=!1,href:x,target:f,rel:p,title:g,ariaLabel:y,testId:b}=e;const v=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[s]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[a]}\n    ${l?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${h?"rounded-full":"rounded-lg"}\n    ${i}\n  `,j=(0,r.jsxs)(r.Fragment,{children:[u&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o&&"left"===d&&!u&&(0,r.jsx)("span",{className:"mr-2",children:o}),t,o&&"right"===d&&(0,r.jsx)("span",{className:"ml-2",children:o})]});return x?(0,r.jsx)("a",{href:x,className:v,target:f,rel:p||("_blank"===f?"noopener noreferrer":void 0),onClick:n,title:g,"aria-label":y,"data-testid":b,children:j}):(0,r.jsx)("button",{type:c,className:v,onClick:n,disabled:l||u,title:g,"aria-label":y,"data-testid":b,children:j})},n=(0,a.memo)(i)}}]);
//# sourceMappingURL=188.3688804d.chunk.js.map