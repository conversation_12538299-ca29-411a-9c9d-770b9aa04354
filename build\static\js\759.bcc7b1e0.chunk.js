"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[759],{8946:(e,s,a)=>{a.r(s),a.d(s,{default:()=>y});var t=a(5043),c=a(3216),i=a(2806),o=a(3593),r=a(7907),d=a(8100),l=a(8662),n=a(9895),g=a(724),u=a(9705),C=a(579);const h=()=>{const e=(0,c.Zp)(),{categories:s,isLoading:a,fetchCategories:h}=(0,n.Ck)(),{showNotification:y}=(0,u.A)(),[p,k]=(0,t.useState)(!1),m=(0,t.useCallback)((s=>{e(g.b.getCategoryDetailsRoute(s.id))}),[e]),b=(0,t.useCallback)((async e=>{try{k(!1),await h(),y({type:"success",title:"Success",message:"Category added successfully"})}catch(s){y({type:"error",title:"Error",message:"Failed to add category"})}}),[h,y]);return(0,C.jsxs)("div",{className:"space-y-6",children:[(0,C.jsx)(i.A,{title:"Categories",description:"Manage product categories",actions:(0,C.jsx)(r.A,{icon:(0,C.jsx)(l.A,{className:"h-5 w-5"}),onClick:()=>k(!0),children:"Add Category"})}),(0,C.jsx)(o.A,{children:(0,C.jsx)(n.h4,{categories:s,onCategoryClick:m,loading:a})}),(0,C.jsx)(d.A,{isOpen:p,onClose:()=>k(!1),title:"Add Category",children:(0,C.jsx)(n.W9,{onSubmit:b,onCancel:()=>k(!1)})})]})},y=(0,t.memo)(h)}}]);
//# sourceMappingURL=759.bcc7b1e0.chunk.js.map