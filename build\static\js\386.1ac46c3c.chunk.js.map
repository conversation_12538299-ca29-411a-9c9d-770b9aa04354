{"version": 3, "file": "static/js/386.1ac46c3c.chunk.js", "mappings": "sJAQA,MAWA,EAX8CA,IAGvC,IAHwC,SAC7CC,EAAQ,UACRC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,MAAID,UAAW,kCAAkCA,IAAYD,SAC1DA,GACE,C,gDCdT,SAASG,EAASJ,EAIfK,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBL,E,uDCflD,MAaA,EAb8CJ,IAIvC,IAJwC,MAC7CsB,EAAK,MACLC,EAAK,UACLrB,EAAY,IACbF,EACC,OACEwB,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,wDAAwDA,IAAYD,SAAA,EAClFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEqB,KACnDnB,EAAAA,EAAAA,KAAA,MAAID,UAAU,mDAAkDD,SAAEsB,MAC9D,C,gDCjBV,SAASE,EAAazB,EAInBK,GAAQ,IAJY,MACrBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBgB,E,gDCvBlD,SAASC,EAAS1B,EAIfK,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiB,E,yPCvBlD,SAASC,EAAuB3B,EAI7BK,GAAQ,IAJsB,MAC/BC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qLAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBkB,G,aCQlD,MAsYA,EAtYmCC,KACjC,MAAM,GAAEV,IAAOW,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,MACXC,EAAUd,GAAM,IAEhB,aAAEe,EAAY,YAAEC,IAAgBC,EAAAA,EAAAA,MAC/BC,EAAOC,IAAYC,EAAAA,EAAAA,UAAuB,OAC1CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAmBC,IAAwBN,EAAAA,EAAAA,WAAS,IACpDO,EAAYC,IAAiBR,EAAAA,EAAAA,WAAS,IAE7CS,EAAAA,EAAAA,YAAU,KACR,IAAKf,EAGH,OAFAQ,GAAa,QACbE,EAAS,wBAIYM,WACrB,IACER,GAAa,GACbE,EAAS,MACT,MAAMO,QAAkBhB,EAAaD,GACrCK,EAASY,EACX,CAAE,MAAOR,GACPS,QAAQT,MAAM,6BAA8BA,GAC5C,MAAMU,EAAeV,aAAiBW,MAAQX,EAAMY,QAAU,6BAC9DX,EAASS,EACX,CAAC,QACCX,GAAa,EACf,GAGFc,EAAgB,GACf,CAACtB,EAASC,IAGb,MAsCMsB,EAAyBA,KAC7BX,GAAqB,EAAM,EAG7B,OAAIL,GAEApC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gDAA+CD,UAC5DE,EAAAA,EAAAA,KAACqD,EAAAA,EAAc,CAACC,KAAK,SAKvBhB,IAAUL,GAEVZ,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oEAAmED,SAAA,EAChFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mCAAkCD,SAAC,yBAClDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,SAAEwC,KAChCtC,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QAASA,IAAM7B,GAAU,GACzB8B,MAAMzD,EAAAA,EAAAA,KAACsB,EAAAA,EAAa,CAACvB,UAAU,YAC/B2D,QAAQ,UAAS5D,SAClB,eAOFmC,GAgBHZ,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAAC2D,EAAAA,EAAU,CACTxD,MAAO,UAAU8B,EAAMlB,KACvB6C,YAAY,yCACZC,YAAa,CACX,CAAE1C,MAAO,SAAU2C,KAAM,WACzB,CAAE3C,MAAO,UAAUc,EAAMlB,OAE3BgD,SACE1C,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QA5DkBQ,KAC5BvB,GAAqB,EAAK,EA4DhBgB,MAAMzD,EAAAA,EAAAA,KAACiE,EAAAA,EAAS,CAAClE,UAAU,YAC3B2D,QAAQ,SACRQ,SAAUxB,EAAW5C,SACtB,kBAGDE,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QAASA,IAAM7B,GAAU,GACzB8B,MAAMzD,EAAAA,EAAAA,KAACsB,EAAAA,EAAa,CAACvB,UAAU,YAC/B2D,QAAQ,UAAS5D,SAClB,kBAQPE,EAAAA,EAAAA,KAACmE,EAAAA,EAAI,CAACpE,UAAU,6DAA4DD,UAC1EuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,sFAAqFD,SAAA,EAClGuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8BAA6BD,SAAA,CAlH7BsE,KACrB,OAAOA,GACL,IAAK,UACH,OAAOpE,EAAAA,EAAAA,KAACqE,EAAAA,EAAS,CAACtE,UAAU,4BAC9B,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAACsE,EAAAA,EAAe,CAACvE,UAAU,0BACpC,IAAK,YACH,OAAOC,EAAAA,EAAAA,KAACuB,EAAAA,EAAS,CAACxB,UAAU,2BAC9B,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAACuE,EAAAA,EAAW,CAACxE,UAAU,yBAChC,QACE,OAAO,KACX,EAuGSyE,CAAcvC,EAAMmC,SACrB/C,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEuB,EAAAA,EAAAA,MAAA,MAAItB,UAAU,sCAAqCD,SAAA,CAAC,UAAQmC,EAAMlB,OAClEf,EAAAA,EAAAA,KAACyE,EAAAA,EAAW,CAACL,OAAQnC,EAAMmC,OAAQM,KAAK,iBAG5CrD,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,aAAYD,SAAA,EACzBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SAAC,kBACvCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kCAAiCD,UAAE6E,EAAAA,EAAAA,IAAe1C,EAAM2C,wBAK7EvD,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wCAAuCD,SAAA,EAEpDE,EAAAA,EAAAA,KAAC6E,EAAAA,EAAa,CACZ1E,MAAM,uBACNyD,YAAY,mDAAkD9D,UAE9DuB,EAAAA,EAAAA,MAACyD,EAAAA,EAAU,CAAAhF,SAAA,EACTE,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAAC5D,MAAM,gBAAgBC,MAAOa,EAAM+C,gBAC/ChF,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAAC5D,MAAM,aAAaC,OAAO6D,EAAAA,EAAAA,IAAWhD,EAAMiD,cACvDlF,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAAC5D,MAAM,gBAAgBC,OAAO6D,EAAAA,EAAAA,IAAWhD,EAAMkD,sBAK9DnF,EAAAA,EAAAA,KAAC6E,EAAAA,EAAa,CACZ1E,MAAM,uBACNyD,YAAY,mDAAkD9D,UAE9DuB,EAAAA,EAAAA,MAACyD,EAAAA,EAAU,CAAAhF,SAAA,EACTE,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAAC5D,MAAM,gBAAgBC,MAAOa,EAAMmD,gBAC/CpF,EAAAA,EAAAA,KAAC+E,EAAAA,EAAU,CAAC5D,MAAM,eAAeC,OAAOpB,EAAAA,EAAAA,KAACyE,EAAAA,EAAW,CAACL,OAAQnC,EAAMmC,OAAQM,KAAK,oBAMrFzC,EAAMoD,OAASpD,EAAMoD,MAAMC,OAAS,IACnCjE,EAAAA,EAAAA,MAACwD,EAAAA,EAAa,CACZ1E,MAAM,cACNyD,YAAY,+CAA8C9D,SAAA,EAE1DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC9BuB,EAAAA,EAAAA,MAAA,SAAOtB,UAAU,sCAAqCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,UAC3BuB,EAAAA,EAAAA,MAAA,MAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,aAG3GE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,SAG3GE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,cAG3GE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,gBAG3GE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,WAG3GE,EAAAA,EAAAA,KAAA,MAAIuF,MAAM,MAAMxF,UAAU,iFAAgFD,SAAC,gBAK/GE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,oCAAmCD,SACjDmC,EAAMoD,MAAMG,KAAKC,IAChBpE,EAAAA,EAAAA,MAAA,MAAkBtB,UAAU,mBAAkBD,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCuB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SAAE2F,EAAKC,OACxDD,EAAK7B,cACJ5D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SAAE2F,EAAK7B,oBAInD5D,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,SAC9D2F,EAAKE,KAAO,SAEf3F,EAAAA,EAAAA,KAAA,MAAID,UAAU,gEAA+DD,SAC1E2F,EAAKG,YAER5F,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,UAC9D6E,EAAAA,EAAAA,IAAec,EAAKI,cAEvB7F,EAAAA,EAAAA,KAAA,MAAID,UAAU,gEAA+DD,UAC1E6E,EAAAA,EAAAA,IAAec,EAAKG,SAAWH,EAAKI,cAEvC7F,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oEAAmED,UAChFE,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAACF,UAAU,gCAvBlB0F,EAAK1E,cAiCtBf,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iCAAgCD,UAC7CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,UAChDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,+BAA8BD,SAAA,EAC3CE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,eAChCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,UAAE6E,EAAAA,EAAAA,IAAe1C,EAAM2C,mBAExDvD,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,+BAA8BD,SAAA,EAC3CE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,UAChCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,iBAElCuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,+BAA8BD,SAAA,EAC3CE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,eAChCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,aAElCuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,2DAA0DD,SAAA,EACvEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,YAChCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,yBAAwBD,UAAE6E,EAAAA,EAAAA,IAAe1C,EAAM2C,6BAS3EvD,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wCAAuCD,SAAA,CAEnDmC,EAAM6D,kBACL9F,EAAAA,EAAAA,KAAC6E,EAAAA,EAAa,CACZ1E,MAAM,mBACNyD,YAAY,qCAAoC9D,UAEhDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,4CAA2CD,SAAA,EACxDE,EAAAA,EAAAA,KAAA,OAAAF,SAAMmC,EAAM6D,gBAAgBC,UAC5B1E,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,CAAMmC,EAAM6D,gBAAgBE,KAAK,KAAG/D,EAAM6D,gBAAgBG,MAAM,IAAEhE,EAAM6D,gBAAgBI,eACxFlG,EAAAA,EAAAA,KAAA,OAAAF,SAAMmC,EAAM6D,gBAAgBK,eAMjClE,EAAMmE,iBACLpG,EAAAA,EAAAA,KAAC6E,EAAAA,EAAa,CACZ1E,MAAM,kBACNyD,YAAY,qCAAoC9D,UAEhDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,4CAA2CD,SAAA,EACxDE,EAAAA,EAAAA,KAAA,OAAAF,SAAMmC,EAAMmE,eAAeL,UAC3B1E,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,CAAMmC,EAAMmE,eAAeJ,KAAK,KAAG/D,EAAMmE,eAAeH,MAAM,IAAEhE,EAAMmE,eAAeF,eACrFlG,EAAAA,EAAAA,KAAA,OAAAF,SAAMmC,EAAMmE,eAAeD,kBAOlClE,EAAMoE,QACLrG,EAAAA,EAAAA,KAAC6E,EAAAA,EAAa,CACZ1E,MAAM,cACNyD,YAAY,kDAAiD9D,UAE7DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACxBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uDAAsDD,UACnEE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SAAEmC,EAAMoE,eAOpDrG,EAAAA,EAAAA,KAACsG,EAAAA,EAAK,CACJC,OAAQ/D,EACRgE,QAASpD,EACTjD,OACEkB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAC5BE,EAAAA,EAAAA,KAACwB,EAAuB,CAACzB,UAAU,4BAErCC,EAAAA,EAAAA,KAAA,OAAAF,UACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAC,sBAIxDwD,KAAK,KACLmD,QACEpF,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QAASJ,EACTM,QAAQ,UACRQ,SAAUxB,EAAW5C,SACtB,YAGDE,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QAzScX,UACxB,GAAKZ,EAAL,CAEAU,GAAc,GACd,UACQZ,EAAYE,EAAMlB,IACxB0B,GAAqB,GACrBd,EAAS,UAAW,CAAE+E,SAAS,GACjC,CAAE,MAAOpE,GAEPS,QAAQT,MAAM,0BAA2BA,EAC3C,CAAC,QACCK,GAAc,EAChB,CAZkB,CAYlB,EA6RUe,QAAQ,SACRiD,QAASjE,EACTe,MAAMzD,EAAAA,EAAAA,KAACiE,EAAAA,EAAS,CAAClE,UAAU,YAAaD,SAEvC4C,EAAa,cAAgB,oBAGnC5C,UAEDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBuB,EAAAA,EAAAA,MAAA,KAAGtB,UAAU,wBAAuBD,SAAA,CAAC,0CACGuB,EAAAA,EAAAA,MAAA,UAAAvB,SAAA,CAAQ,IAAO,OAALmC,QAAK,IAALA,OAAK,EAALA,EAAOlB,MAAY,sCAErEf,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iDAAgDD,UAC7DuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,UAC5BE,EAAAA,EAAAA,KAACwB,EAAuB,CAACzB,UAAU,4BAErCsB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,mCAAkCD,SAAC,aAGjDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,UACxCuB,EAAAA,EAAAA,MAAA,MAAItB,UAAU,2BAA0BD,SAAA,EACtCE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,oEACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,sDACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,yDAhRlBuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oEAAmED,SAAA,EAChFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SAAC,qBACnDE,EAAAA,EAAAA,KAACuD,EAAAA,EAAM,CACLC,QAASA,IAAM7B,GAAU,GACzB8B,MAAMzD,EAAAA,EAAAA,KAACsB,EAAAA,EAAa,CAACvB,UAAU,YAC/B2D,QAAQ,UAAS5D,SAClB,cAkRC,C,uDCxZV,MAqBA,EArBoDD,IAK7C,IAL8C,MACnDM,EAAK,YACLyD,EAAW,SACX9D,EAAQ,UACRC,EAAY,IACbF,EACC,OACEwB,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,iDAAiDA,IAAYD,SAAA,EAC3EuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAEK,IAC5DyD,IACC5D,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAE8D,QAGzD5D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,SACtCA,MAEC,C,gDC1BV,SAASuE,EAASxE,EAIfK,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB+D,E,gDCvBlD,SAASE,EAAW1E,EAIjBK,GAAQ,IAJU,MACnBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiE,E,6ECMlD,MAAM+B,EAA8BzG,IAiB7B,IAjB8B,OACnC0G,EAAM,QACNC,EAAO,MACPrG,EAAK,SACLL,EAAQ,KACRwD,EAAO,KAAI,OACXmD,EAAM,WACNG,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfhH,EAAY,GAAE,cACdiH,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtBC,GACDvH,EACC,MAAMwH,GAAWC,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDA1E,EAAAA,EAAAA,YAAU,KACR,MAAM2E,EAAgBC,IAChBZ,GAAwB,WAAVY,EAAEC,KAClBjB,GACF,EASF,OANID,IACFmB,SAASC,iBAAiB,UAAWJ,GAErCG,SAASE,KAAKC,MAAMC,SAAW,UAG1B,KACLJ,SAASK,oBAAoB,UAAWR,GACxCG,SAASE,KAAKC,MAAMC,SAAW,MAAM,CACtC,GACA,CAACvB,EAAQC,EAASI,KAGrBhE,EAAAA,EAAAA,YAAU,KACR,IAAK2D,IAAWc,EAASW,QAAS,OAElC,MAAMC,EAAoBZ,EAASW,QAAQE,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkB3C,OAAc,OAEpC,MAAM6C,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkB3C,OAAS,GAE3D+C,EAAgBb,IACN,QAAVA,EAAEC,MAEFD,EAAEc,SACAZ,SAASa,gBAAkBJ,IAC7BC,EAAYI,QACZhB,EAAEiB,kBAGAf,SAASa,gBAAkBH,IAC7BD,EAAaK,QACbhB,EAAEiB,kBAEN,EAMF,OAHAf,SAASC,iBAAiB,UAAWU,GACrCF,EAAaK,QAEN,KACLd,SAASK,oBAAoB,UAAWM,EAAa,CACtD,GACA,CAAC9B,KAECA,EAAQ,OAAO,KAGpB,MAUMmC,GACJrH,EAAAA,EAAAA,MAACsH,EAAAA,SAAQ,CAAA7I,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACED,UAAW,gEAAgEoH,IAC3E3D,QAASqD,EAAuBL,OAAUoC,EAC1C,cAAa,GAAGxB,gBAIlBpH,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UACjDE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yBAAyBgH,EAAW,SAAW,yCAAyCjH,UACtGuB,EAAAA,EAAAA,MAAA,OACEP,IAAKuG,EACLtH,UAAW,GAxBD,CAClB8I,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4B5F,2GAA8GvD,IACxIyD,QAAUgE,GAAMA,EAAE2B,kBAClB,cAAa/B,EAAOtH,SAAA,EAGpBuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,wEAAwEkH,IAAkBnH,SAAA,CACtF,kBAAVK,GACNH,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAEK,IAErDA,EAED2G,IACC9G,EAAAA,EAAAA,KAAA,UACE0E,KAAK,SACL3E,UAAU,wGACVyD,QAASgD,EACT,aAAW,cACX,cAAa,GAAGY,iBAAsBtH,UAEtCE,EAAAA,EAAAA,KAACoJ,EAAAA,EAAS,CAACrJ,UAAU,kBAM3BC,EAAAA,EAAAA,KAAA,OAAKD,UAAW,aAAaiH,IAAgBlH,SAC1CA,IAIF2G,IACCzG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4EAA4EmH,IAAkBpH,SAC3G2G,cAUf,OAAO4C,EAAAA,EAAAA,cAAaX,EAAchB,SAASE,KAAK,EAGlD,GAAe0B,EAAAA,EAAAA,MAAKhD,E,gDClLpB,SAASrC,EAASpE,EAIfK,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2D,E", "sources": ["components/common/DetailList.tsx", "../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/DetailItem.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "pages/OrderDetailsPage.tsx", "components/common/DetailSection.tsx", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailListProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailList: React.FC<DetailListProps> = ({\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <dl className={`sm:divide-y sm:divide-gray-200 ${className}`}>\r\n      {children}\r\n    </dl>\r\n  );\r\n};\r\n\r\nexport default DetailList;", "import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailItemProps {\r\n  label: string;\r\n  value: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailItem: React.FC<DetailItemProps> = ({\r\n  label,\r\n  value,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${className}`}>\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{value}</dd>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailItem;", "import * as React from \"react\";\nfunction ArrowLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowLeftIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;", "/**\r\n * Order Details Page\r\n * \r\n * A comprehensive page for displaying detailed order information.\r\n * This page can be accessed from multiple locations (User Edit Page, Orders Page, etc.)\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport Card from '../components/common/Card';\r\nimport Button from '../components/common/Button';\r\nimport Modal from '../components/common/Modal';\r\nimport StatusBadge from '../components/common/StatusBadge';\r\nimport DetailSection from '../components/common/DetailSection';\r\nimport DetailList from '../components/common/DetailList';\r\nimport DetailItem from '../components/common/DetailItem';\r\nimport { useOrders } from '../features/orders/index';\r\nimport { formatCurrency, formatDate } from '../utils/formatters';\r\nimport {\r\n  CheckCircleIcon,\r\n  ClockIcon,\r\n  XCircleIcon,\r\n  TruckIcon,\r\n  PhotoIcon,\r\n  ArrowLeftIcon,\r\n  TrashIcon,\r\n  ExclamationTriangleIcon\r\n} from '@heroicons/react/24/outline';\r\nimport type { Order, OrderItem } from '../features/orders/types';\r\n\r\nconst OrderDetailsPage: React.FC = () => {\r\n  const { id } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const orderId = id || '';\r\n\r\n  const { getOrderById, deleteOrder } = useOrders();\r\n  const [order, setOrder] = useState<Order | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!orderId) {\r\n      setIsLoading(false);\r\n      setError('No order ID provided');\r\n      return;\r\n    }\r\n\r\n    const fetchOrderData = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        const orderData = await getOrderById(orderId);\r\n        setOrder(orderData);\r\n      } catch (error) {\r\n        console.error('Error fetching order data:', error);\r\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch order data';\r\n        setError(errorMessage);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchOrderData();\r\n  }, [orderId, getOrderById]);\r\n\r\n  // Helper function to get status icon\r\n  const getStatusIcon = (status: string) => {\r\n    switch(status) {\r\n      case 'pending':\r\n        return <ClockIcon className=\"w-5 h-5 text-yellow-500\" />;\r\n      case 'approved':\r\n        return <CheckCircleIcon className=\"w-5 h-5 text-blue-500\" />;\r\n      case 'completed':\r\n        return <TruckIcon className=\"w-5 h-5 text-green-500\" />;\r\n      case 'rejected':\r\n        return <XCircleIcon className=\"w-5 h-5 text-red-500\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Handle delete order\r\n  const handleDeleteOrder = async () => {\r\n    if (!order) return;\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      await deleteOrder(order.id);\r\n      setIsDeleteModalOpen(false);\r\n      navigate('/orders', { replace: true });\r\n    } catch (error) {\r\n      // Error is already handled by the hook with notifications\r\n      console.error('Failed to delete order:', error);\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Handle opening delete confirmation modal\r\n  const handleOpenDeleteModal = () => {\r\n    setIsDeleteModalOpen(true);\r\n  };\r\n\r\n  // Handle closing delete confirmation modal\r\n  const handleCloseDeleteModal = () => {\r\n    setIsDeleteModalOpen(false);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center min-h-screen\">\r\n        <LoadingSpinner size=\"lg\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error && !order) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n        <div className=\"text-red-600 text-lg font-medium\">Error Loading Order</div>\r\n        <div className=\"text-gray-600\">{error}</div>\r\n        <Button\r\n          onClick={() => navigate(-1)}\r\n          icon={<ArrowLeftIcon className=\"w-4 h-4\" />}\r\n          variant=\"outline\"\r\n        >\r\n          Go Back\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!order) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n        <div className=\"text-gray-600 text-lg font-medium\">Order not found</div>\r\n        <Button\r\n          onClick={() => navigate(-1)}\r\n          icon={<ArrowLeftIcon className=\"w-4 h-4\" />}\r\n          variant=\"outline\"\r\n        >\r\n          Go Back\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title={`Order #${order.id}`}\r\n        description=\"Complete order information and details\"\r\n        breadcrumbs={[\r\n          { label: 'Orders', path: '/orders' },\r\n          { label: `Order #${order.id}` }\r\n        ]}\r\n        actions={\r\n          <div className=\"flex space-x-3\">\r\n            <Button\r\n              onClick={handleOpenDeleteModal}\r\n              icon={<TrashIcon className=\"w-4 h-4\" />}\r\n              variant=\"danger\"\r\n              disabled={isDeleting}\r\n            >\r\n              Delete Order\r\n            </Button>\r\n            <Button\r\n              onClick={() => navigate(-1)}\r\n              icon={<ArrowLeftIcon className=\"w-4 h-4\" />}\r\n              variant=\"outline\"\r\n            >\r\n              Go Back\r\n            </Button>\r\n          </div>\r\n        }\r\n      />\r\n\r\n      {/* Order Status and Summary */}\r\n      <Card className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            {getStatusIcon(order.status)}\r\n            <div>\r\n              <h2 className=\"text-xl font-semibold text-gray-900\">Order #{order.id}</h2>\r\n              <StatusBadge status={order.status} type=\"order\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <div className=\"text-sm text-gray-500\">Total Amount</div>\r\n            <div className=\"text-2xl font-bold text-primary\">{formatCurrency(order.totalAmount)}</div>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Customer Information */}\r\n        <DetailSection\r\n          title=\"Customer Information\"\r\n          description=\"Details about the customer who placed this order\"\r\n        >\r\n          <DetailList>\r\n            <DetailItem label=\"Customer Name\" value={order.customerName} />\r\n            <DetailItem label=\"Order Date\" value={formatDate(order.orderDate)} />\r\n            <DetailItem label=\"Delivery Date\" value={formatDate(order.deliveryDate)} />\r\n          </DetailList>\r\n        </DetailSection>\r\n\r\n        {/* Supplier Information */}\r\n        <DetailSection\r\n          title=\"Supplier Information\"\r\n          description=\"Details about the supplier fulfilling this order\"\r\n        >\r\n          <DetailList>\r\n            <DetailItem label=\"Supplier Name\" value={order.supplierName} />\r\n            <DetailItem label=\"Order Status\" value={<StatusBadge status={order.status} type=\"order\" />} />\r\n          </DetailList>\r\n        </DetailSection>\r\n      </div>\r\n\r\n      {/* Order Items */}\r\n      {order.items && order.items.length > 0 && (\r\n        <DetailSection\r\n          title=\"Order Items\"\r\n          description=\"Products and services included in this order\"\r\n        >\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full divide-y divide-gray-200\">\r\n              <thead className=\"bg-gray-50\">\r\n                <tr>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Product\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    SKU\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Quantity\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Unit Price\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Total\r\n                  </th>\r\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Image\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                {order.items.map((item: OrderItem) => (\r\n                  <tr key={item.id} className=\"hover:bg-gray-50\">\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div>\r\n                        <div className=\"text-sm font-medium text-gray-900\">{item.name}</div>\r\n                        {item.description && (\r\n                          <div className=\"text-sm text-gray-500\">{item.description}</div>\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                      {item.sku || 'N/A'}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium\">\r\n                      {item.quantity}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                      {formatCurrency(item.unitPrice)}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                      {formatCurrency(item.quantity * item.unitPrice)}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg\">\r\n                        <PhotoIcon className=\"w-6 h-6 text-gray-400\" />\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          {/* Order Summary */}\r\n          <div className=\"mt-6 bg-gray-50 rounded-lg p-6\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Subtotal:</span>\r\n                  <span className=\"text-gray-900\">{formatCurrency(order.totalAmount)}</span>\r\n                </div>\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Tax:</span>\r\n                  <span className=\"text-gray-900\">Included</span>\r\n                </div>\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Shipping:</span>\r\n                  <span className=\"text-gray-900\">Free</span>\r\n                </div>\r\n                <div className=\"border-t pt-2 flex justify-between text-base font-medium\">\r\n                  <span className=\"text-gray-900\">Total:</span>\r\n                  <span className=\"text-primary font-bold\">{formatCurrency(order.totalAmount)}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </DetailSection>\r\n      )}\r\n\r\n      {/* Additional Information */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Shipping Address */}\r\n        {order.shippingAddress && (\r\n          <DetailSection\r\n            title=\"Shipping Address\"\r\n            description=\"Where this order will be delivered\"\r\n          >\r\n            <div className=\"px-4 py-5 text-sm text-gray-900 space-y-1\">\r\n              <div>{order.shippingAddress.street}</div>\r\n              <div>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}</div>\r\n              <div>{order.shippingAddress.country}</div>\r\n            </div>\r\n          </DetailSection>\r\n        )}\r\n\r\n        {/* Billing Address */}\r\n        {order.billingAddress && (\r\n          <DetailSection\r\n            title=\"Billing Address\"\r\n            description=\"Billing information for this order\"\r\n          >\r\n            <div className=\"px-4 py-5 text-sm text-gray-900 space-y-1\">\r\n              <div>{order.billingAddress.street}</div>\r\n              <div>{order.billingAddress.city}, {order.billingAddress.state} {order.billingAddress.postalCode}</div>\r\n              <div>{order.billingAddress.country}</div>\r\n            </div>\r\n          </DetailSection>\r\n        )}\r\n      </div>\r\n\r\n      {/* Order Notes */}\r\n      {order.notes && (\r\n        <DetailSection\r\n          title=\"Order Notes\"\r\n          description=\"Additional information and special instructions\"\r\n        >\r\n          <div className=\"px-4 py-5\">\r\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\r\n              <p className=\"text-sm text-gray-900\">{order.notes}</p>\r\n            </div>\r\n          </div>\r\n        </DetailSection>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal\r\n        isOpen={isDeleteModalOpen}\r\n        onClose={handleCloseDeleteModal}\r\n        title={\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"flex-shrink-0\">\r\n              <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Delete Order</h3>\r\n            </div>\r\n          </div>\r\n        }\r\n        size=\"sm\"\r\n        footer={\r\n          <div className=\"flex space-x-3\">\r\n            <Button\r\n              onClick={handleCloseDeleteModal}\r\n              variant=\"outline\"\r\n              disabled={isDeleting}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              onClick={handleDeleteOrder}\r\n              variant=\"danger\"\r\n              loading={isDeleting}\r\n              icon={<TrashIcon className=\"w-4 h-4\" />}\r\n            >\r\n              {isDeleting ? 'Deleting...' : 'Delete Order'}\r\n            </Button>\r\n          </div>\r\n        }\r\n      >\r\n        <div className=\"space-y-4\">\r\n          <p className=\"text-sm text-gray-600\">\r\n            Are you sure you want to delete order <strong>#{order?.id}</strong>? This action cannot be undone.\r\n          </p>\r\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\r\n            <div className=\"flex\">\r\n              <div className=\"flex-shrink-0\">\r\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\r\n              </div>\r\n              <div className=\"ml-3\">\r\n                <h3 className=\"text-sm font-medium text-red-800\">\r\n                  Warning\r\n                </h3>\r\n                <div className=\"mt-2 text-sm text-red-700\">\r\n                  <ul className=\"list-disc pl-5 space-y-1\">\r\n                    <li>This will permanently delete the order and all associated data</li>\r\n                    <li>Customer and supplier records will remain intact</li>\r\n                    <li>This action cannot be reversed</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrderDetailsPage;\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailSection: React.FC<DetailSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-white shadow overflow-hidden sm:rounded-lg ${className}`}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"border-t border-gray-200\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailSection;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "className", "_jsx", "PhotoIcon", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "label", "value", "_jsxs", "ArrowLeftIcon", "TruckIcon", "ExclamationTriangleIcon", "OrderDetailsPage", "useParams", "navigate", "useNavigate", "orderId", "getOrderById", "deleteOrder", "useOrders", "order", "setOrder", "useState", "isLoading", "setIsLoading", "error", "setError", "isDeleteModalOpen", "setIsDeleteModalOpen", "isDeleting", "setIsDeleting", "useEffect", "async", "orderData", "console", "errorMessage", "Error", "message", "fetchOrderData", "handleCloseDeleteModal", "LoadingSpinner", "size", "<PERSON><PERSON>", "onClick", "icon", "variant", "<PERSON><PERSON><PERSON><PERSON>", "description", "breadcrumbs", "path", "actions", "handleOpenDeleteModal", "TrashIcon", "disabled", "Card", "status", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "getStatusIcon", "StatusBadge", "type", "formatCurrency", "totalAmount", "DetailSection", "DetailList", "DetailItem", "customerName", "formatDate", "orderDate", "deliveryDate", "supplierName", "items", "length", "scope", "map", "item", "name", "sku", "quantity", "unitPrice", "shippingAddress", "street", "city", "state", "postalCode", "country", "billing<PERSON><PERSON>ress", "notes", "Modal", "isOpen", "onClose", "footer", "replace", "loading", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "testId", "modalRef", "useRef", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "current", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "undefined", "xs", "sm", "md", "lg", "xl", "full", "stopPropagation", "XMarkIcon", "createPortal", "memo"], "sourceRoot": ""}