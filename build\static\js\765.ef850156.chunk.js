"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[765],{1512:(e,r,t)=>{t.d(r,{A:()=>i});var a=t(5043),s=t(4703),n=t(9705);const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{enableNotifications:r=!0,enableReporting:t=!0,onError:i}=e,{showNotification:o}=(0,n.A)(),[l,c]=(0,a.useState)({hasError:!1,error:null,errorType:null}),d=(0,a.useCallback)((()=>{c({hasError:!1,error:null,errorType:null})}),[]),m=(0,a.useCallback)(((e,a)=>{const n=(0,s.hS)(e,r?e=>{o({type:e.type,title:e.title,message:e.message})}:void 0);return c({hasError:!0,error:n,errorType:"api",...a&&{context:a}}),t&&e instanceof Error&&(0,s.N7)(e,a),i&&i(e,a),n}),[r,t,o,i]),u=(0,a.useCallback)(((e,t,a,n)=>{const l=(0,s.co)(e,t,a);return c({hasError:!0,error:l,errorType:"validation",...n&&{context:n}}),r&&o({type:"error",title:"Validation Error",message:l.message}),i&&i(l,n),l}),[r,o,i]),g=(0,a.useCallback)(((e,a,n)=>{(0,s.NC)(e,a,r?e=>{o({type:e.type,title:e.title,message:e.message})}:void 0),c({hasError:!0,error:e,errorType:"form",...n&&{context:n}}),t&&e instanceof Error&&(0,s.N7)(e,n),i&&i(e,n)}),[r,t,o,i]),h=(0,a.useCallback)(((e,a)=>{const n=e instanceof Error?e:new Error(String(e));return c({hasError:!0,error:n,errorType:"general",...a&&{context:a}}),r&&o({type:"error",title:"Error",message:n.message}),t&&(0,s.N7)(n,a),(0,s.vV)(n,a),i&&i(e,a),n}),[r,t,o,i]),x=(0,a.useCallback)((async(e,r)=>{try{return d(),await e()}catch(t){return m(t,r),null}}),[d,m]),p=(0,a.useCallback)((async(e,r,t)=>{try{return d(),await e()}catch(a){return g(a,r,t),null}}),[d,g]);return{...l,handleApiError:m,handleValidationError:u,handleFormError:g,handleGeneralError:h,clearError:d,withErrorHandling:x,withFormErrorHandling:p,isApiError:e=>e&&"object"===typeof e&&"status"in e,isValidationError:e=>e&&"object"===typeof e&&"field"in e}}},3619:(e,r,t)=>{t.d(r,{Ay:()=>c});var a=t(5043),s=t(7515),n=t(4703),i=t(579);const o=e=>{let{error:r,resetError:t}=e;return(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50",children:[(0,i.jsx)("div",{className:"text-red-500 text-2xl mb-2",children:"\u26a0\ufe0f"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,i.jsx)("p",{className:"text-red-600 text-sm mb-4 text-center max-w-md",children:r.message||"An unexpected error occurred"}),(0,i.jsx)("button",{onClick:t,className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Try Again"})]})};function l(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{fallback:t=o,onError:l,enableReporting:c=!0,context:d}=r,m=(0,a.forwardRef)(((r,a)=>(0,i.jsx)(s.A,{fallback:(0,i.jsx)(t,{error:new Error,resetError:()=>window.location.reload()}),onError:(r,t)=>{c&&(0,n.N7)(r,d||e.displayName||e.name,{componentStack:t.componentStack,errorBoundary:!0}),l&&l(r,t)},children:(0,i.jsx)(e,{...r,ref:a})})));return m.displayName=`withErrorBoundary(${e.displayName||e.name})`,m}const c=l},6765:(e,r,t)=>{t.r(r),t.d(r,{default:()=>x});var a=t(5043),s=t(3216),n=t(7752),i=t(9705),o=t(7907),l=t(724),c=t(5870),d=t(6773),m=t(1512),u=t(4703),g=t(3619),h=t(579);const x=(0,g.Ay)((()=>{var e;const[r,t]=(0,a.useState)({email:"",password:"",rememberMe:!1}),[g,x]=(0,a.useState)({}),[p,b]=(0,a.useState)(!1),{login:v}=(0,n.A)(),{showError:y,showSuccess:f}=(0,i.A)(),w=(0,s.Zp)(),j=(0,s.zy)(),{handleFormError:N,withFormErrorHandling:k,clearError:A}=(0,m.A)({enableNotifications:!0,enableReporting:!0}),E=j.state,C=(null===E||void 0===E||null===(e=E.from)||void 0===e?void 0:e.pathname)||l.b.DASHBOARD,P=e=>{const{name:r,value:a,type:s,checked:n}=e.target;t((e=>({...e,[r]:"checkbox"===s?n:a}))),g[r]&&x((e=>({...e,[r]:""})))};return(0,h.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,h.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-center text-3xl font-bold bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent",children:"ConnectChain"}),(0,h.jsx)("h2",{className:"mt-6 text-center text-2xl font-bold text-gray-800",children:"Admin Panel"}),(0,h.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to your account to access the admin dashboard"})]}),(0,h.jsxs)("div",{className:"mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 border border-gray-100",children:[(0,h.jsxs)("form",{className:"space-y-6",onSubmit:async e=>{e.preventDefault(),A();const t=(0,d.l)({email:r.email,password:r.password},{email:[d.tU.required(),d.tU.email()],password:[d.tU.required()]});if(Object.keys(t).length>0)return void x(t);b(!0);const a=await k((async()=>(await v({email:r.email,password:r.password,rememberMe:r.rememberMe}),f("Login successful"),w(C,{replace:!0}),!0)),((e,r)=>{x((t=>({...t,[e]:r})))}),"User Login");b(!1),a||console.error("Login failed")},children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,h.jsxs)("div",{className:"mt-1",children:[(0,h.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r.email,onChange:P,className:`appearance-none block w-full px-3 py-2 border ${g.email?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm`,placeholder:"<EMAIL>"}),g.email&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.email})]})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,h.jsxs)("div",{className:"mt-1",children:[(0,h.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:r.password,onChange:P,className:`appearance-none block w-full px-3 py-2 border ${g.password?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm`,placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"}),g.password&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.password})]})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:r.rememberMe,onChange:P,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,h.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),(0,h.jsx)("div",{className:"text-sm",children:(0,h.jsx)("button",{className:"text-primary hover:text-primary-dark text-sm font-medium",onClick:()=>{},children:"Forgot Password?"})})]}),(0,h.jsx)("div",{children:(0,h.jsx)(o.A,{type:"submit",variant:"primary",fullWidth:!0,size:"lg",loading:p,disabled:p,children:"Sign in"})})]}),(0,h.jsxs)("div",{className:"mt-6",children:[(0,h.jsxs)("div",{className:"relative",children:[(0,h.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,h.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,h.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,h.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Demo Credentials"})})]}),(0,h.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-3",children:[(0,h.jsxs)("div",{className:"text-sm text-center text-gray-600",children:[(0,h.jsxs)("p",{children:["Email: ",(0,h.jsx)("span",{className:"font-medium",children:"<EMAIL>"})]}),(0,h.jsxs)("p",{children:["Password: ",(0,h.jsx)("span",{className:"font-medium",children:"password123"})]})]}),(0,h.jsxs)("div",{className:"mt-4 text-center",children:[(0,h.jsx)("button",{type:"button",onClick:()=>{const e=u.Ar.removeItem("mock_db_users"),r=u.Ar.removeItem("mock_db_suppliers"),a=u.Ar.removeItem("mock_db_categories"),s=u.Ar.removeItem("mock_db_orders");if(e&&r&&a&&s)try{c.n.forceReset(),f("Mock database has been reset. Please try logging in again."),t({email:"",password:"",rememberMe:!1}),x({}),A()}catch(n){N(n,((e,r)=>{x((t=>({...t,[e]:r})))}))}else y("Failed to reset mock database - localStorage error")},className:"text-xs text-primary hover:text-primary-dark underline",children:"Reset Mock Database"}),(0,h.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"If you're having trouble logging in, try resetting the mock database."})]})]})]})]})]})})}),{fallback:e=>{let{error:r,resetError:t}=e;return(0,h.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,h.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"\ud83d\udd10"}),(0,h.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Login Page Error"}),(0,h.jsx)("p",{className:"text-gray-600 mb-4",children:r.message||"An error occurred while loading the login page"}),(0,h.jsx)("button",{onClick:t,className:"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors",children:"Reload Page"})]})})})},context:"LoginPage"})},6773:(e,r,t)=>{t.d(r,{l:()=>g,tU:()=>h});const a=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),s=e=>/^\+?[0-9]{10,15}$/.test(e),n=e=>{try{return new URL(e),!0}catch(r){return!1}},i=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),o=e=>/^[0-9]+$/.test(e),l=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),c=e=>/^[a-zA-Z0-9]+$/.test(e),d=e=>{const r=new Date(e);return!isNaN(r.getTime())},m=(e,r)=>e===r,u=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),g=(e,r)=>{const t={};return Object.entries(r).forEach((r=>{let[a,s]=r;const n=a,i=((e,r,t,a)=>{const s=Array.isArray(t)?t:[t];for(const n of s)if(!n.validator(r,a))return n.message;return""})(0,e[n],s,e);i&&(t[n]=i)})),t},h={required:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:a,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:s,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,r)=>({validator:r=>((e,r)=>e.length>=r)(r,e),message:r||`Must be at least ${e} characters`}),maxLength:(e,r)=>({validator:r=>((e,r)=>e.length<=r)(r,e),message:r||`Must be no more than ${e} characters`}),numeric:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:u,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,r)=>m(e,null===r||void 0===r?void 0:r.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,r)=>m(e,null===r||void 0===r?void 0:r.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,r)=>!r||!r.stock||e<=r.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:r=>!!Array.isArray(r)&&r.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},7907:(e,r,t)=>{t.d(r,{A:()=>i});var a=t(5043),s=t(579);const n=e=>{let{children:r,variant:t="primary",size:a="md",className:n="",onClick:i,disabled:o=!1,type:l="button",icon:c,iconPosition:d="left",fullWidth:m=!1,loading:u=!1,rounded:g=!1,href:h,target:x,rel:p,title:b,ariaLabel:v,testId:y}=e;const f=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[t]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[a]}\n    ${o?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${g?"rounded-full":"rounded-lg"}\n    ${n}\n  `,w=(0,s.jsxs)(s.Fragment,{children:[u&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!u&&(0,s.jsx)("span",{className:"mr-2",children:c}),r,c&&"right"===d&&(0,s.jsx)("span",{className:"ml-2",children:c})]});return h?(0,s.jsx)("a",{href:h,className:f,target:x,rel:p||("_blank"===x?"noopener noreferrer":void 0),onClick:i,title:b,"aria-label":v,"data-testid":y,children:w}):(0,s.jsx)("button",{type:l,className:f,onClick:i,disabled:o||u,title:b,"aria-label":v,"data-testid":y,children:w})},i=(0,a.memo)(n)}}]);
//# sourceMappingURL=765.ef850156.chunk.js.map