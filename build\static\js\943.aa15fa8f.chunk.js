"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[943],{402:(e,t,a)=>{a.r(t),a.d(t,{default:()=>L});var r=a(5043),s=a(2806),i=a(3593),n=a(8100),l=a(3927),c=a(9705),o=a(7422),d=a(8300),m=a(8682),u=a(9850),x=a(7012),h=a(4538),f=a(7098),p=a(9531),v=a(9422),y=a(7591),j=a(579);const g=e=>{let{verifications:t,onViewVerification:a,onApproveClick:r,onRejectClick:s,title:i="Verifications",description:n}=e;const l=[{key:"companyName",label:"Company Name",sortable:!0,render:(e,t)=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)("div",{className:"mr-3",children:(0,j.jsx)(d.A,{alt:t.companyName,name:t.companyName,size:"sm"})}),(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{className:"font-medium text-gray-900",children:t.companyName}),(0,j.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"email",label:"Email",sortable:!0,render:e=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(m.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,j.jsx)("span",{children:e})]})},{key:"phone",label:"Phone",sortable:!0,render:e=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(u.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,j.jsx)("span",{children:e})]})},{key:"status",label:"Status",sortable:!0,render:e=>{if(!e)return(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(x.A,{className:"w-4 h-4 text-gray-400 mr-1"}),(0,j.jsx)("span",{children:"Unknown"})]});let t;switch(e){case"verified":t=(0,j.jsx)(h.A,{className:"w-4 h-4 text-green-500 mr-1"});break;case"pending":t=(0,j.jsx)(x.A,{className:"w-4 h-4 text-yellow-500 mr-1"});break;case"rejected":t=(0,j.jsx)(f.A,{className:"w-4 h-4 text-red-500 mr-1"});break;default:t=(0,j.jsx)(x.A,{className:"w-4 h-4 text-gray-400 mr-1"})}return(0,j.jsxs)("div",{className:"flex items-center",children:[t,(0,j.jsx)("span",{children:e?e.charAt(0).toUpperCase()+e.slice(1):"Unknown"})]})}},{key:"submittedDate",label:"Submitted Date",sortable:!0,render:e=>new Date(e).toLocaleDateString()},{key:"actions",label:"Actions",render:(e,t)=>(0,j.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,j.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),a(t)},title:"View verification details",children:(0,j.jsx)(p.A,{className:"w-5 h-5"})}),(0,j.jsx)("button",{className:"p-1 text-gray-500 hover:text-green-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),r(t)},title:"Approve verification",children:(0,j.jsx)(v.A,{className:"w-5 h-5"})}),(0,j.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),s(t)},title:"Reject verification",children:(0,j.jsx)(y.A,{className:"w-5 h-5"})})]})}];return(0,j.jsx)(o.A,{columns:l,data:t,title:i,description:n,pagination:!0,selectable:!1})};var w=a(7907);const b=e=>{let{verification:t,onClose:a,onApprove:r,onReject:s}=e;return(0,j.jsxs)("div",{className:"space-y-6",children:[(0,j.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Company Information"}),(0,j.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,j.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,j.jsx)("span",{className:"font-medium",children:"Company Name:"})," ",t.companyName]}),(0,j.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,j.jsx)("span",{className:"font-medium",children:"Contact Person:"})," ",t.contactPerson]}),(0,j.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,j.jsx)("span",{className:"font-medium",children:"Email:"})," ",t.email]}),(0,j.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,j.jsx)("span",{className:"font-medium",children:"Phone:"})," ",t.phone]}),(0,j.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,j.jsx)("span",{className:"font-medium",children:"Submitted Date:"})," ",new Date(t.submittedDate).toLocaleString()]})]})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Submitted Documents"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsx)("ul",{className:"divide-y divide-gray-200",children:t.documents.map(((e,t)=>(0,j.jsx)("li",{className:"py-2",children:(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e.name}),(0,j.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," Document"]})]}),(0,j.jsx)(w.A,{variant:"outline",size:"xs",href:e.url,target:"_blank",children:"View"})]})},t)))})})]})]}),(0,j.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,j.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,j.jsx)(w.A,{variant:"outline",onClick:a,children:"Close"}),(0,j.jsx)(w.A,{variant:"success",icon:(0,j.jsx)(v.A,{className:"h-4 w-4"}),onClick:r,children:"Approve"}),(0,j.jsx)(w.A,{variant:"danger",icon:(0,j.jsx)(y.A,{className:"h-4 w-4"}),onClick:s,children:"Reject"})]})})]})},N=e=>{let{isOpen:t,onClose:a,onApprove:r,verification:s}=e;return s?(0,j.jsxs)(n.A,{isOpen:t,onClose:a,title:"Approve Supplier",size:"sm",footer:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(w.A,{variant:"outline",onClick:a,children:"Cancel"}),(0,j.jsx)(w.A,{variant:"success",onClick:r,children:"Approve"})]}),children:[(0,j.jsxs)("p",{className:"text-sm text-gray-600",children:["Are you sure you want to approve ",(0,j.jsx)("span",{className:"font-medium",children:s.companyName})," as a verified supplier?"]}),(0,j.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"This will grant them access to list products and receive orders on the platform."})]}):null},k=e=>{let{isOpen:t,onClose:a,onReject:r,verification:s,rejectReason:i,setRejectReason:l}=e;return s?(0,j.jsx)(n.A,{isOpen:t,onClose:a,title:"Reject Supplier",size:"sm",footer:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(w.A,{variant:"outline",onClick:a,children:"Cancel"}),(0,j.jsx)(w.A,{variant:"danger",onClick:r,children:"Reject"})]}),children:(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsxs)("p",{className:"text-sm text-gray-600",children:["Are you sure you want to reject ",(0,j.jsx)("span",{className:"font-medium",children:s.companyName}),"'s verification request?"]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{htmlFor:"reject-reason",className:"block text-sm font-medium text-gray-700",children:"Reason for Rejection"}),(0,j.jsx)("textarea",{id:"reject-reason",rows:3,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Please provide a reason for rejection",value:i,onChange:e=>l(e.target.value)})]}),(0,j.jsx)("p",{className:"text-xs text-gray-500",children:"This reason will be sent to the supplier via email."})]})}):null};var A=a(1568),C=a(4703),E=a(8479);const S={getVerifications:async e=>{try{const t=await A.A.get("/verifications",{params:e});return E.lg.getList(t,"verifications")}catch(t){throw(0,C.hS)(t)}},getVerificationById:async e=>{try{const t=await A.A.get(`/verifications/${e}`);return E.lg.getById(t,"verification",e)}catch(t){throw(0,C.hS)(t)}},createVerification:async e=>{try{const t=await A.A.post("/verifications",e);return E.lg.create(t,"verification")}catch(t){throw(0,C.hS)(t)}},updateVerificationStatus:async(e,t)=>{try{const a=await A.A.put(`/verifications/${e}/status`,{status:t});return E.lg.update(a,"verification",e)}catch(a){throw(0,C.hS)(a)}},getVerificationsByStatus:async e=>{try{const t=await A.A.get("/verifications",{params:{status:e}});return E.lg.getList(t,"verifications",!0)}catch(t){throw(0,C.hS)(t)}},getVerificationsByUser:async e=>{try{const t=await A.A.get("/verifications",{params:{userId:e}});return E.lg.getList(t,"verifications",!0)}catch(t){throw(0,C.hS)(t)}},updateVerification:async(e,t)=>{try{const a=await A.A.put(`/verifications/${e}`,t);return E.lg.update(a,"verification",e)}catch(a){throw(0,C.hS)(a)}},approveVerification:async(e,t)=>{try{const a=await A.A.put(`/verifications/${e}/approve`,{notes:t});return E.lg.update(a,"verification",e)}catch(a){throw(0,C.hS)(a)}},rejectVerification:async(e,t)=>{try{const a=await A.A.put(`/verifications/${e}/reject`,{notes:t});return E.lg.update(a,"verification",e)}catch(a){throw(0,C.hS)(a)}}},V=()=>{const[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(!1),[i,n]=(0,r.useState)(null),{showNotification:l}=(0,c.A)(),o=(0,r.useRef)(l),d=(0,r.useRef)(!1);(0,r.useEffect)((()=>{o.current=l}));const m=(0,r.useCallback)((async()=>{s(!0),n(null);try{const e=await S.getVerifications();t(e)}catch(e){n(e),o.current({type:"error",title:"Error",message:"Failed to fetch verifications"})}finally{s(!1)}}),[]),u=(0,r.useCallback)((async e=>{s(!0),n(null);try{return await S.getVerificationById(e)}catch(t){throw n(t),o.current({type:"error",title:"Error",message:`Failed to fetch verification ${e}`}),t}finally{s(!1)}}),[]),x=(0,r.useCallback)((async e=>{s(!0),n(null);try{return await S.getVerificationsByStatus(e)}catch(t){throw n(t),o.current({type:"error",title:"Error",message:`Failed to fetch verifications with status ${e}`}),t}finally{s(!1)}}),[]),h=(0,r.useCallback)((async(e,a)=>{s(!0),n(null);try{const r=await S.updateVerification(e,a);return t((t=>t.map((t=>t.id===e?r:t)))),o.current({type:"success",title:"Success",message:"Verification updated successfully"}),r}catch(r){throw n(r),o.current({type:"error",title:"Error",message:"Failed to update verification"}),r}finally{s(!1)}}),[]),f=(0,r.useCallback)((async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";s(!0),n(null);try{const r=await S.approveVerification(e,a);return t((t=>t.map((t=>t.id===e?r:t)))),r}catch(r){throw n(r),o.current({type:"error",title:"Error",message:"Failed to approve verification"}),r}finally{s(!1)}}),[]),p=(0,r.useCallback)((async(e,a)=>{s(!0),n(null);try{const r=await S.rejectVerification(e,a);return t((t=>t.map((t=>t.id===e?r:t)))),r}catch(r){throw n(r),o.current({type:"error",title:"Error",message:"Failed to reject verification"}),r}finally{s(!1)}}),[]);return(0,r.useEffect)((()=>{d.current||(d.current=!0,m())}),[]),{verifications:e,isLoading:a,error:i,fetchVerifications:m,getVerificationById:u,getVerificationsByStatus:x,updateVerification:h,approveVerification:f,rejectVerification:p}},L=()=>{const{verifications:e,isLoading:t,approveVerification:a,rejectVerification:o}=V(),{showSuccess:d,showError:m}=(0,c.A)(),[u,x]=(0,r.useState)(null),[h,f]=(0,r.useState)(!1),[p,v]=(0,r.useState)(!1),[y,w]=(0,r.useState)(!1),[A,C]=(0,r.useState)(""),[E,S]=(0,r.useState)(new Set),L=(0,r.useMemo)((()=>e.filter((e=>!E.has(e.id)&&"pending"===e.status))),[e,E]);return(0,j.jsxs)("div",{children:[(0,j.jsx)(s.A,{title:"Supplier Verifications",description:"Review and manage supplier verification requests",breadcrumbs:[{label:"Verifications"}]}),(0,j.jsx)(i.A,{children:t?(0,j.jsx)("div",{className:"flex justify-center py-8",children:(0,j.jsx)(l.A,{})}):0===L.length?(0,j.jsxs)("div",{className:"text-center py-12",children:[(0,j.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No pending verifications"}),(0,j.jsx)("div",{className:"text-gray-400 text-sm",children:"All verification requests have been processed or there are no new requests."})]}):(0,j.jsx)(g,{verifications:L,onViewVerification:e=>{x(e),f(!0)},onApproveClick:e=>{x(e),v(!0)},onRejectClick:e=>{x(e),w(!0)},title:"Pending Verifications",description:"Suppliers waiting for verification approval"})}),(0,j.jsx)(n.A,{isOpen:h,onClose:()=>f(!1),title:"Verification Details",size:"lg",children:u&&(0,j.jsx)(b,{verification:u,onClose:()=>f(!1),onApprove:()=>{f(!1),v(!0)},onReject:()=>{f(!1),w(!0)}})}),(0,j.jsx)(N,{isOpen:p,onClose:()=>v(!1),onApprove:async()=>{if(u)try{await a(u.id),S((e=>new Set(e).add(u.id))),d(`${u.companyName} has been approved`),v(!1),x(null)}catch(e){console.error("Error approving verification:",e)}},verification:u}),(0,j.jsx)(k,{isOpen:y,onClose:()=>w(!1),onReject:async()=>{if(A.trim()){if(u)try{await o(u.id,A),S((e=>new Set(e).add(u.id))),d(`${u.companyName} has been rejected`),w(!1),C(""),x(null)}catch(e){console.error("Error rejecting verification:",e)}}else m("Please provide a reason for rejection")},verification:u,rejectReason:A,setRejectReason:C})]})}},2806:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(5043),s=a(5475),i=a(5501),n=a(6365),l=a(579);const c=e=>{let{title:t,description:a,actions:r,breadcrumbs:c,className:o="",testId:d}=e;return(0,l.jsxs)("div",{className:`mb-6 ${o}`,"data-testid":d,children:[c&&c.length>0&&(0,l.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,l.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,l.jsx)("li",{children:(0,l.jsx)(s.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})}),c.map(((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<c.length-1?(0,l.jsx)(s.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),a&&"string"===typeof a?(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a}):a]}),r&&(0,l.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:r})]})]})},o=(0,r.memo)(c)},6365:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const i=r.forwardRef(s)},7012:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(s)},7098:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(s)},8100:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(5043),s=a(7591),i=a(7950),n=a(579);const l=e=>{let{isOpen:t,onClose:a,title:l,children:c,size:o="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:u=!0,showCloseButton:x=!0,centered:h=!0,className:f="",bodyClassName:p="",headerClassName:v="",footerClassName:y="",backdropClassName:j="",testId:g}=e;const w=(0,r.useRef)(null);if((0,r.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&a()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,a,m]),(0,r.useEffect)((()=>{if(!t||!w.current)return;const e=w.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const a=e[0],r=e[e.length-1],s=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===a&&(r.focus(),e.preventDefault()):document.activeElement===r&&(a.focus(),e.preventDefault()))};return document.addEventListener("keydown",s),a.focus(),()=>{document.removeEventListener("keydown",s)}}),[t]),!t)return null;const b=(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${j}`,onClick:u?a:void 0,"data-testid":`${g}-backdrop`}),(0,n.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,n.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,n.jsxs)("div",{ref:w,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${f}`,onClick:e=>e.stopPropagation(),"data-testid":g,children:[(0,n.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${v}`,children:["string"===typeof l?(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,x&&(0,n.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:a,"aria-label":"Close modal","data-testid":`${g}-close-button`,children:(0,n.jsx)(s.A,{className:"h-6 w-6"})})]}),(0,n.jsx)("div",{className:`px-6 py-4 ${p}`,children:c}),d&&(0,n.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${y}`,children:d})]})})})]});return(0,i.createPortal)(b,document.body)},c=(0,r.memo)(l)},8682:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const i=r.forwardRef(s)},9422:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const i=r.forwardRef(s)},9531:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const i=r.forwardRef(s)},9850:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const i=r.forwardRef(s)}}]);
//# sourceMappingURL=943.aa15fa8f.chunk.js.map