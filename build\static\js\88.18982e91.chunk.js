"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[88],{233:(e,t,r)=>{r.d(t,{B:()=>n});var a=r(5043),s=r(9705);const n=(e,t)=>{const[r,n]=(0,a.useState)([]),[i,l]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),{showNotification:d}=(0,s.A)(),u=(0,a.useRef)(e),m=(0,a.useRef)(d),g=(0,a.useRef)(t.entityName),h=(0,a.useRef)(!1);(0,a.useEffect)((()=>{u.current=e,m.current=d,g.current=t.entityName}));const y=(0,a.useCallback)((async e=>{l(!0),c(null);try{const t=await u.current.getAll(e);return n(t),t}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${g.current}`}),e}finally{l(!1)}}),[]),p=(0,a.useCallback)((async e=>{l(!0),c(null);try{return await u.current.getById(e)}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${g.current}`}),e}finally{l(!1)}}),[]),x=(0,a.useCallback)((async e=>{l(!0),c(null);try{const t=await u.current.create(e);return n((e=>[...e,t])),m.current({type:"success",title:"Success",message:`${g.current} created successfully`}),t}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to create ${g.current}`}),e}finally{l(!1)}}),[]),f=(0,a.useCallback)((async(e,t)=>{l(!0),c(null);try{const r=await u.current.update(e,t);return n((t=>t.map((t=>t.id===e?r:t)))),m.current({type:"success",title:"Success",message:`${g.current} updated successfully`}),r}catch(r){const e=r;throw c(e),m.current({type:"error",title:"Error",message:`Failed to update ${g.current}`}),e}finally{l(!1)}}),[]),v=(0,a.useCallback)((async e=>{l(!0),c(null);try{await u.current.delete(e),n((t=>t.filter((t=>t.id!==e)))),m.current({type:"success",title:"Success",message:`${g.current} deleted successfully`})}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to delete ${g.current}`}),e}finally{l(!1)}}),[]);return(0,a.useEffect)((()=>{if(!1!==t.initialFetch&&!h.current){console.log(`[useEntityData] Starting initial fetch for ${t.entityName}`),h.current=!0;const r=async()=>{l(!0),c(null);try{console.log(`[useEntityData] Calling API for ${t.entityName}`);const r=await e.getAll();console.log(`[useEntityData] Received data for ${t.entityName}:`,r),n(r)}catch(r){const e=r;console.error(`[useEntityData] Error fetching ${t.entityName}:`,e),c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${t.entityName}`})}finally{console.log(`[useEntityData] Finished fetch for ${t.entityName}`),l(!1)}};r()}}),[e,t.entityName,t.initialFetch]),{entities:r,isLoading:i,error:o,fetchEntities:y,getEntityById:p,createEntity:x,updateEntity:f,deleteEntity:v,setEntities:n}}},2806:(e,t,r)=>{r.d(t,{A:()=>c});var a=r(5043),s=r(5475),n=r(5501),i=r(6365),l=r(579);const o=e=>{let{title:t,description:r,actions:a,breadcrumbs:o,className:c="",testId:d}=e;return(0,l.jsxs)("div",{className:`mb-6 ${c}`,"data-testid":d,children:[o&&o.length>0&&(0,l.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,l.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,l.jsx)("li",{children:(0,l.jsx)(s.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,l.jsx)(n.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(i.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,l.jsx)(s.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),r&&"string"===typeof r?(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r}):r]}),a&&(0,l.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:a})]})]})},c=(0,a.memo)(o)},2811:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const n=a.forwardRef(s)},6365:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const n=a.forwardRef(s)},6773:(e,t,r)=>{r.d(t,{l:()=>g,tU:()=>h});const a=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),s=e=>/^\+?[0-9]{10,15}$/.test(e),n=e=>{try{return new URL(e),!0}catch(t){return!1}},i=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),l=e=>/^[0-9]+$/.test(e),o=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),c=e=>/^[a-zA-Z0-9]+$/.test(e),d=e=>{const t=new Date(e);return!isNaN(t.getTime())},u=(e,t)=>e===t,m=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),g=(e,t)=>{const r={};return Object.entries(t).forEach((t=>{let[a,s]=t;const n=a,i=((e,t,r,a)=>{const s=Array.isArray(r)?r:[r];for(const n of s)if(!n.validator(t,a))return n.message;return""})(0,e[n],s,e);i&&(r[n]=i)})),r},h={required:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:a,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:s,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,t)=>({validator:t=>((e,t)=>e.length>=t)(t,e),message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validator:t=>((e,t)=>e.length<=t)(t,e),message:t||`Must be no more than ${e} characters`}),numeric:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:m,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,t)=>!t||!t.stock||e<=t.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:t=>!!Array.isArray(t)&&t.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},7098:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const n=a.forwardRef(s)},8100:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(5043),s=r(7591),n=r(7950),i=r(579);const l=e=>{let{isOpen:t,onClose:r,title:l,children:o,size:c="md",footer:d,closeOnEsc:u=!0,closeOnBackdropClick:m=!0,showCloseButton:g=!0,centered:h=!0,className:y="",bodyClassName:p="",headerClassName:x="",footerClassName:f="",backdropClassName:v="",testId:b}=e;const w=(0,a.useRef)(null);if((0,a.useEffect)((()=>{const e=e=>{u&&"Escape"===e.key&&r()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,r,u]),(0,a.useEffect)((()=>{if(!t||!w.current)return;const e=w.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const r=e[0],a=e[e.length-1],s=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===r&&(a.focus(),e.preventDefault()):document.activeElement===a&&(r.focus(),e.preventDefault()))};return document.addEventListener("keydown",s),r.focus(),()=>{document.removeEventListener("keydown",s)}}),[t]),!t)return null;const j=(0,i.jsxs)(a.Fragment,{children:[(0,i.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${v}`,onClick:m?r:void 0,"data-testid":`${b}-backdrop`}),(0,i.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,i.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,i.jsxs)("div",{ref:w,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${y}`,onClick:e=>e.stopPropagation(),"data-testid":b,children:[(0,i.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${x}`,children:["string"===typeof l?(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,g&&(0,i.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:r,"aria-label":"Close modal","data-testid":`${b}-close-button`,children:(0,i.jsx)(s.A,{className:"h-6 w-6"})})]}),(0,i.jsx)("div",{className:`px-6 py-4 ${p}`,children:o}),d&&(0,i.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${f}`,children:d})]})})})]});return(0,n.createPortal)(j,document.body)},o=(0,a.memo)(l)},8662:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const n=a.forwardRef(s)},9248:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const n=a.forwardRef(s)},9531:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(5043);function s(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const n=a.forwardRef(s)},9895:(e,t,r)=>{r.d(t,{W9:()=>h,h4:()=>u,Ck:()=>b});var a=r(5043),s=r(7422),n=r(4538),i=r(7098),l=r(9531),o=r(2811),c=r(9248),d=r(579);const u=e=>{let{categories:t,onCategoryClick:r,onViewCategory:a,onEditCategory:u,onDeleteCategory:m,title:g="Categories",loading:h=!1}=e;const y=[{key:"id",label:"ID",sortable:!0,render:e=>(0,d.jsx)("span",{className:"text-xs text-gray-500",children:e})},{key:"name",label:"Name",sortable:!0,render:e=>(0,d.jsx)("span",{className:"font-medium text-gray-900",children:e})},{key:"description",label:"Description",sortable:!0},{key:"productCount",label:"Products",sortable:!0,render:e=>(0,d.jsx)("span",{className:"font-medium",children:e})},{key:"status",label:"Status",sortable:!0,render:e=>(0,d.jsxs)("div",{className:"flex items-center",children:["active"===e?(0,d.jsx)(n.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,d.jsx)(i.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,d.jsx)("span",{children:e.charAt(0).toUpperCase()+e.slice(1)})]})},{key:"createdAt",label:"Created At",sortable:!0},{key:"actions",label:"Actions",render:(e,t)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[a&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),a(t)},children:(0,d.jsx)(l.A,{className:"w-5 h-5"})}),u&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),u(t)},children:(0,d.jsx)(o.A,{className:"w-5 h-5"})}),m&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),m(t)},children:(0,d.jsx)(c.A,{className:"w-5 h-5"})})]})}];return(0,d.jsx)(s.A,{columns:y,data:t,onRowClick:r,title:g,pagination:!0,loading:h})};var m=r(7907);var g=r(6773);const h=e=>{let{onSubmit:t,onCancel:r,isLoading:s=!1}=e;const[n,i]=(0,a.useState)({name:"",description:"",status:"active",visibleInSupplierApp:!0,visibleInCustomerApp:!0}),[l,o]=(0,a.useState)({}),c=e=>{const{name:t,value:r}=e.target;i((e=>({...e,[t]:r}))),l[t]&&o((e=>({...e,[t]:""})))};return(0,d.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{const e=(0,g.l)({name:n.name,description:n.description},{name:[g.tU.required("Category name is required")],description:[g.tU.required("Description is required")]});return o(e),0===Object.keys(e).length})()&&t(n)},className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:["Category Name ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",id:"name",name:"name",value:n.name,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(l.name?"border-red-300":"")}),l.name&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.name})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:["Description ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("textarea",{id:"description",name:"description",rows:3,value:n.description,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(l.description?"border-red-300":"")}),l.description&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.description})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,d.jsxs)("select",{id:"status",name:"status",value:n.status,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm",children:[(0,d.jsx)("option",{value:"active",children:"Active"}),(0,d.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)("div",{children:(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"visibleInSupplierApp",checked:n.visibleInSupplierApp,onChange:e=>i((t=>({...t,visibleInSupplierApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Supplier App"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"visibleInCustomerApp",checked:n.visibleInCustomerApp,onChange:e=>i((t=>({...t,visibleInCustomerApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Customer App"})]})})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)(m.A,{type:"button",variant:"outline",onClick:r,disabled:s,children:"Cancel"}),(0,d.jsx)(m.A,{type:"submit",loading:s,children:"Add Category"})]})]})};var y=r(233),p=r(1568),x=r(4703),f=r(8479);const v={getCategories:async e=>{try{const t=await p.A.get("/categories",{params:e});return f.lg.getList(t,"categories")}catch(t){throw(0,x.hS)(t)}},getCategoryById:async e=>{try{const t=await p.A.get(`/categories/${e}`);return f.lg.getById(t,"category",e)}catch(t){throw(0,x.hS)(t)}},createCategory:async e=>{try{const t=await p.A.post("/categories",e);return f.lg.create(t,"category")}catch(t){throw(0,x.hS)(t)}},updateCategory:async(e,t)=>{try{const r=await p.A.put(`/categories/${e}`,t);return f.lg.update(r,"category",e)}catch(r){throw(0,x.hS)(r)}},deleteCategory:async e=>{try{const t=await p.A.delete(`/categories/${e}`);return f.lg.delete(t,"category",e)}catch(t){throw(0,x.hS)(t)}},getSubcategories:async e=>{try{const t=await p.A.get("/categories",{params:{parentId:e}});return f.lg.getList(t,"subcategories",!0)}catch(t){throw(0,x.hS)(t)}}},b=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{initialFetch:!0};const t=(0,y.B)({getAll:v.getCategories,getById:v.getCategoryById,create:v.createCategory,update:v.updateCategory,delete:v.deleteCategory},{entityName:"categories",initialFetch:e.initialFetch}),r=(0,a.useCallback)((()=>t.entities.map((e=>({...e,subcategories:e.subcategories||[]})))),[t.entities]);return{...t,categories:t.entities,fetchCategories:t.fetchEntities,getCategoryById:t.getEntityById,getCategoryHierarchy:r}};r(3109)}}]);
//# sourceMappingURL=88.18982e91.chunk.js.map