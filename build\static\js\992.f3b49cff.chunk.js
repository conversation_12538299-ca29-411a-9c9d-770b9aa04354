"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[992],{233:(e,t,s)=>{s.d(t,{B:()=>n});var r=s(5043),a=s(9705);const n=(e,t)=>{const[s,n]=(0,r.useState)([]),[i,l]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),{showNotification:c}=(0,a.A)(),m=(0,r.useRef)(e),u=(0,r.useRef)(c),p=(0,r.useRef)(t.entityName),h=(0,r.useRef)(!1);(0,r.useEffect)((()=>{m.current=e,u.current=c,p.current=t.entityName}));const x=(0,r.useCallback)((async e=>{l(!0),d(null);try{const t=await m.current.getAll(e);return n(t),t}catch(t){const e=t;throw d(e),u.current({type:"error",title:"Error",message:`Failed to fetch ${p.current}`}),e}finally{l(!1)}}),[]),g=(0,r.useCallback)((async e=>{l(!0),d(null);try{return await m.current.getById(e)}catch(t){const e=t;throw d(e),u.current({type:"error",title:"Error",message:`Failed to fetch ${p.current}`}),e}finally{l(!1)}}),[]),y=(0,r.useCallback)((async e=>{l(!0),d(null);try{const t=await m.current.create(e);return n((e=>[...e,t])),u.current({type:"success",title:"Success",message:`${p.current} created successfully`}),t}catch(t){const e=t;throw d(e),u.current({type:"error",title:"Error",message:`Failed to create ${p.current}`}),e}finally{l(!1)}}),[]),v=(0,r.useCallback)((async(e,t)=>{l(!0),d(null);try{const s=await m.current.update(e,t);return n((t=>t.map((t=>t.id===e?s:t)))),u.current({type:"success",title:"Success",message:`${p.current} updated successfully`}),s}catch(s){const e=s;throw d(e),u.current({type:"error",title:"Error",message:`Failed to update ${p.current}`}),e}finally{l(!1)}}),[]),f=(0,r.useCallback)((async e=>{l(!0),d(null);try{await m.current.delete(e),n((t=>t.filter((t=>t.id!==e)))),u.current({type:"success",title:"Success",message:`${p.current} deleted successfully`})}catch(t){const e=t;throw d(e),u.current({type:"error",title:"Error",message:`Failed to delete ${p.current}`}),e}finally{l(!1)}}),[]);return(0,r.useEffect)((()=>{if(!1!==t.initialFetch&&!h.current){console.log(`[useEntityData] Starting initial fetch for ${t.entityName}`),h.current=!0;const s=async()=>{l(!0),d(null);try{console.log(`[useEntityData] Calling API for ${t.entityName}`);const s=await e.getAll();console.log(`[useEntityData] Received data for ${t.entityName}:`,s),n(s)}catch(s){const e=s;console.error(`[useEntityData] Error fetching ${t.entityName}:`,e),d(e),u.current({type:"error",title:"Error",message:`Failed to fetch ${t.entityName}`})}finally{console.log(`[useEntityData] Finished fetch for ${t.entityName}`),l(!1)}};s()}}),[e,t.entityName,t.initialFetch]),{entities:s,isLoading:i,error:o,fetchEntities:x,getEntityById:g,createEntity:y,updateEntity:v,deleteEntity:f,setEntities:n}}},312:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{children:t,className:s=""}=e;return(0,r.jsx)("dl",{className:`sm:divide-y sm:divide-gray-200 ${s}`,children:t})}},1602:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const n=r.forwardRef(a)},2671:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5043),a=s(3593),n=s(579);const i=e=>{if(!r.isValidElement(e))return"bg-primary bg-opacity-10";const t=(e.props.className||"").match(/text-([a-z]+)-/);if(t){return`bg-${t[1]}-50`}return"bg-primary bg-opacity-10"},l=e=>{let{title:t,data:s,icon:l,formatValue:o=e=>e.toString()}=e;return(0,n.jsx)(a.A,{children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:`p-3 rounded-full ${i(l)}`,children:r.isValidElement(l)?(()=>{const e=l,t=(e.props.className||"").match(/text-[a-z0-9-]+/),s=t?t[0]:"text-gray-600";return r.cloneElement(e,{className:`h-6 w-6 ${s}`})})():l}),(0,n.jsxs)("div",{className:"ml-4 flex-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-500",children:t}),(0,n.jsxs)("div",{className:"flex items-baseline",children:[(0,n.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:o(s.total)}),void 0!==s.growth&&(0,n.jsxs)("p",{className:"ml-2 flex items-baseline text-sm font-semibold "+(s.growth>=0?"text-green-600":"text-red-600"),children:[s.growth>=0?"+":"",s.growth.toFixed(1),"%"]})]})]})]})})},o=e=>{let{metrics:t,className:s=""}=e;return(0,n.jsx)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-6 ${s}`,children:t.map(((e,t)=>(0,n.jsx)(l,{title:e.title,data:{total:"string"===typeof e.value?parseFloat(e.value)||0:e.value,growth:e.change||0},icon:e.icon},t)))})}},2811:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const n=r.forwardRef(a)},5149:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{label:t,value:s,className:a=""}=e;return(0,r.jsxs)("div",{className:`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${a}`,children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:t}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:s})]})}},5901:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{label:t,name:s,type:a="text",value:n,onChange:i,error:l,required:o=!1,placeholder:d="",options:c=[],className:m="",disabled:u=!1,loading:p=!1}=e;const h="mt-1 block w-full rounded-md shadow-sm sm:text-sm "+(l?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary focus:ring-primary");return(0,r.jsxs)("div",{className:`${m}`,children:[(0,r.jsxs)("label",{htmlFor:s,className:"block text-sm font-medium text-gray-700",children:[t," ",o&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(()=>{switch(a){case"textarea":return(0,r.jsx)("textarea",{id:s,name:s,value:n,onChange:i,className:h,placeholder:d,disabled:u});case"select":return(0,r.jsx)("select",{id:s,name:s,value:n,onChange:i,className:h,disabled:u||p,children:p?(0,r.jsx)("option",{value:"",children:"Loading..."}):c.map((e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)))});case"checkbox":return(0,r.jsx)("input",{type:"checkbox",id:s,name:s,checked:n,onChange:i,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",disabled:u});default:return(0,r.jsx)("input",{type:a,id:s,name:s,value:n,onChange:i,className:h,placeholder:d,disabled:u})}})(),l&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})}},6773:(e,t,s)=>{s.d(t,{l:()=>p,tU:()=>h});const r=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),a=e=>/^\+?[0-9]{10,15}$/.test(e),n=e=>{try{return new URL(e),!0}catch(t){return!1}},i=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),l=e=>/^[0-9]+$/.test(e),o=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),d=e=>/^[a-zA-Z0-9]+$/.test(e),c=e=>{const t=new Date(e);return!isNaN(t.getTime())},m=(e,t)=>e===t,u=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),p=(e,t)=>{const s={};return Object.entries(t).forEach((t=>{let[r,a]=t;const n=r,i=((e,t,s,r)=>{const a=Array.isArray(s)?s:[s];for(const n of a)if(!n.validator(t,r))return n.message;return""})(0,e[n],a,e);i&&(s[n]=i)})),s},h={required:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:r,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:a,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,t)=>({validator:t=>((e,t)=>e.length>=t)(t,e),message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validator:t=>((e,t)=>e.length<=t)(t,e),message:t||`Must be no more than ${e} characters`}),numeric:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:u,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,t)=>m(e,null===t||void 0===t?void 0:t.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,t)=>m(e,null===t||void 0===t?void 0:t.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,t)=>!t||!t.stock||e<=t.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:t=>!!Array.isArray(t)&&t.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},6887:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{title:t,description:s,children:a,className:n=""}=e;return(0,r.jsxs)("div",{className:`bg-white shadow overflow-hidden sm:rounded-lg ${n}`,children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:t}),s&&(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:s})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:a})]})}},6992:(e,t,s)=>{s.d(t,{pC:()=>x,Sk:()=>y,cA:()=>H,ID:()=>U,Pt:()=>L,w2:()=>O,kp:()=>de});var r=s(5043),a=s(7907),n=s(5901),i=s(7147),l=s(6773),o=s(1568),d=s(4703),c=s(8479);const m={getBusinessTypes:async()=>{try{const e=await o.A.get("/business-types");return c.lg.getList(e,"business types")}catch(e){throw(0,d.hS)(e)}},getBusinessTypeById:async e=>{try{const t=await o.A.get(`/business-types/${e}`);return c.lg.getById(t,"business type",e)}catch(t){throw(0,d.hS)(t)}}},{getBusinessTypes:u,getBusinessTypeById:p}=m;var h=s(579);const x=e=>{let{onSubmit:t,onCancel:s,isLoading:o=!1}=e;const[d,c]=(0,r.useState)({name:"",email:"",type:"customer",phone:"",address:"",businessType:"",password:"",confirmPassword:"",sendInvite:!0,image:null}),[m,p]=(0,r.useState)({}),[x,g]=(0,r.useState)([]),[y,v]=(0,r.useState)(!1);(0,r.useEffect)((()=>{(async()=>{v(!0);try{const e=await u();g(e)}catch(e){console.error("Failed to load business types:",e)}finally{v(!1)}})()}),[]);const f=e=>{const{name:t,value:s,type:r}=e.target;if("checkbox"===r){const s=e.target.checked;c((e=>({...e,[t]:s})))}else c((e=>({...e,[t]:s})));m[t]&&p((e=>({...e,[t]:""})))};return(0,h.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),(()=>{const e={name:[l.tU.required("Name is required")],email:[l.tU.required("Email is required"),l.tU.email()],type:[l.tU.required("User type is required")],password:[l.tU.required("Password is required"),l.tU.password()],confirmPassword:[l.tU.required("Confirm password is required"),l.tU.passwordMatch()],address:[l.tU.required("Address is required")],businessType:[l.tU.required("Business type is required")]},t=(0,l.l)(d,e);return p(t),0===Object.keys(t).length})()){const e={...d,image:d.image};t(e)}},className:"space-y-6",children:[(0,h.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,h.jsx)(n.A,{label:"Full Name",name:"name",value:d.name,onChange:f,error:m.name,required:!0}),(0,h.jsx)(n.A,{label:"Email",name:"email",type:"email",value:d.email,onChange:f,error:m.email,required:!0}),(0,h.jsx)(n.A,{label:"User Type",name:"type",type:"select",value:d.type,onChange:f,error:m.type,required:!0,options:[{value:"customer",label:"Customer"},{value:"supplier",label:"Supplier"}]}),(0,h.jsx)(n.A,{label:"Phone Number",name:"phone",value:d.phone,onChange:f,error:m.phone}),(0,h.jsx)(n.A,{label:"Address",name:"address",type:"textarea",value:d.address,onChange:f,error:m.address,required:!0,placeholder:"Enter full address"}),(0,h.jsx)(n.A,{label:"Business Type",name:"businessType",type:"select",value:d.businessType,onChange:f,error:m.businessType,required:!0,loading:y,options:[{value:"",label:"Select Business Type"},...x.map((e=>({value:e.id,label:e.name})))]}),(0,h.jsx)(n.A,{label:"Password",name:"password",type:"password",value:d.password,onChange:f,error:m.password,required:!0}),(0,h.jsx)(n.A,{label:"Confirm Password",name:"confirmPassword",type:"password",value:d.confirmPassword,onChange:f,error:m.confirmPassword,required:!0})]}),(0,h.jsx)(i.A,{label:"Profile Picture",name:"image",value:d.image,onChange:e=>{c((t=>({...t,image:e}))),m.image&&p((e=>({...e,image:""})))},error:m.image||void 0,maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"]}),(0,h.jsx)("div",{className:"flex items-center",children:(0,h.jsx)(n.A,{label:"Send invitation email",name:"sendInvite",type:"checkbox",value:d.sendInvite,onChange:f,className:"flex items-center space-x-2"})}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,h.jsx)(a.A,{variant:"outline",onClick:s,disabled:o,children:"Cancel"}),(0,h.jsx)(a.A,{type:"submit",loading:o,children:"Add User"})]})]})};var g=s(3593);const y=e=>{let{user:t,onSubmit:s,isLoading:n=!1}=e;const[i,o]=(0,r.useState)({name:"",email:"",type:"customer"}),[d,c]=(0,r.useState)({});(0,r.useEffect)((()=>{t&&o({name:t.name,email:t.email,type:t.type})}),[t]);const m=e=>{const{name:t,value:s,type:r}=e.target;if("checkbox"===r){const s=e.target.checked;o((e=>({...e,[t]:s})))}else o((e=>({...e,[t]:s})));d[t]&&c((e=>({...e,[t]:""})))};return(0,h.jsx)(g.A,{children:(0,h.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={name:[l.tU.required("Name is required")],email:[l.tU.required("Email is required"),l.tU.email()],type:[l.tU.required("User type is required")]},t=(0,l.l)(i,e);return c(t),0===Object.keys(t).length})())try{await s(i)}catch(t){console.error("Form submission error:",t)}},className:"p-6 space-y-6",children:[(0,h.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,h.jsxs)("div",{children:[(0,h.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:["Full Name ",(0,h.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,h.jsx)("input",{type:"text",id:"name",name:"name",value:i.name,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(d.name?"border-red-300":"")}),d.name&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.name})]}),(0,h.jsxs)("div",{children:[(0,h.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:["Email ",(0,h.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,h.jsx)("input",{type:"email",id:"email",name:"email",value:i.email,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(d.email?"border-red-300":"")}),d.email&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.email})]}),(0,h.jsxs)("div",{children:[(0,h.jsxs)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700",children:["User Type ",(0,h.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,h.jsxs)("select",{id:"type",name:"type",value:i.type,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(d.type?"border-red-300":""),children:[(0,h.jsx)("option",{value:"customer",children:"Customer"}),(0,h.jsx)("option",{value:"supplier",children:"Supplier"})]}),d.type&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.type})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,h.jsx)("input",{type:"password",id:"password",name:"password",value:i.password||"",onChange:m,placeholder:"Leave blank to keep current password",className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(d.password?"border-red-300":"")}),d.password&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.password})]})]}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,h.jsx)(a.A,{variant:"outline",type:"button",onClick:()=>window.history.back(),disabled:n,children:"Cancel"}),(0,h.jsx)(a.A,{type:"submit",loading:n,children:"Save Changes"})]})]})})};var v=s(3216),f=s(6887),b=s(312),w=s(5149),j=s(3893),N=s(4692),A=s(9531);const E=e=>{let{orders:t,title:s="Orders",description:r="Recent orders",onViewOrder:n,emptyMessage:i="No orders found",className:l=""}=e;return(0,h.jsx)(f.A,{title:s,description:r,className:l,children:0===t.length?(0,h.jsx)("div",{className:"px-4 py-5 text-center text-sm text-gray-500",children:i}):(0,h.jsx)("div",{className:"overflow-x-auto",children:(0,h.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,h.jsx)("thead",{className:"bg-gray-50",children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,h.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,h.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>(0,h.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-primary",children:e.id}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,j.Yq)(e.orderDate)}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,j.vv)(e.totalAmount)}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,h.jsx)(N.A,{status:e.status,type:"order"})}),(0,h.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:n&&(0,h.jsx)(a.A,{variant:"text",size:"sm",onClick:()=>n(e),icon:(0,h.jsx)(A.A,{className:"w-4 h-4 text-black"}),className:"text-black hover:text-gray-700 hover:bg-gray-100",children:"View"})})]},e.id)))})]})})})};var k=s(724);const U=e=>{let{user:t,userOrders:s=[]}=e;const r=(0,v.Zp)();return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)(f.A,{title:"User Information",description:"Personal details and application",children:(0,h.jsxs)(b.A,{children:[(0,h.jsx)(w.A,{label:"Full name",value:t.name}),(0,h.jsx)(w.A,{label:"Email address",value:t.email}),(0,h.jsx)(w.A,{label:"User type",value:t.type}),(0,h.jsx)(w.A,{label:"Status",value:(0,h.jsx)(N.A,{status:t.status,type:"user"})}),(0,h.jsx)(w.A,{label:"Last login",value:t.lastLogin})]})}),(0,h.jsx)(E,{orders:s,title:"User Orders",description:"Orders placed by this user",onViewOrder:e=>{r(k.b.getOrderDetailsRoute(e.id))},emptyMessage:"This user has not placed any orders yet"})]})};var S=s(8300),C=s(4538),F=s(7098);const L=e=>{let{user:t}=e;return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)("div",{className:"flex items-center justify-between",children:(0,h.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,h.jsx)(S.A,{...t.avatar&&{src:t.avatar},alt:t.name,name:t.name,size:"xl"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t.name}),(0,h.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",t.id]}),(0,h.jsx)("div",{className:"mt-1",children:(0,h.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("active"===t.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})})]})]})}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Contact Information"}),(0,h.jsxs)("dl",{className:"space-y-3",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("dt",{className:"text-xs text-gray-500",children:"Email"}),(0,h.jsx)("dd",{className:"text-sm text-gray-900",children:t.email})]}),t.phone&&(0,h.jsxs)("div",{children:[(0,h.jsx)("dt",{className:"text-xs text-gray-500",children:"Phone"}),(0,h.jsx)("dd",{className:"text-sm text-gray-900",children:t.phone})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("dt",{className:"text-xs text-gray-500",children:"Last Login"}),(0,h.jsx)("dd",{className:"text-sm text-gray-900",children:t.lastLogin})]})]})]}),t.address&&(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Address"}),(0,h.jsx)("address",{className:"not-italic text-sm text-gray-900",children:t.address})]})]}),t.businessType&&(0,h.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Business Type"}),(0,h.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:t.businessType})]}),(0,h.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Account Status"}),(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:["active"===t.status?(0,h.jsx)(C.A,{className:"w-5 h-5 text-green-500"}):(0,h.jsx)(F.A,{className:"w-5 h-5 text-red-500"}),(0,h.jsx)("span",{className:"text-sm text-gray-700",children:"active"===t.status?`Active since ${t.lastLogin}`:"Account is banned"})]})]})]})};var T=s(5445),P=s(8682),B=s(2811),$=s(9248);const O=e=>{let{users:t,onViewUser:s,onEditUser:r,onDeleteUser:a,onUserClick:n,title:i="Users",loading:l=!1}=e;const o=(0,v.Zp)(),d=[{key:"name",label:"Name",sortable:!0,render:(e,t)=>(0,h.jsxs)("div",{className:"flex items-center",children:[t.avatar?(0,h.jsx)("img",{src:t.avatar,alt:t.name,className:"w-8 h-8 rounded-full mr-3 object-cover"}):(0,h.jsx)("div",{className:"w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3",children:t.name.charAt(0)}),(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{className:"font-medium text-gray-900",children:t.name}),(0,h.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"email",label:"Email",sortable:!0,render:e=>(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)(P.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,h.jsx)("span",{children:e})]})},{key:"type",label:"Type",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"lastLogin",label:"Last Login",sortable:!0},{key:"actions",label:"Actions",render:(e,t)=>(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),s(t)},children:(0,h.jsx)(A.A,{className:"w-5 h-5"})}),(0,h.jsx)("button",{className:"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),o(k.b.getUserEditRoute(t.id))},children:(0,h.jsx)(B.A,{className:"w-5 h-5"})}),(0,h.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),a(t)},children:(0,h.jsx)($.A,{className:"w-5 h-5"})})]})}];return(0,h.jsx)(T.A,{data:t,columns:d,onRowClick:e=>{o(k.b.getUserEditRoute(e.id))},title:i,pagination:!0,loading:l,emptyMessage:"No users found"})};var q=s(3488),D=s(2683),I=s(7012),R=s(6058),M=s(2671),z=s(7174);const H=e=>{var t,s,r;let{userId:a,userData:n}=e;const i={totalOrders:(null===n||void 0===n?void 0:n.totalOrders)||0,totalSpent:(null===n||void 0===n?void 0:n.totalSpent)||0,averageOrderValue:(null===n||void 0===n?void 0:n.averageOrderValue)||0,orderFrequency:(null===n||void 0===n?void 0:n.orderFrequency)||0,orderHistory:(null===n||void 0===n?void 0:n.orderHistory)||[]},l=[{title:"Total Orders",value:i.totalOrders,icon:(0,h.jsx)(q.A,{className:"w-6 h-6 text-blue-500"})},{title:"Total Spent",value:(0,j.vv)(i.totalSpent),icon:(0,h.jsx)(D.A,{className:"w-6 h-6 text-green-500"})},{title:"Average Order",value:(0,j.vv)(i.averageOrderValue),icon:(0,h.jsx)(I.A,{className:"w-6 h-6 text-purple-500"})}],o=e=>{try{return new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"2-digit"})}catch(t){return e}},d=(()=>{if(0===i.orderHistory.length)return{labels:["No Data"],datasets:[{label:"Spending Trend",data:[0],borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",fill:!0,tension:.4}]};const e=i.orderHistory.filter((e=>e&&e.date&&"number"===typeof e.amount)).sort(((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()));let t=0;const s=e.map((e=>(t+=e.amount,{label:o(e.date),value:t})));return{labels:s.map((e=>e.label)),datasets:[{label:"Cumulative Spending",data:s.map((e=>e.value)),borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",fill:!0,tension:.4,pointBackgroundColor:"#F28B22",pointBorderColor:"#ffffff",pointBorderWidth:2,pointRadius:4}]}})();return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)(M.A,{metrics:l}),(0,h.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,h.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Spending Trend"}),(0,h.jsx)("p",{className:"text-sm text-gray-500",children:i.orderHistory.length>0?"Cumulative spending over time":"No spending data available"})]}),i.orderHistory.length>0?(0,h.jsx)("div",{className:"h-80",children:(0,h.jsx)(R.N1,{data:d,options:{...z.OL,plugins:{...z.OL.plugins,title:{display:!1},legend:{display:!1}},scales:{...z.OL.scales,y:{...null===(t=z.OL.scales)||void 0===t?void 0:t.y,ticks:{...null===(s=z.OL.scales)||void 0===s||null===(r=s.y)||void 0===r?void 0:r.ticks,callback:function(e){return(0,j.vv)(e)}}}}}})}):(0,h.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\ufffd"}),(0,h.jsx)("p",{className:"text-gray-500 text-lg font-medium",children:"No Spending Data"}),(0,h.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:"Spending trends will appear here once the user places orders"})]})})]}),(0,h.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Order Frequency"}),(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Order patterns and frequency analysis"})]}),(0,h.jsx)("div",{className:"space-y-4",children:i.orderHistory.length>0?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,h.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Most Recent Order"}),(0,h.jsx)("span",{className:"text-sm text-gray-900",children:(()=>{const e=i.orderHistory[i.orderHistory.length-1];return null!==e&&void 0!==e&&e.date?o(e.date):"N/A"})()})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,h.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"First Order"}),(0,h.jsx)("span",{className:"text-sm text-gray-900",children:(()=>{const e=i.orderHistory[0];return null!==e&&void 0!==e&&e.date?o(e.date):"N/A"})()})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,h.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Largest Order"}),(0,h.jsx)("span",{className:"text-sm text-gray-900",children:(0,j.vv)(Math.max(...i.orderHistory.map((e=>e.amount))))})]}),(0,h.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,h.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Smallest Order"}),(0,h.jsx)("span",{className:"text-sm text-gray-900",children:(0,j.vv)(Math.min(...i.orderHistory.map((e=>e.amount))))})]})]}):(0,h.jsxs)("div",{className:"text-center py-8",children:[(0,h.jsx)("div",{className:"text-gray-400 text-3xl mb-3",children:"\ud83d\udcca"}),(0,h.jsx)("p",{className:"text-gray-500 font-medium",children:"No Order Data"}),(0,h.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Order frequency analysis will appear here"})]})})]})]})]})};var V=s(233);const Z={BASE:"/users",DETAILS:e=>`/users/${e}`,STATUS:e=>`/users/${e}/status`,BAN:e=>`/users/${e}/ban`,UNBAN:e=>`/users/${e}/unban`,UPLOAD_IMAGE:"/users/upload-image"},_=e=>{const t={Name:e.name,Email:e.email,verificationStatus:e.verificationStatus||"pending"};return void 0!==e.password&&(t.password=e.password),void 0!==e.phone&&(t.PhoneNumber=e.phone),void 0!==e.address&&(t.Address=e.address),void 0!==e.businessType&&(t.BusinessType=e.businessType),t},W=e=>{const t={id:e.id,name:e.name,email:e.email,type:e.type,status:e.status,verificationStatus:e.verificationStatus||"pending"};return void 0!==e.avatar&&(t.avatar=e.avatar),void 0!==e.phone&&(t.phone=e.phone),void 0!==e.address&&(t.address=e.address),void 0!==e.businessType&&(t.businessType=e.businessType),void 0!==e.lastLogin&&(t.lastLogin=e.lastLogin),t},G=e=>{if(!e)throw new Error("Empty response received");if("object"!==typeof e)throw new Error("Invalid response format");if(!("success"in e))throw new Error("Response missing success field");if(!1===e.success)throw new Error(e.message||"Request failed");if(!("data"in e))throw new Error("Response missing data field");return e},J=e=>{const t=G(e),s=W(t.data);return{success:t.success,message:t.message,data:s}},K=e=>"string"===typeof e?e:null!==e&&void 0!==e&&e.message?e.message:null!==e&&void 0!==e&&e.error?e.error:"An unexpected error occurred",Y={getUsers:async e=>{try{const t=e?(e=>{const t={};return void 0!==e.page&&(t.page=e.page),void 0!==e.limit&&(t.limit=e.limit),void 0!==e.search&&(t.search=e.search),void 0!==e.status&&(t.status=e.status),void 0!==e.sort&&(t.sort=e.sort),void 0!==e.order&&(t.order=e.order),t})(e):{},s=await o.A.get(Z.BASE,{params:t});if(s.error)throw new Error(s.error);if(!s.data)throw new Error("No response data received");return(e=>{const t=G(e),s=t.data.map(W),r={success:t.success,message:t.message,data:s};return void 0!==t.pagination&&(r.pagination=t.pagination),r})(s.data)}catch(s){var t;if(null!==(t=s.response)&&void 0!==t&&t.data){const e=K(s.response.data);throw new Error(e)}throw(0,d.hS)(s)}},getUserById:async e=>{try{const t=await o.A.get(Z.DETAILS(e));if(t.error)throw new Error(t.error);if(!t.data)throw new Error("No response data received");return J(t.data).data}catch(s){var t;if(null!==(t=s.response)&&void 0!==t&&t.data){const e=K(s.response.data);throw new Error(e)}throw(0,d.hS)(s)}},createUser:async e=>{try{const t=_(e),s=await o.A.post(Z.BASE,t);if(s.error)throw new Error(s.error);if(!s.data)throw new Error("No response data received");return J(s.data).data}catch(s){var t;if(null!==(t=s.response)&&void 0!==t&&t.data){const e=K(s.response.data);throw new Error(e)}throw(0,d.hS)(s)}},updateUser:async(e,t)=>{try{const s=_(t),r=await o.A.put(Z.DETAILS(e),s);if(r.error)throw new Error(r.error);if(!r.data)throw new Error("No response data received");return J(r.data).data}catch(r){var s;if(null!==(s=r.response)&&void 0!==s&&s.data){const e=K(r.response.data);throw new Error(e)}throw(0,d.hS)(r)}},deleteUser:async e=>{try{const t=await o.A.delete(Z.DETAILS(e));if(t.error)throw new Error(t.error);if(t.data&&"object"===typeof t.data&&"success"in t.data&&!t.data.success){const e="message"in t.data?t.data.message:"Delete operation failed";throw new Error(e)}}catch(s){var t;if(null!==(t=s.response)&&void 0!==t&&t.data){const e=K(s.response.data);throw new Error(e)}throw(0,d.hS)(s)}},updateUserStatus:async(e,t)=>{try{const s={status:t},r=await o.A.put(Z.STATUS(e),s);if(r.error)throw new Error(r.error);if(r.data&&"object"===typeof r.data&&"success"in r.data&&!r.data.success){const e="message"in r.data?r.data.message:"Status update failed";throw new Error(e)}}catch(r){var s;if(null!==(s=r.response)&&void 0!==s&&s.data){const e=K(r.response.data);throw new Error(e)}throw(0,d.hS)(r)}},searchUsers:async(e,t)=>{try{const s={...t,search:e};return await Y.getUsers(s)}catch(s){throw(0,d.hS)(s)}},getUsersByType:async(e,t)=>{try{const e={...t};return await Y.getUsers(e)}catch(s){throw(0,d.hS)(s)}},uploadUserImage:async e=>{try{const t=new FormData;t.append("image",e);const s=await o.A.post(Z.UPLOAD_IMAGE,t,{headers:{"Content-Type":"multipart/form-data"}});if(s.error)throw new Error(s.error);if(!s.data)throw new Error("No response data received");return G(s.data).data}catch(s){var t;if(null!==(t=s.response)&&void 0!==t&&t.data){const e=K(s.response.data);throw new Error(e)}throw(0,d.hS)(s)}}},{getUsers:Q,getUserById:X,createUser:ee,updateUser:te,deleteUser:se,updateUserStatus:re,searchUsers:ae,getUsersByType:ne,uploadUserImage:ie}=Y,le=Y;var oe=s(9705);const de=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{initialFetch:!0};(0,r.useEffect)((()=>{o.A.clearCache()}),[]);const t={getAll:async()=>(await le.getUsers()).data,getById:le.getUserById,create:le.createUser,update:le.updateUser,delete:le.deleteUser},s=(0,V.B)(t,{entityName:"users",initialFetch:e.initialFetch}),{showNotification:a}=(0,oe.A)(),n=(0,r.useRef)(a);(0,r.useEffect)((()=>{n.current=a}));const i=(0,r.useCallback)((async(e,t)=>{try{await le.updateUserStatus(e,t);const r=s.entities.map((s=>s.id===e?{...s,status:t}:s));s.setEntities(r),n.current({type:"success",title:"Success",message:`User ${"active"===t?"activated":"banned"} successfully`})}catch(r){throw n.current({type:"error",title:"Error",message:`Failed to ${"active"===t?"activate":"ban"} user`}),r}}),[s]),l=(0,r.useCallback)((async(e,t)=>{try{const r=await le.updateUser(e,t),a=s.entities.map((t=>t.id===e?r:t));return s.setEntities(a),n.current({type:"success",title:"Success",message:"User updated successfully"}),r}catch(r){throw n.current({type:"error",title:"Error",message:"Failed to update user"}),r}}),[s]),d=(0,r.useCallback)((async(e,t)=>{try{return await le.searchUsers(e,t)}catch(s){throw n.current({type:"error",title:"Error",message:"Failed to search users"}),s}}),[]),c=(0,r.useCallback)((async e=>{try{return await le.getUsers(e)}catch(t){throw n.current({type:"error",title:"Error",message:"Failed to fetch users"}),t}}),[]);return{...s,users:s.entities,fetchUsers:s.fetchEntities,getUserById:s.getEntityById,createEntity:s.createEntity,deleteEntity:s.deleteEntity,updateUserStatus:i,updateUser:l,searchUsers:d,getUsersWithPagination:c}};s(6953)},7147:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5043),a=s(7591),n=s(1602),i=s(4703),l=s(579);const o=e=>{let{label:t,name:s,value:o,onChange:d,error:c,required:m=!1,disabled:u=!1,maxSize:p=5242880,allowedTypes:h=["image/jpeg","image/png","image/gif","image/webp"],className:x=""}=e;const[g,y]=(0,r.useState)(!1),[v,f]=(0,r.useState)(null),b=(0,r.useRef)(null);r.useEffect((()=>{if(o instanceof File){const e=URL.createObjectURL(o);return f(e),()=>URL.revokeObjectURL(e)}return"string"===typeof o&&o?void f(o):void f(null)}),[o]);const w=(0,r.useCallback)((e=>{const t=(0,i.nJ)(e,{maxSize:p,allowedTypes:h});t.valid?d(e):console.error("File validation failed:",t.error)}),[p,h,d]);return(0,l.jsxs)("div",{className:x,children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[t," ",m&&(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:`\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n          ${g?"border-primary bg-primary bg-opacity-5":"border-gray-300"}\n          ${c?"border-red-300":""}\n          ${u?"opacity-50 cursor-not-allowed":"hover:border-primary hover:bg-gray-50"}\n        `,onDragOver:e=>{e.preventDefault(),u||y(!0)},onDragLeave:e=>{e.preventDefault(),y(!1)},onDrop:e=>{var t;if(e.preventDefault(),y(!1),u)return;const s=null===(t=e.dataTransfer.files)||void 0===t?void 0:t[0];s&&w(s)},onClick:()=>{!u&&b.current&&b.current.click()},children:[(0,l.jsx)("input",{ref:b,type:"file",name:s,accept:h.join(","),onChange:e=>{var t;const s=null===(t=e.target.files)||void 0===t?void 0:t[0];s&&w(s)},className:"hidden",disabled:u}),v?(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("img",{src:v,alt:"Preview",className:"mx-auto h-32 w-32 object-cover rounded-lg"}),!u&&(0,l.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),d(null),b.current&&(b.current.value="")},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,l.jsx)(a.A,{className:"h-4 w-4"})})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(n.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:g?"Drop image here":"Click to upload or drag and drop"}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["PNG, JPG, GIF up to ",Math.round(p/1024/1024),"MB"]})]})]})]}),c&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c})]})}},7174:(e,t,s)=>{s.d(t,{HW:()=>i,O1:()=>n,OL:()=>a});var r=s(7304);r.t1.register(r.PP,r.kc,r.FN,r.No,r.E8,r.Bs,r.pr,r.hE,r.m_,r.s$,r.dN);const a={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{display:!1,labels:{color:"#000000"}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12,displayColors:!1}},scales:{x:{grid:{display:!1},ticks:{color:"#000000"}},y:{beginAtZero:!0,grid:{display:!0,color:"#E5E7EB"},ticks:{color:"#000000"}}}},n={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{position:"bottom",labels:{color:"#000000",font:{size:12}}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12}}},i=e=>{e&&e.destroy()}},8682:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const n=r.forwardRef(a)},9248:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const n=r.forwardRef(a)}}]);
//# sourceMappingURL=992.f3b49cff.chunk.js.map