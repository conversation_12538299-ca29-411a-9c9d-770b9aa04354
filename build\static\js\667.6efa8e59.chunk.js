"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[667],{1318:(e,t,s)=>{s.r(t),s.d(t,{default:()=>z});var r=s(5043),a=s(3216),i=s(2806),l=s(3593),n=s(7907),c=s(8100),o=s(3927);function d(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}const m=r.forwardRef(d);var u=s(8662),g=s(9895),x=s(4692),p=s(3893),h=s(8267),y=s(579);const b=e=>{let{product:t,onClick:s}=e;return(0,y.jsxs)("div",{onClick:()=>{s(t.id)},className:"border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer bg-white",children:[(0,y.jsx)("div",{className:"aspect-square bg-gray-100 rounded-md mb-3 flex items-center justify-center overflow-hidden",children:t.image?(0,y.jsx)("img",{src:t.image,alt:t.name,className:"w-full h-full object-cover"}):(0,y.jsx)(h.A,{className:"h-8 w-8 text-gray-400"})}),(0,y.jsxs)("div",{className:"space-y-2",children:[(0,y.jsxs)("div",{children:[(0,y.jsx)("h5",{className:"font-medium text-gray-900 text-sm truncate",title:t.name,children:t.name}),(0,y.jsxs)("p",{className:"text-xs text-gray-500 truncate",title:t.sku,children:["SKU: ",t.sku]})]}),(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsx)("div",{className:"text-sm font-semibold text-gray-900",children:(0,p.vv)(t.price)}),(0,y.jsx)(x.A,{status:t.status})]}),(0,y.jsxs)("div",{className:"text-xs text-gray-500",children:["Stock: ",t.stock]})]})]})},v=(0,r.memo)(b);var j=s(2517),f=s(6365);function w(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"}))}const N=r.forwardRef(w);var A=s(9531);function C(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const k=r.forwardRef(C);var S=s(9248);const I=e=>{let{subcategory:t,onDelete:s,onToggleVisibility:a,onProductClick:i}=e;const[l,c]=(0,r.useState)(!1),o=t.products||[];return(0,y.jsxs)("div",{className:"border border-gray-200 rounded-lg bg-white",children:[(0,y.jsx)("div",{className:"p-4 border-b border-gray-100 bg-gray-25",children:(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,y.jsx)("button",{onClick:()=>c(!l),className:"p-1 rounded-full hover:bg-gray-200 focus:outline-none",children:l?(0,y.jsx)(j.A,{className:"h-4 w-4 text-gray-500"}):(0,y.jsx)(f.A,{className:"h-4 w-4 text-gray-500"})}),(0,y.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,y.jsx)(N,{className:"h-5 w-5 text-blue-500"}),(0,y.jsxs)("div",{children:[(0,y.jsx)("h4",{className:"font-medium text-gray-900",children:t.name}),(0,y.jsx)("p",{className:"text-sm text-gray-600",children:t.description})]})]})]}),(0,y.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,y.jsx)(x.A,{status:t.status}),(0,y.jsxs)("div",{className:"text-sm text-gray-500",children:[t.productCount," products"]}),(0,y.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,y.jsx)("button",{onClick:()=>a(t.id,"supplier"),className:"p-1.5 rounded-full transition-colors "+(t.visibleInSupplierApp?"text-green-600 bg-green-100 hover:bg-green-200":"text-gray-400 bg-gray-100 hover:bg-gray-200"),title:(t.visibleInSupplierApp?"Hide from":"Show in")+" Supplier App",children:t.visibleInSupplierApp?(0,y.jsx)(A.A,{className:"h-3 w-3"}):(0,y.jsx)(k,{className:"h-3 w-3"})}),(0,y.jsx)("button",{onClick:()=>a(t.id,"customer"),className:"p-1.5 rounded-full transition-colors "+(t.visibleInCustomerApp?"text-blue-600 bg-blue-100 hover:bg-blue-200":"text-gray-400 bg-gray-100 hover:bg-gray-200"),title:(t.visibleInCustomerApp?"Hide from":"Show in")+" Customer App",children:t.visibleInCustomerApp?(0,y.jsx)(A.A,{className:"h-3 w-3"}):(0,y.jsx)(k,{className:"h-3 w-3"})})]}),(0,y.jsx)(n.A,{variant:"outline",size:"sm",icon:(0,y.jsx)(S.A,{className:"h-3 w-3"}),onClick:()=>{window.confirm(`Are you sure you want to delete the subcategory "${t.name}"? This will also delete all products within it.`)&&s(t.id)},className:"text-red-600 border-red-300 hover:bg-red-50 px-2 py-1",children:"Delete"})]})]})}),l&&(0,y.jsx)("div",{className:"p-4",children:0===o.length?(0,y.jsx)("div",{className:"text-center py-6 text-gray-500",children:(0,y.jsx)("div",{className:"text-sm",children:"No products in this subcategory"})}):(0,y.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:o.map((e=>(0,y.jsx)(v,{product:e,onClick:i},e.id)))})})]})},E=(0,r.memo)(I);var D=s(934);const L=e=>{let{category:t,onDelete:s,onDeleteSubcategory:a,onToggleVisibility:i,onToggleSubcategoryVisibility:c,onProductClick:o,onAddSubcategory:d}=e;const[m,g]=(0,r.useState)(!0),p=t.subcategories||[];return(0,y.jsxs)(l.A,{className:"overflow-hidden",children:[(0,y.jsx)("div",{className:"p-6 border-b border-gray-200 bg-gray-50",children:(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,y.jsx)("button",{onClick:()=>g(!m),className:"p-1 rounded-full hover:bg-gray-200 focus:outline-none",children:m?(0,y.jsx)(j.A,{className:"h-5 w-5 text-gray-500"}):(0,y.jsx)(f.A,{className:"h-5 w-5 text-gray-500"})}),(0,y.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,y.jsx)(D.A,{className:"h-6 w-6 text-primary"}),(0,y.jsxs)("div",{children:[(0,y.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t.name}),(0,y.jsx)("p",{className:"text-sm text-gray-600",children:t.description})]})]})]}),(0,y.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,y.jsx)(x.A,{status:t.status}),(0,y.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,y.jsxs)("span",{children:[t.subcategoryCount," subcategories"]}),(0,y.jsx)("span",{className:"mx-2",children:"\u2022"}),(0,y.jsxs)("span",{children:[t.productCount," products"]})]}),(0,y.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,y.jsx)("button",{onClick:()=>i(t.id,"supplier"),className:"p-2 rounded-full transition-colors "+(t.visibleInSupplierApp?"text-green-600 bg-green-100 hover:bg-green-200":"text-gray-400 bg-gray-100 hover:bg-gray-200"),title:(t.visibleInSupplierApp?"Hide from":"Show in")+" Supplier App",children:t.visibleInSupplierApp?(0,y.jsx)(A.A,{className:"h-4 w-4"}):(0,y.jsx)(k,{className:"h-4 w-4"})}),(0,y.jsx)("button",{onClick:()=>i(t.id,"customer"),className:"p-2 rounded-full transition-colors "+(t.visibleInCustomerApp?"text-blue-600 bg-blue-100 hover:bg-blue-200":"text-gray-400 bg-gray-100 hover:bg-gray-200"),title:(t.visibleInCustomerApp?"Hide from":"Show in")+" Customer App",children:t.visibleInCustomerApp?(0,y.jsx)(A.A,{className:"h-4 w-4"}):(0,y.jsx)(k,{className:"h-4 w-4"})})]}),(0,y.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,y.jsx)(n.A,{variant:"outline",size:"sm",icon:(0,y.jsx)(u.A,{className:"h-4 w-4"}),onClick:()=>d(t),children:"Add Subcategory"}),(0,y.jsx)(n.A,{variant:"outline",size:"sm",icon:(0,y.jsx)(S.A,{className:"h-4 w-4"}),onClick:()=>{window.confirm(`Are you sure you want to delete the category "${t.name}"? This will also delete all subcategories and products within it.`)&&s(t.id)},className:"text-red-600 border-red-300 hover:bg-red-50",children:"Delete"})]})]})]})}),m&&(0,y.jsx)("div",{className:"p-6",children:0===p.length?(0,y.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,y.jsx)(D.A,{className:"h-12 w-12 mx-auto mb-3 text-gray-300"}),(0,y.jsx)("p",{className:"text-lg font-medium mb-1",children:"No subcategories yet"}),(0,y.jsx)("p",{className:"text-sm",children:"Add subcategories to organize products within this category"}),(0,y.jsx)(n.A,{variant:"outline",size:"sm",icon:(0,y.jsx)(u.A,{className:"h-4 w-4"}),onClick:()=>d(t),className:"mt-4",children:"Add First Subcategory"})]}):(0,y.jsx)("div",{className:"space-y-4",children:p.map((e=>(0,y.jsx)(E,{subcategory:e,onDelete:a,onToggleVisibility:c,onProductClick:o},e.id)))})})]})},F=(0,r.memo)(L),M=e=>{let{categories:t,onDeleteCategory:s,onDeleteSubcategory:r,onToggleCategoryVisibility:a,onToggleSubcategoryVisibility:i,onProductClick:l,onAddSubcategory:n}=e;return 0===t.length?(0,y.jsxs)("div",{className:"text-center py-12",children:[(0,y.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No categories found"}),(0,y.jsx)("div",{className:"text-gray-400 text-sm",children:"Create your first category to get started with organizing your products"})]}):(0,y.jsx)("div",{className:"space-y-6",children:t.map((e=>(0,y.jsx)(F,{category:e,onDelete:s,onDeleteSubcategory:r,onToggleVisibility:a,onToggleSubcategoryVisibility:i,onProductClick:l,onAddSubcategory:n},e.id)))})},V=(0,r.memo)(M);var T=s(6773);const B=e=>{let{categoryId:t,onSubmit:s,onCancel:a,isLoading:i=!1}=e;const[l,c]=(0,r.useState)({name:"",description:"",status:"active",categoryId:t,visibleInSupplierApp:!0,visibleInCustomerApp:!0}),[o,d]=(0,r.useState)({}),m=e=>{const{name:t,value:s}=e.target;c((e=>({...e,[t]:s}))),o[t]&&d((e=>({...e,[t]:""})))};return(0,y.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{const e=(0,T.l)({name:l.name,description:l.description},{name:[T.tU.required("Subcategory name is required")],description:[T.tU.required("Description is required")]});return d(e),0===Object.keys(e).length})()&&s(l)},className:"space-y-6",children:[(0,y.jsxs)("div",{className:"space-y-4",children:[(0,y.jsxs)("div",{children:[(0,y.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:["Subcategory Name ",(0,y.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,y.jsx)("input",{type:"text",id:"name",name:"name",value:l.name,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.name?"border-red-300":""),placeholder:"Enter subcategory name"}),o.name&&(0,y.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.name})]}),(0,y.jsxs)("div",{children:[(0,y.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:["Description ",(0,y.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,y.jsx)("textarea",{id:"description",name:"description",rows:3,value:l.description,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.description?"border-red-300":""),placeholder:"Enter subcategory description"}),o.description&&(0,y.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.description})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,y.jsxs)("select",{id:"status",name:"status",value:l.status,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm",children:[(0,y.jsx)("option",{value:"active",children:"Active"}),(0,y.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,y.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,y.jsx)("div",{children:(0,y.jsxs)("label",{className:"flex items-center",children:[(0,y.jsx)("input",{type:"checkbox",name:"visibleInSupplierApp",checked:l.visibleInSupplierApp,onChange:e=>c((t=>({...t,visibleInSupplierApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,y.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Supplier App"})]})}),(0,y.jsx)("div",{children:(0,y.jsxs)("label",{className:"flex items-center",children:[(0,y.jsx)("input",{type:"checkbox",name:"visibleInCustomerApp",checked:l.visibleInCustomerApp,onChange:e=>c((t=>({...t,visibleInCustomerApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,y.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Customer App"})]})})]})]}),(0,y.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,y.jsx)(n.A,{type:"button",variant:"outline",onClick:a,disabled:i,children:"Cancel"}),(0,y.jsx)(n.A,{type:"submit",loading:i,children:"Add Subcategory"})]})]})};var O=s(724),$=s(9705);const U=()=>{const e=(0,a.Zp)(),{categories:t,isLoading:s,fetchCategories:d}=(0,g.Ck)(),{showNotification:x}=(0,$.A)(),[p,h]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[j,f]=(0,r.useState)(null),[w,N]=(0,r.useState)("all"),[A,C]=(0,r.useState)("all"),k=(0,r.useMemo)((()=>t.filter((e=>("all"===w||e.status===w)&&(!("supplier"===A&&!e.visibleInSupplierApp)&&!("customer"===A&&!e.visibleInCustomerApp))))),[t,w,A]),S=(0,r.useCallback)((async e=>{try{console.log("Adding category:",e),h(!1),await d(),x({type:"success",title:"Success",message:"Category added successfully"})}catch(t){x({type:"error",title:"Error",message:"Failed to add category"})}}),[d,x]),I=(0,r.useCallback)((async e=>{try{console.log("Adding subcategory:",e),v(!1),f(null),await d(),x({type:"success",title:"Success",message:"Subcategory added successfully"})}catch(t){x({type:"error",title:"Error",message:"Failed to add subcategory"})}}),[d,x]),E=(0,r.useCallback)((async e=>{try{console.log("Deleting category:",e),await d(),x({type:"success",title:"Success",message:"Category deleted successfully"})}catch(t){x({type:"error",title:"Error",message:"Failed to delete category"})}}),[d,x]),D=(0,r.useCallback)((async e=>{try{console.log("Deleting subcategory:",e),await d(),x({type:"success",title:"Success",message:"Subcategory deleted successfully"})}catch(t){x({type:"error",title:"Error",message:"Failed to delete subcategory"})}}),[d,x]),L=(0,r.useCallback)((async(e,t)=>{try{console.log("Toggling category visibility:",e,t),await d(),x({type:"success",title:"Success",message:"Category visibility updated"})}catch(s){x({type:"error",title:"Error",message:"Failed to update category visibility"})}}),[d,x]),F=(0,r.useCallback)((async(e,t)=>{try{console.log("Toggling subcategory visibility:",e,t),await d(),x({type:"success",title:"Success",message:"Subcategory visibility updated"})}catch(s){x({type:"error",title:"Error",message:"Failed to update subcategory visibility"})}}),[d,x]),M=(0,r.useCallback)((t=>{e(O.b.getProductDetailsRoute(t))}),[e]),T=(0,r.useCallback)((e=>{f(e),v(!0)}),[]);return s?(0,y.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,y.jsx)(o.A,{size:"lg"})}):(0,y.jsxs)("div",{className:"space-y-6",children:[(0,y.jsx)(i.A,{title:"Category Management",description:"Manage categories, subcategories, and their visibility in supplier and customer apps",actions:(0,y.jsxs)("div",{className:"flex space-x-3",children:[(0,y.jsx)(n.A,{variant:"outline",icon:(0,y.jsx)(m,{className:"h-5 w-5"}),onClick:()=>{},children:"Filters"}),(0,y.jsx)(n.A,{icon:(0,y.jsx)(u.A,{className:"h-5 w-5"}),onClick:()=>h(!0),children:"Add Category"})]})}),(0,y.jsx)(l.A,{children:(0,y.jsxs)("div",{className:"flex flex-wrap gap-4 p-4",children:[(0,y.jsxs)("div",{children:[(0,y.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,y.jsxs)("select",{value:w,onChange:e=>N(e.target.value),className:"rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm",children:[(0,y.jsx)("option",{value:"all",children:"All Status"}),(0,y.jsx)("option",{value:"active",children:"Active"}),(0,y.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Visibility"}),(0,y.jsxs)("select",{value:A,onChange:e=>C(e.target.value),className:"rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm",children:[(0,y.jsx)("option",{value:"all",children:"All Apps"}),(0,y.jsx)("option",{value:"supplier",children:"Supplier App Only"}),(0,y.jsx)("option",{value:"customer",children:"Customer App Only"})]})]})]})}),(0,y.jsx)(V,{categories:k,onDeleteCategory:E,onDeleteSubcategory:D,onToggleCategoryVisibility:L,onToggleSubcategoryVisibility:F,onProductClick:M,onAddSubcategory:T}),(0,y.jsx)(c.A,{isOpen:p,onClose:()=>h(!1),title:"Add Category",children:(0,y.jsx)(g.W9,{onSubmit:S,onCancel:()=>h(!1)})}),(0,y.jsx)(c.A,{isOpen:b,onClose:()=>{v(!1),f(null)},title:`Add Subcategory to ${(null===j||void 0===j?void 0:j.name)||""}`,children:j&&(0,y.jsx)(B,{categoryId:j.id,onSubmit:I,onCancel:()=>{v(!1),f(null)}})})]})},z=(0,r.memo)(U)},3893:(e,t,s)=>{s.d(t,{Yq:()=>r,v7:()=>i,vv:()=>a});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const s=new Date(e),r={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",r).format(s)}catch(s){return console.error("Error formatting date:",s),e}},a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(s,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(r){return console.error("Error formatting currency:",r),`${t} ${e.toFixed(2)}`}},i=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,t)).toFixed(2))} ${["Bytes","KB","MB","GB","TB"][t]}`}},4692:(e,t,s)=>{s.d(t,{A:()=>o});s(5043);var r=s(4538),a=s(7012),i=s(7098),l=s(5889),n=s(3867),c=s(579);const o=e=>{let{status:t,type:s="user",className:o=""}=e;if(!t)return(0,c.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${o}`,children:"Unknown"});const d=t.toLowerCase();let m="",u=null;"active"===d||"verified"===d||"completed"===d?(m="bg-green-100 text-green-800",u=(0,c.jsx)(r.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(m="bg-blue-100 text-blue-800",u=(0,c.jsx)(a.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(m="bg-red-100 text-red-800",u=(0,c.jsx)(i.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(m="bg-purple-100 text-purple-800",u=(0,c.jsx)(l.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(m="bg-yellow-100 text-yellow-800",u=(0,c.jsx)(n.A,{className:"w-4 h-4 mr-1"})):m="bg-gray-100 text-gray-800";const g=t?t.charAt(0).toUpperCase()+t.slice(1):"Unknown";return(0,c.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${m} ${o}`,children:[u,g]})}},5889:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const i=r.forwardRef(a)},7012:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(a)},8267:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"}))}const i=r.forwardRef(a)}}]);
//# sourceMappingURL=667.6efa8e59.chunk.js.map