"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[906],{4791:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5043);function n(e,t){let{title:r,titleId:n,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const l=a.forwardRef(n)},5889:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5043);function n(e,t){let{title:r,titleId:n,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const l=a.forwardRef(n)},7012:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5043);function n(e,t){let{title:r,titleId:n,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=a.forwardRef(n)},7098:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5043);function n(e,t){let{title:r,titleId:n,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=a.forwardRef(n)},8761:(e,t,r)=>{r.r(t),r.d(t,{default:()=>v});var a=r(5043),n=r(3216),l=r(3593),i=r(7907),s=r(2806),o=r(3927),d=r(4791),c=r(724),u=r(245),m=r(579);const h=()=>{const e=(0,n.Zp)(),{orders:t,isLoading:r}=(0,u.h)(),[h,v]=(0,a.useState)("all"),[w,f]=(0,a.useState)(!1),k=(0,a.useMemo)((()=>"all"===h?t:t.filter((e=>e.status===h))),[t,h]),g=(0,a.useCallback)((t=>{e(c.b.getOrderDetailsRoute(t.id))}),[e]),p=(0,a.useCallback)((()=>{f(!0),setTimeout((()=>{f(!1),console.log("Exporting orders...")}),1500)}),[]);return(0,m.jsxs)("div",{className:"space-y-6",children:[(0,m.jsx)(s.A,{title:"Orders",description:"Manage and track all orders in the system",actions:(0,m.jsx)(i.A,{variant:"outline",icon:(0,m.jsx)(d.A,{className:"h-5 w-5"}),onClick:p,loading:w,children:"Export Orders"})}),(0,m.jsxs)(l.A,{children:[(0,m.jsx)(u.Ji,{activeFilter:h,onFilterChange:v}),r?(0,m.jsx)("div",{className:"flex justify-center py-8",children:(0,m.jsx)(o.A,{})}):(0,m.jsx)(u.F9,{orders:k,onOrderClick:g,title:`${h.charAt(0).toUpperCase()+h.slice(1)} Orders (${k.length})`})]})]})},v=(0,a.memo)(h)}}]);
//# sourceMappingURL=906.3427b167.chunk.js.map