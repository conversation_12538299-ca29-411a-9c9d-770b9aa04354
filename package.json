{"name": "admin-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "axios": "^1.6.2", "chart.js": "^4.4.1", "jotai": "^2.12.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "start:mock": "env-cmd -f .env.mock react-scripts start", "start:prod": "env-cmd -f .env.production react-scripts start", "build": "react-scripts build", "build:mock": "env-cmd -f .env.mock react-scripts build", "build:prod": "env-cmd -f .env.production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "env-cmd": "^10.1.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}