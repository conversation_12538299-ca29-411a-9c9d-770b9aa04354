{"version": 3, "file": "static/js/765.ef850156.chunk.js", "mappings": "oKA+BO,MAuMP,EAvM+B,WAA2C,IAA1CA,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjE,MAAM,oBAAEG,GAAsB,EAAI,gBAAEC,GAAkB,EAAI,QAAEC,GAAYN,GAClE,iBAAEO,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAqB,CACvDC,UAAU,EACVC,MAAO,KACPC,UAAW,OAIPC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BN,EAAc,CACZE,UAAU,EACVC,MAAO,KACPC,UAAW,MACX,GACD,IAGGG,GAA0BD,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KACvD,MAAMC,GAAWC,EAAAA,EAAAA,IACfP,EACAT,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAkBN,OAfAO,EAAc,CACZE,UAAU,EACVC,MAAOM,EACPL,UAAW,SACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,GAGVC,CAAQ,GACd,CAACf,EAAqBC,EAAiBE,EAAkBD,IAGtDqB,GAAiCX,EAAAA,EAAAA,cAAY,CACjDY,EACAJ,EACAK,EACAX,KAEA,MAAMY,GAAkBC,EAAAA,EAAAA,IAAsBH,EAAOJ,EAASK,GAqB9D,OAnBAnB,EAAc,CACZE,UAAU,EACVC,MAAOiB,EACPhB,UAAW,gBACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,mBACPC,QAASM,EAAgBN,UAIzBlB,GACFA,EAAQwB,EAAiBZ,GAGpBY,CAAe,GACrB,CAAC1B,EAAqBG,EAAkBD,IAGrC0B,GAA2BhB,EAAAA,EAAAA,cAAY,CAC3CH,EACAoB,EACAf,MAEAgB,EAAAA,EAAAA,IACErB,EACAoB,EACA7B,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAGNO,EAAc,CACZE,UAAU,EACVC,QACAC,UAAW,UACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,EACjB,GACC,CAACd,EAAqBC,EAAiBE,EAAkBD,IAGtD6B,GAAqBnB,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KAClD,MAAMkB,EAAWvB,aAAiBY,MAAQZ,EAAQ,IAAIY,MAAMY,OAAOxB,IA2BnE,OAzBAH,EAAc,CACZE,UAAU,EACVC,MAAOuB,EACPtB,UAAW,aACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,QACPC,QAASY,EAASZ,UAIlBnB,IACFqB,EAAAA,EAAAA,IAAYU,EAAUlB,IAGxBoB,EAAAA,EAAAA,IAASF,EAAUlB,GAEfZ,GACFA,EAAQO,EAAOK,GAGVkB,CAAQ,GACd,CAAChC,EAAqBC,EAAiBE,EAAkBD,IAGtDiC,GAAoBvB,EAAAA,EAAAA,cAAYwB,MACpCC,EACAvB,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAI,EAAwBJ,EAAOK,GACxB,IACT,IACC,CAACH,EAAYE,IAGVyB,GAAwB1B,EAAAA,EAAAA,cAAYwB,MACxCC,EACAR,EACAf,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAmB,EAAyBnB,EAAOoB,EAAef,GACxC,IACT,IACC,CAACH,EAAYiB,IAEhB,MAAO,IAEFvB,EAGHW,eAAgBH,EAChBc,sBAAuBJ,EACvBO,gBAAiBF,EACjBG,qBACApB,aAGAwB,oBACAG,wBAGAC,WAAa9B,GACXA,GAA0B,kBAAVA,GAAsB,WAAYA,EACpD+B,kBAAoB/B,GAClBA,GAA0B,kBAAVA,GAAsB,UAAWA,EAEvD,C,8EChNA,MAAMgC,EAA2EC,IAAA,IAAC,MAChFjC,EAAK,WACLkC,GACDD,EAAA,OACCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SAAC,0BACxDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDAAgDC,SAC1DrC,EAAMW,SAAW,kCAEpB2B,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,6EAA4EC,SACvF,gBAGG,EAMD,SAASG,EACdC,GAEC,IADDC,EAA2BtD,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAE/B,MACEuD,SAAUC,EAAoBZ,EAAoB,QAClDvC,EAAO,gBACPD,GAAkB,EAAI,QACtBa,GACEqC,EAEEG,GAAmBC,EAAAA,EAAAA,aAAmB,CAACC,EAAOC,KAiBhDV,EAAAA,EAAAA,KAACW,EAAAA,EAAa,CACZN,UAAUL,EAAAA,EAAAA,KAACM,EAAiB,CAAC5C,MAAO,IAAIY,MAASsB,WAAYA,IAAMgB,OAAOC,SAASC,WACnF3D,QAlBgB4D,CAACrD,EAAcsD,KAE7B9D,IACFqB,EAAAA,EAAAA,IAAYb,EAAOK,GAAWoC,EAAUc,aAAed,EAAUe,KAAM,CACrEC,eAAgBH,EAAUG,eAC1BC,eAAe,IAKfjE,GACFA,EAAQO,EAAOsD,EACjB,EAMuBjB,UAErBC,EAAAA,EAAAA,KAACG,EAAS,IAAMM,EAAeC,IAAKA,QAQ1C,OAFAH,EAAiBU,YAAc,qBAAqBd,EAAUc,aAAed,EAAUe,QAEhFX,CACT,CAKO,MAsBP,G,yKCtFA,MAsPA,GAAeL,EAAAA,EAAAA,KAtPamB,KAAO,IAADC,EAChC,MAAOC,EAAUC,IAAehE,EAAAA,EAAAA,UAAS,CACvCiE,MAAO,GACPC,SAAU,GACVC,YAAY,KAEPC,EAAQC,IAAarE,EAAAA,EAAAA,UAAiC,CAAC,IACvDsE,EAAWC,IAAgBvE,EAAAA,EAAAA,WAAS,IACrC,MAAEwE,IAAUC,EAAAA,EAAAA,MACZ,UAAEC,EAAS,YAAEC,IAAgB9E,EAAAA,EAAAA,KAC7B+E,GAAWC,EAAAA,EAAAA,MACXxB,GAAWyB,EAAAA,EAAAA,OAGX,gBACJvD,EAAe,sBACfQ,EAAqB,WACrB3B,IACE2E,EAAAA,EAAAA,GAAgB,CAClBtF,qBAAqB,EACrBC,iBAAiB,IAGbsF,EAAgB3B,EAAS4B,MACzBC,GAAoB,OAAbF,QAAa,IAAbA,GAAmB,QAANlB,EAAbkB,EAAeE,YAAI,IAAApB,OAAN,EAAbA,EAAqBqB,WAAYC,EAAAA,EAAOC,UAE/CC,EAAgBC,IACpB,MAAM,KAAE7B,EAAI,MAAE8B,EAAK,KAAE7E,EAAI,QAAE8E,GAAYF,EAAEG,OACzC1B,GAAY2B,IAAI,IACXA,EACH,CAACjC,GAAgB,aAAT/C,EAAsB8E,EAAUD,MAItCpB,EAAOV,IACTW,GAAUsB,IAAI,IAAUA,EAAM,CAACjC,GAAO,MACxC,EAyEF,OACElB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sFAAqFC,UAClGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2GAA0GC,SAAC,kBAGzHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oDAAmDC,SAAC,iBAGlEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAC,8DAKxDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,YAAYsD,SAtFf/D,UACnB0D,EAAEM,iBACFzF,IAGA,MAAM0F,GAAmBC,EAAAA,EAAAA,GAAa,CACpC9B,MAAOF,EAASE,MAChBC,SAAUH,EAASG,UAClB,CACDD,MAAO,CAAC+B,EAAAA,GAAgBC,WAAYD,EAAAA,GAAgB/B,SACpDC,SAAU,CAAC8B,EAAAA,GAAgBC,cAG7B,GAAIC,OAAOC,KAAKL,GAAkBvG,OAAS,EAEzC,YADA8E,EAAUyB,GAIZvB,GAAa,GAEb,MAAM6B,QAAerE,GAAsBF,gBACnC2C,EAAM,CACVP,MAAOF,EAASE,MAChBC,SAAUH,EAASG,SACnBC,WAAYJ,EAASI,aAGvBQ,EAAY,oBACZC,EAASM,EAAM,CAAEmB,SAAS,KACnB,KACN,CAACpF,EAAOJ,KACTwD,GAAUsB,IAAI,IAAUA,EAAM,CAAC1E,GAAQJ,KAAW,GACjD,cAEH0D,GAAa,GAER6B,GACHE,QAAQpG,MAAM,eAChB,EAgDyDqC,SAAA,EACjDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAO+D,QAAQ,QAAQjE,UAAU,0CAAyCC,SAAC,mBAG3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SACEgE,GAAG,QACH9C,KAAK,QACL/C,KAAK,QACL8F,aAAa,QACbR,UAAQ,EACRT,MAAOzB,EAAgB,MACvB2C,SAAUpB,EACVhD,UAAW,iDAAiD8B,EAAc,MAAI,iBAAmB,oIACjGuC,YAAY,2BAEbvC,EAAc,QACb5B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAE6B,EAAc,eAK9D/B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAO+D,QAAQ,WAAWjE,UAAU,0CAAyCC,SAAC,cAG9EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SACEgE,GAAG,WACH9C,KAAK,WACL/C,KAAK,WACL8F,aAAa,mBACbR,UAAQ,EACRT,MAAOzB,EAAmB,SAC1B2C,SAAUpB,EACVhD,UAAW,iDAAiD8B,EAAiB,SAAI,iBAAmB,oIACpGuC,YAAY,qDAEbvC,EAAiB,WAChB5B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAE6B,EAAiB,kBAKjE/B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,SACEgE,GAAG,aACH9C,KAAK,aACL/C,KAAK,WACL8E,QAAS1B,EAASI,WAClBuC,SAAUpB,EACVhD,UAAU,qEAEZE,EAAAA,EAAAA,KAAA,SAAO+D,QAAQ,aAAajE,UAAU,mCAAkCC,SAAC,oBAK3EC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,UAASC,UACtBC,EAAAA,EAAAA,KAAA,UACEF,UAAU,2DACVG,QAASA,OAASF,SACnB,2BAMLC,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAACoE,EAAAA,EAAM,CACLjG,KAAK,SACLkG,QAAQ,UACRC,WAAS,EACTC,KAAK,KACLC,QAAS1C,EACT2C,SAAU3C,EAAU/B,SACrB,kBAMLF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,UACjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCAEjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCAAsCC,UACnDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8BAA6BC,SAAC,2BAIlDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,WAAOC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,+BACxCF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,cAAUC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,uBAG7CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,UACE7B,KAAK,SACL8B,QAnJUyE,KACxB,MAAMC,EAAUC,EAAAA,GAAiBC,WAAW,iBACtCC,EAAWF,EAAAA,GAAiBC,WAAW,qBACvCE,EAAWH,EAAAA,GAAiBC,WAAW,sBACvCG,EAAWJ,EAAAA,GAAiBC,WAAW,kBAE7C,GAAIF,GAAWG,GAAYC,GAAYC,EACrC,IACEC,EAAAA,EAAOC,aACP/C,EAAY,8DAEZX,EAAY,CACVC,MAAO,GACPC,SAAU,GACVC,YAAY,IAEdE,EAAU,CAAC,GACXjE,GACF,CAAE,MAAOF,GACPqB,EAAgBrB,GAAO,CAACe,EAAOJ,KAC7BwD,GAAUsB,IAAI,IAAUA,EAAM,CAAC1E,GAAQJ,KAAW,GAEtD,MAEA6D,EAAU,qDACZ,EA2HcpC,UAAU,yDAAwDC,SACnE,yBAGDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAC,0FAQhD,GAKkC,CAC1CM,SAAUV,IAAA,IAAC,MAAEjC,EAAK,WAAEkC,GAAYD,EAAA,OAC9BK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sFAAqFC,UAClGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BC,UACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6BAA4BC,SAAC,sBAC3CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAC9BrC,EAAMW,SAAW,oDAEpB2B,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,kFAAiFC,SAC5F,sBAKD,EAERhC,QAAS,a,2CClRJ,MAAMoH,EAAgB1D,GACR,mDACD2D,KAAK3D,GAGZ4D,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAO9H,GACP,OAAO,CACT,GAGWgI,EAAc1C,GACX,OAAVA,QAA4BhG,IAAVgG,IACD,kBAAVA,EAA2BA,EAAM2C,OAAO5I,OAAS,GACxD6I,MAAMC,QAAQ7C,IAAeA,EAAMjG,OAAS,GAYrC+I,EAAa9C,GACjB,WAAWoC,KAAKpC,GAGZ+C,EAAa/C,GACjB,sBAAsBoC,KAAKpC,GAGvBgD,EAAkBhD,GACtB,iBAAiBoC,KAAKpC,GAGlBiD,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAAC7E,EAAkB8E,IAC1C9E,IAAa8E,EAGTC,EAAoB/E,KAE3BA,EAAS3E,OAAS,OAGjB,QAAQqI,KAAK1D,OAGb,QAAQ0D,KAAK1D,OAGb,QAAQ0D,KAAK1D,MAGb,sCAAsC0D,KAAK1D,MAwBrC6B,EAAeA,CAC1BmD,EACAlD,KAEA,MAAM5B,EAA2C,CAAC,EAUlD,OARA8B,OAAOiD,QAAQnD,GAAiBoD,SAAQjH,IAAyB,IAAvBkH,EAAWC,GAAMnH,EACzD,MAAMoH,EAAMF,EACNnJ,EA1BmBsJ,EAC3BC,EACAjE,EACA8D,EACAvF,KAEA,MAAM2F,EAAYtB,MAAMC,QAAQiB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMK,KAAQD,EACjB,IAAKC,EAAKC,UAAUpE,EAAOzB,GACzB,OAAO4F,EAAK9I,QAIhB,MAAO,EAAE,EAYO2I,CAAcH,EAAWH,EAAOK,GAAMD,EAAOJ,GACvDhJ,IACFkE,EAAOmF,GAAOrJ,EAChB,IAGKkE,CAAM,EAIF4B,EAAkB,CAC7BC,SAAU,WAA2C,MAAsB,CACzE2D,UAAW1B,EACXrH,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAED2E,MAAO,WAAuD,MAAsB,CAClF2F,UAAWjC,EACX9G,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDwI,MAAO,WAAsD,MAAsB,CACjF8B,UAAW/B,EACXhH,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAED0I,IAAK,WAA6C,MAAsB,CACtE4B,UAAW7B,EACXlH,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDuK,UAAWA,CAACC,EAAajJ,KAAgB,CACvC+I,UAAYpE,GA3GSqE,EAACrE,EAAesE,IAChCtE,EAAMjG,QAAUuK,EA0GSD,CAAUrE,EAAOsE,GAC/CjJ,QAASA,GAAW,oBAAoBiJ,iBAG1CC,UAAWA,CAACC,EAAanJ,KAAgB,CACvC+I,UAAYpE,GA5GSuE,EAACvE,EAAewE,IAChCxE,EAAMjG,QAAUyK,EA2GSD,CAAUvE,EAAOwE,GAC/CnJ,QAASA,GAAW,wBAAwBmJ,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EL,UAAWtB,EACXzH,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAED4K,QAAS,WAAwD,MAAsB,CACrFN,UAAWrB,EACX1H,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAED6K,aAAc,WAAwD,MAAsB,CAC1FP,UAAWpB,EACX3H,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDqJ,KAAM,WAA8C,MAAsB,CACxEiB,UAAWnB,EACX5H,QAFoBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAED4E,SAAU,WAA2H,MAAsB,CACzJ0F,UAAWX,EACXpI,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAED8K,cAAe,WAA2C,MAAsB,CAC9ER,UAAWA,CAACpE,EAAezB,IAAmBgF,EAAiBvD,EAAe,OAARzB,QAAQ,IAARA,OAAQ,EAARA,EAAUiF,iBAChFnI,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAED+K,qBAAsB,WAA2C,MAAsB,CACrFT,UAAWA,CAACpE,EAAezB,IAAmBgF,EAAiBvD,EAAe,OAARzB,QAAQ,IAARA,OAAQ,EAARA,EAAUG,UAChFrD,QAFoCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDgL,IAAK,WAA6C,MAAsB,CACtEV,UAAYpE,GAAkB,sBAAsBoC,KAAKpC,GACzD3E,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDiL,MAAO,WAA+C,MAAsB,CAC1EX,UAAYpE,GAAkBA,EAAQ,GAAKA,GAAS,OACpD3E,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDkL,MAAO,WAAwD,MAAsB,CACnFZ,UAAYpE,GAAkBiF,OAAOC,UAAUlF,IAAUA,GAAS,EAClE3E,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDqL,aAAc,WAA6D,MAAsB,CAC/Ff,UAAYpE,GAAkBiF,OAAOC,UAAUlF,IAAUA,GAAS,EAClE3E,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDsL,iBAAkB,WAAuE,MAAsB,CAC7GhB,UAAWA,CAACe,EAAsB5G,KAC3BA,IAAaA,EAASyG,OACpBG,GAAgB5G,EAASyG,MAElC3J,QALgCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDuL,cAAe,WAAkD,MAAsB,CACrFjB,UAAYpE,GAAiB4C,MAAMC,QAAQ7C,IAAUA,EAAMjG,OAAS,EACpEsB,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAEDwL,WAAY,eAACC,EAAgBzL,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEsK,UAAYpE,KACL4C,MAAMC,QAAQ7C,IACZA,EAAMjG,QAAUwL,EAEzBlK,SALkDvB,UAAAC,OAAA,EAAAD,UAAA,QAAAE,IAK9B,WAAWuL,mBAChC,E,yDCxMH,MAAMnE,EAAgCzE,IAmB/B,IAnBgC,SACrCI,EAAQ,QACRsE,EAAU,UAAS,KACnBE,EAAO,KAAI,UACXzE,EAAY,GAAE,QACdG,EAAO,SACPwE,GAAW,EAAK,KAChBtG,EAAO,SAAQ,KACfqK,EAAI,aACJC,EAAe,OAAM,UACrBnE,GAAY,EAAK,QACjBE,GAAU,EAAK,QACfkE,GAAU,EAAK,KACfC,EAAI,OACJzF,EAAM,IACN0F,EAAG,MACHxK,EAAK,UACLyK,EAAS,OACTC,GACDnJ,EACC,MAwBMoJ,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRxE,QAAS,0EACTyE,KAAM,2EACNC,KAAM,kFAiBWhF,WAdC,CAClBiF,GAAI,oBACJC,GAAI,sBACJC,GAAI,oBACJC,GAAI,wBACJC,GAAI,qBAUUnF,WAPQE,EAAW,gCAAkC,yBAClDH,EAAY,SAAW,WACrBoE,EAAU,eAAiB,qBAS5C5I,QAGE6J,GACJ9J,EAAAA,EAAAA,MAAA+J,EAAAA,SAAA,CAAA7J,SAAA,CACGyE,IACC3E,EAAAA,EAAAA,MAAA,OACEC,UAAU,+CACV+J,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMhK,SAAA,EAElBC,EAAAA,EAAAA,KAAA,UACEF,UAAU,aACVkK,GAAG,KACHC,GAAG,KACHC,EAAE,KACFC,OAAO,eACPC,YAAY,OAEdpK,EAAAA,EAAAA,KAAA,QACEF,UAAU,aACVgK,KAAK,eACLO,EAAE,uHAKP7B,GAAyB,SAAjBC,IAA4BjE,IACnCxE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAEyI,IAGzBzI,EAEAyI,GAAyB,UAAjBC,IACPzI,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAEyI,OAM9B,OAAIG,GAEA3I,EAAAA,EAAAA,KAAA,KACE2I,KAAMA,EACN7I,UAAWiJ,EACX7F,OAAQA,EACR0F,IAAKA,IAAmB,WAAX1F,EAAsB,2BAAwBlG,GAC3DiD,QAASA,EACT7B,MAAOA,EACP,aAAYyK,EACZ,cAAaC,EAAO/I,SAEnB4J,KAOL3J,EAAAA,EAAAA,KAAA,UACE7B,KAAMA,EACN2B,UAAWiJ,EACX9I,QAASA,EACTwE,SAAUA,GAAYD,EACtBpG,MAAOA,EACP,aAAYyK,EACZ,cAAaC,EAAO/I,SAEnB4J,GACM,EAIb,GAAeW,EAAAA,EAAAA,MAAKlG,E", "sources": ["hooks/useErrorHandler.ts", "components/common/withErrorBoundary.tsx", "pages/LoginPage.tsx", "utils/validation.ts", "components/common/Button.tsx"], "sourcesContent": ["/**\r\n * Error <PERSON>ler Hook\r\n * \r\n * This hook provides React-specific error handling utilities and state management.\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { \r\n  handleApiError, \r\n  handleValidationError, \r\n  handleFormError,\r\n  logError,\r\n  reportError,\r\n  type ApiError,\r\n  type ValidationError \r\n} from '../utils/errorHandling';\r\nimport useNotification from './useNotification';\r\n\r\ninterface ErrorState {\r\n  hasError: boolean;\r\n  error: Error | ApiError | ValidationError | null;\r\n  errorType: 'api' | 'validation' | 'form' | 'general' | null;\r\n  context?: string;\r\n}\r\n\r\ninterface UseErrorHandlerOptions {\r\n  enableNotifications?: boolean;\r\n  enableReporting?: boolean;\r\n  onError?: (error: any, context?: string) => void;\r\n}\r\n\r\nexport const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {\r\n  const { enableNotifications = true, enableReporting = true, onError } = options;\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [errorState, setErrorState] = useState<ErrorState>({\r\n    hasError: false,\r\n    error: null,\r\n    errorType: null\r\n  });\r\n\r\n  // Clear error state\r\n  const clearError = useCallback(() => {\r\n    setErrorState({\r\n      hasError: false,\r\n      error: null,\r\n      errorType: null\r\n    });\r\n  }, []);\r\n\r\n  // Handle API errors\r\n  const handleApiErrorWithState = useCallback((error: any, context?: string) => {\r\n    const apiError = handleApiError(\r\n      error,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: apiError,\r\n      errorType: 'api',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return apiError;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle validation errors\r\n  const handleValidationErrorWithState = useCallback((\r\n    field: string,\r\n    message: string,\r\n    code?: string,\r\n    context?: string\r\n  ) => {\r\n    const validationError = handleValidationError(field, message, code);\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: validationError,\r\n      errorType: 'validation',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Validation Error',\r\n        message: validationError.message\r\n      });\r\n    }\r\n\r\n    if (onError) {\r\n      onError(validationError, context);\r\n    }\r\n\r\n    return validationError;\r\n  }, [enableNotifications, showNotification, onError]);\r\n\r\n  // Handle form errors\r\n  const handleFormErrorWithState = useCallback((\r\n    error: any,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ) => {\r\n    handleFormError(\r\n      error,\r\n      setFieldError,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error,\r\n      errorType: 'form',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle general errors\r\n  const handleGeneralError = useCallback((error: any, context?: string) => {\r\n    const errorObj = error instanceof Error ? error : new Error(String(error));\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: errorObj,\r\n      errorType: 'general',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorObj.message\r\n      });\r\n    }\r\n\r\n    if (enableReporting) {\r\n      reportError(errorObj, context);\r\n    }\r\n\r\n    logError(errorObj, context);\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return errorObj;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Async operation wrapper with error handling\r\n  const withErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleApiErrorWithState(error, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleApiErrorWithState]);\r\n\r\n  // Form submission wrapper with error handling\r\n  const withFormErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleFormErrorWithState(error, setFieldError, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleFormErrorWithState]);\r\n\r\n  return {\r\n    // Error state\r\n    ...errorState,\r\n    \r\n    // Error handlers\r\n    handleApiError: handleApiErrorWithState,\r\n    handleValidationError: handleValidationErrorWithState,\r\n    handleFormError: handleFormErrorWithState,\r\n    handleGeneralError,\r\n    clearError,\r\n    \r\n    // Wrapper functions\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    \r\n    // Utility functions\r\n    isApiError: (error: any): error is ApiError => \r\n      error && typeof error === 'object' && 'status' in error,\r\n    isValidationError: (error: any): error is ValidationError => \r\n      error && typeof error === 'object' && 'field' in error,\r\n  };\r\n};\r\n\r\nexport default useErrorHandler;\r\n", "/**\r\n * Higher-Order Component for Error Boundary\r\n * \r\n * This HOC wraps components with an error boundary to catch and handle errors gracefully.\r\n */\r\n\r\nimport React, { ComponentType, forwardRef } from 'react';\r\nimport ErrorBoundary from './ErrorBoundary';\r\nimport { reportError } from '../../utils/errorHandling';\r\n\r\ninterface ErrorBoundaryConfig {\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\r\n  enableReporting?: boolean;\r\n  context?: string;\r\n}\r\n\r\n/**\r\n * Default error fallback component\r\n */\r\nconst DefaultErrorFallback: React.FC<{ error: Error; resetError: () => void }> = ({ \r\n  error, \r\n  resetError \r\n}) => (\r\n  <div className=\"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50\">\r\n    <div className=\"text-red-500 text-2xl mb-2\">⚠️</div>\r\n    <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Something went wrong</h3>\r\n    <p className=\"text-red-600 text-sm mb-4 text-center max-w-md\">\r\n      {error.message || 'An unexpected error occurred'}\r\n    </p>\r\n    <button\r\n      onClick={resetError}\r\n      className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\"\r\n    >\r\n      Try Again\r\n    </button>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Higher-order component that wraps a component with an error boundary\r\n */\r\nexport function withErrorBoundary<P extends object>(\r\n  Component: ComponentType<P>,\r\n  config: ErrorBoundaryConfig = {}\r\n) {\r\n  const {\r\n    fallback: FallbackComponent = DefaultErrorFallback,\r\n    onError,\r\n    enableReporting = true,\r\n    context\r\n  } = config;\r\n\r\n  const WrappedComponent = forwardRef<any, P>((props, ref) => {\r\n    const handleError = (error: Error, errorInfo: React.ErrorInfo) => {\r\n      // Report error if enabled\r\n      if (enableReporting) {\r\n        reportError(error, context || Component.displayName || Component.name, {\r\n          componentStack: errorInfo.componentStack,\r\n          errorBoundary: true\r\n        });\r\n      }\r\n\r\n      // Call custom error handler if provided\r\n      if (onError) {\r\n        onError(error, errorInfo);\r\n      }\r\n    };\r\n\r\n    return (\r\n      <ErrorBoundary\r\n        fallback={<FallbackComponent error={new Error()} resetError={() => window.location.reload()} />}\r\n        onError={handleError}\r\n      >\r\n        <Component {...(props as any)} ref={ref} />\r\n      </ErrorBoundary>\r\n    );\r\n  });\r\n\r\n  // Set display name for debugging\r\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\r\n\r\n  return WrappedComponent;\r\n}\r\n\r\n/**\r\n * Hook for creating error boundary configuration\r\n */\r\nexport const useErrorBoundaryConfig = (\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void,\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>,\r\n  context?: string\r\n): ErrorBoundaryConfig => {\r\n  return {\r\n    ...(onError && { onError }),\r\n    ...(fallback && { fallback }),\r\n    ...(context && { context }),\r\n    enableReporting: true\r\n  };\r\n};\r\n\r\n/**\r\n * Decorator for class components\r\n */\r\nexport const errorBoundary = (config: ErrorBoundaryConfig = {}) => {\r\n  return <P extends object>(Component: ComponentType<P>) => {\r\n    return withErrorBoundary(Component, config);\r\n  };\r\n};\r\n\r\nexport default withErrorBoundary;\r\n", "/**\r\n * LoginPage Component\r\n *\r\n * The login page for the ConnectChain admin panel.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport useAuth from '../hooks/useAuth';\r\nimport useNotification from '../hooks/useNotification';\r\nimport Button from '../components/common/Button';\r\nimport { ROUTES } from '../constants/routes';\r\nimport { mockDb } from '../mockData/db';\r\nimport { validateForm, validationRules } from '../utils/validation';\r\nimport useErrorHandler from '../hooks/useErrorHandler';\r\nimport { safeLocalStorage } from '../utils/errorHandling';\r\nimport withErrorBoundary from '../components/common/withErrorBoundary';\r\n\r\ninterface LocationState {\r\n  from?: {\r\n    pathname?: string;\r\n  };\r\n}\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    rememberMe: false\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { login } = useAuth();\r\n  const { showError, showSuccess } = useNotification();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  // Error handling\r\n  const {\r\n    handleFormError,\r\n    withFormErrorHandling,\r\n    clearError\r\n  } = useErrorHandler({\r\n    enableNotifications: true,\r\n    enableReporting: true\r\n  });\r\n\r\n  const locationState = location.state as LocationState;\r\n  const from = locationState?.from?.pathname || ROUTES.DASHBOARD;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    clearError();\r\n\r\n    // Validate form\r\n    const validationErrors = validateForm({\r\n      email: formData.email,\r\n      password: formData.password\r\n    }, {\r\n      email: [validationRules.required(), validationRules.email()],\r\n      password: [validationRules.required()]\r\n    });\r\n\r\n    if (Object.keys(validationErrors).length > 0) {\r\n      setErrors(validationErrors);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    const result = await withFormErrorHandling(async () => {\r\n      await login({\r\n        email: formData.email,\r\n        password: formData.password,\r\n        rememberMe: formData.rememberMe\r\n      });\r\n\r\n      showSuccess('Login successful');\r\n      navigate(from, { replace: true });\r\n      return true;\r\n    }, (field, message) => {\r\n      setErrors(prev => ({ ...prev, [field]: message }));\r\n    }, 'User Login');\r\n\r\n    setIsLoading(false);\r\n\r\n    if (!result) {\r\n      console.error('Login failed');\r\n    }\r\n  };\r\n\r\n  // Reset the mock database (for development troubleshooting)\r\n  const handleResetMockDb = () => {\r\n    const success = safeLocalStorage.removeItem('mock_db_users');\r\n    const success2 = safeLocalStorage.removeItem('mock_db_suppliers');\r\n    const success3 = safeLocalStorage.removeItem('mock_db_categories');\r\n    const success4 = safeLocalStorage.removeItem('mock_db_orders');\r\n\r\n    if (success && success2 && success3 && success4) {\r\n      try {\r\n        mockDb.forceReset();\r\n        showSuccess('Mock database has been reset. Please try logging in again.');\r\n        // Clear form\r\n        setFormData({\r\n          email: '',\r\n          password: '',\r\n          rememberMe: false\r\n        });\r\n        setErrors({});\r\n        clearError();\r\n      } catch (error) {\r\n        handleFormError(error, (field, message) => {\r\n          setErrors(prev => ({ ...prev, [field]: message }));\r\n        });\r\n      }\r\n    } else {\r\n      showError('Failed to reset mock database - localStorage error');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"max-w-md w-full space-y-8\">\r\n        <div>\r\n          <h1 className=\"text-center text-3xl font-bold bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent\">\r\n            ConnectChain\r\n          </h1>\r\n          <h2 className=\"mt-6 text-center text-2xl font-bold text-gray-800\">\r\n            Admin Panel\r\n          </h2>\r\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\r\n            Sign in to your account to access the admin dashboard\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 border border-gray-100\">\r\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n            <div>\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={formData['email']}\r\n                  onChange={handleChange}\r\n                  className={`appearance-none block w-full px-3 py-2 border ${errors['email'] ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm`}\r\n                  placeholder=\"<EMAIL>\"\r\n                />\r\n                {errors['email'] && (\r\n                  <p className=\"mt-1 text-sm text-red-600\">{errors['email']}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={formData['password']}\r\n                  onChange={handleChange}\r\n                  className={`appearance-none block w-full px-3 py-2 border ${errors['password'] ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm`}\r\n                  placeholder=\"••••••••\"\r\n                />\r\n                {errors['password'] && (\r\n                  <p className=\"mt-1 text-sm text-red-600\">{errors['password']}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center\">\r\n                <input\r\n                  id=\"rememberMe\"\r\n                  name=\"rememberMe\"\r\n                  type=\"checkbox\"\r\n                  checked={formData.rememberMe}\r\n                  onChange={handleChange}\r\n                  className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\r\n                />\r\n                <label htmlFor=\"rememberMe\" className=\"ml-2 block text-sm text-gray-700\">\r\n                  Remember me\r\n                </label>\r\n              </div>\r\n\r\n              <div className=\"text-sm\">\r\n                <button\r\n                  className=\"text-primary hover:text-primary-dark text-sm font-medium\"\r\n                  onClick={() => {}}\r\n                >\r\n                  Forgot Password?\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <Button\r\n                type=\"submit\"\r\n                variant=\"primary\"\r\n                fullWidth\r\n                size=\"lg\"\r\n                loading={isLoading}\r\n                disabled={isLoading}\r\n              >\r\n                Sign in\r\n              </Button>\r\n            </div>\r\n          </form>\r\n\r\n          <div className=\"mt-6\">\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <div className=\"w-full border-t border-gray-300\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-sm\">\r\n                <span className=\"px-2 bg-white text-gray-500\">Demo Credentials</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-6 grid grid-cols-1 gap-3\">\r\n              <div className=\"text-sm text-center text-gray-600\">\r\n                <p>Email: <span className=\"font-medium\"><EMAIL></span></p>\r\n                <p>Password: <span className=\"font-medium\">password123</span></p>\r\n              </div>\r\n\r\n              <div className=\"mt-4 text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={handleResetMockDb}\r\n                  className=\"text-xs text-primary hover:text-primary-dark underline\"\r\n                >\r\n                  Reset Mock Database\r\n                </button>\r\n                <p className=\"text-xs text-gray-500 mt-1\">\r\n                  If you're having trouble logging in, try resetting the mock database.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Wrap with error boundary\r\nexport default withErrorBoundary(LoginPage, {\r\n  fallback: ({ error, resetError }) => (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"max-w-md w-full space-y-8\">\r\n        <div className=\"text-center\">\r\n          <div className=\"text-red-500 text-4xl mb-4\">🔐</div>\r\n          <h2 className=\"text-xl font-semibold mb-2\">Login Page Error</h2>\r\n          <p className=\"text-gray-600 mb-4\">\r\n            {error.message || 'An error occurred while loading the login page'}\r\n          </p>\r\n          <button\r\n            onClick={resetError}\r\n            className=\"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors\"\r\n          >\r\n            Reload Page\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  ),\r\n  context: 'LoginPage'\r\n});\r\n", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n"], "names": ["options", "arguments", "length", "undefined", "enableNotifications", "enableReporting", "onError", "showNotification", "useNotification", "errorState", "setErrorState", "useState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorType", "clearError", "useCallback", "handleApiErrorWithState", "context", "apiError", "handleApiError", "notification", "type", "title", "message", "Error", "reportError", "handleValidationErrorWithState", "field", "code", "validationError", "handleValidationError", "handleFormErrorWithState", "setFieldError", "handleFormError", "handleGeneralError", "errorObj", "String", "logError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "operation", "withFormError<PERSON>andling", "isApiError", "isValidationError", "DefaultError<PERSON><PERSON><PERSON>", "_ref", "resetError", "_jsxs", "className", "children", "_jsx", "onClick", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "config", "fallback", "FallbackComponent", "WrappedComponent", "forwardRef", "props", "ref", "Error<PERSON>ou<PERSON><PERSON>", "window", "location", "reload", "handleError", "errorInfo", "displayName", "name", "componentStack", "errorBoundary", "LoginPage", "_locationState$from", "formData", "setFormData", "email", "password", "rememberMe", "errors", "setErrors", "isLoading", "setIsLoading", "login", "useAuth", "showError", "showSuccess", "navigate", "useNavigate", "useLocation", "useErrorHandler", "locationState", "state", "from", "pathname", "ROUTES", "DASHBOARD", "handleChange", "e", "value", "checked", "target", "prev", "onSubmit", "preventDefault", "validationErrors", "validateForm", "validationRules", "required", "Object", "keys", "result", "replace", "console", "htmlFor", "id", "autoComplete", "onChange", "placeholder", "<PERSON><PERSON>", "variant", "fullWidth", "size", "loading", "disabled", "handleResetMockDb", "success", "safeLocalStorage", "removeItem", "success2", "success3", "success4", "mockDb", "forceReset", "isValidEmail", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "trim", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "confirmPassword", "isStrongPassword", "values", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "ruleArray", "rule", "validator", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "icon", "iconPosition", "rounded", "href", "rel", "aria<PERSON><PERSON><PERSON>", "testId", "buttonClasses", "primary", "secondary", "outline", "danger", "text", "link", "xs", "sm", "md", "lg", "xl", "content", "_Fragment", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "memo"], "sourceRoot": ""}