"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[523],{1602:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const l=s.forwardRef(r)},2523:(e,t,a)=>{a.r(t),a.d(t,{default:()=>F});var s=a(5043),r=a(3216),l=a(2806),i=a(3593),n=a(4692),o=a(7907),c=a(3927),d=a(1602),m=a(8224),u=a(6365),h=a(579);const g=e=>{let{images:t,productName:a,className:r=""}=e;const[l,i]=(0,s.useState)(0),n=t&&t.length>0,o=n&&t.length>1;return n?(0,h.jsxs)("div",{className:`space-y-4 ${r}`,children:[(0,h.jsxs)("div",{className:"relative bg-gray-100 rounded-lg overflow-hidden aspect-square",children:[(0,h.jsx)("img",{src:t[l],alt:`${a} - Image ${l+1}`,className:"w-full h-full object-cover"}),o&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("button",{onClick:()=>{i((e=>0===e?t.length-1:e-1))},className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity","aria-label":"Previous image",children:(0,h.jsx)(m.A,{className:"h-5 w-5"})}),(0,h.jsx)("button",{onClick:()=>{i((e=>e===t.length-1?0:e+1))},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity","aria-label":"Next image",children:(0,h.jsx)(u.A,{className:"h-5 w-5"})})]}),o&&(0,h.jsxs)("div",{className:"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm",children:[l+1," / ",t.length]})]}),o&&(0,h.jsx)("div",{className:"flex space-x-2 overflow-x-auto pb-2",children:t.map(((e,t)=>(0,h.jsx)("button",{onClick:()=>(e=>{i(e)})(t),className:"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors "+(t===l?"border-blue-500":"border-gray-200 hover:border-gray-300"),children:(0,h.jsx)("img",{src:e,alt:`${a} thumbnail ${t+1}`,className:"w-full h-full object-cover"})},t)))})]}):(0,h.jsx)("div",{className:`bg-gray-200 rounded-lg flex items-center justify-center ${r}`,children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)(d.A,{className:"mx-auto h-16 w-16 text-gray-400"}),(0,h.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"No images available"})]})})};var x=a(5901);function p(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))}const v=s.forwardRef(p);function b(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}const f=s.forwardRef(b);var y=a(7591),j=a(8662),N=a(4703);const w=e=>{let{label:t,name:a,value:r=[],onChange:l,error:i,required:n=!1,disabled:o=!1,maxSize:c=5242880,allowedTypes:m=["image/jpeg","image/png","image/gif","image/webp"],maxFiles:u=10,className:g=""}=e;const[x,p]=(0,s.useState)(!1),b=(0,s.useRef)(null),w=(0,s.useCallback)((e=>{const t=[],a=r.length;for(let s=0;s<e.length&&a+t.length<u;s++){const a=e[s];if(!a)continue;const r=(0,N.nJ)(a,{maxSize:c,allowedTypes:m});r.valid?t.push(a):console.error("File validation failed:",r.error)}t.length>0&&l([...r,...t])}),[r,l,c,m,u]),k=(e,t)=>{if(t<0||t>=r.length)return;const a=[...r],[s]=a.splice(e,1);s&&(a.splice(t,0,s),l(a))},A=e=>"string"===typeof e?e:URL.createObjectURL(e);return(0,h.jsxs)("div",{className:g,children:[(0,h.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[t," ",n&&(0,h.jsx)("span",{className:"text-red-500",children:"*"}),(0,h.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",r.length,"/",u," images)"]})]}),r.length>0&&(0,h.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4",children:r.map(((e,t)=>(0,h.jsxs)("div",{className:"relative group",children:[(0,h.jsx)("img",{src:A(e),alt:`Preview ${t+1}`,className:"w-full h-24 object-cover rounded-lg border border-gray-200"}),(0,h.jsxs)("div",{className:"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-1",children:[t>0&&(0,h.jsx)("button",{type:"button",onClick:()=>k(t,t-1),className:"p-1 bg-white rounded-full hover:bg-gray-100 transition-colors",title:"Move up",children:(0,h.jsx)(v,{className:"h-3 w-3 text-gray-600"})}),t<r.length-1&&(0,h.jsx)("button",{type:"button",onClick:()=>k(t,t+1),className:"p-1 bg-white rounded-full hover:bg-gray-100 transition-colors",title:"Move down",children:(0,h.jsx)(f,{className:"h-3 w-3 text-gray-600"})}),(0,h.jsx)("button",{type:"button",onClick:()=>(e=>{const t=r.filter(((t,a)=>a!==e));l(t)})(t),className:"p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors",title:"Remove image",children:(0,h.jsx)(y.A,{className:"h-3 w-3"})})]}),0===t&&(0,h.jsx)("div",{className:"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded",children:"Primary"})]},t)))}),r.length<u&&(0,h.jsxs)("div",{className:`\n            relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n            ${x?"border-primary bg-primary bg-opacity-5":"border-gray-300"}\n            ${i?"border-red-300":""}\n            ${o?"opacity-50 cursor-not-allowed":"hover:border-primary hover:bg-gray-50"}\n          `,onDragOver:e=>{e.preventDefault(),o||p(!0)},onDragLeave:e=>{e.preventDefault(),p(!1)},onDrop:e=>{if(e.preventDefault(),p(!1),o)return;const t=e.dataTransfer.files;t&&w(t)},onClick:()=>{!o&&b.current&&r.length<u&&b.current.click()},children:[(0,h.jsx)("input",{ref:b,type:"file",name:a,accept:m.join(","),onChange:e=>{const t=e.target.files;t&&w(t),e.target.value=""},className:"hidden",disabled:o,multiple:!0}),(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{className:"flex justify-center",children:0===r.length?(0,h.jsx)(d.A,{className:"h-12 w-12 text-gray-400"}):(0,h.jsx)(j.A,{className:"h-8 w-8 text-gray-400"})}),(0,h.jsxs)("div",{className:"mt-4",children:[(0,h.jsx)("p",{className:"text-sm text-gray-600",children:x?"Drop images here":0===r.length?"Click to upload or drag and drop images":"Add more images"}),(0,h.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["PNG, JPG, GIF up to ",Math.round(c/1024/1024),"MB each"]})]})]})]}),i&&(0,h.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})};const k=function(e){let{label:t,value:a,onChange:s,fieldConfigs:r,createEmpty:l,error:i,disabled:n=!1,maxItems:c=20,className:d="",itemLabel:m}=e;const u=(e,t)=>{if(m)return m(e,t);const a=e.name||e.title||e.label;return a||`Item ${t+1}`};return(0,h.jsxs)("div",{className:d,children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,h.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[t,(0,h.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",a.length,"/",c," items)"]})]}),(0,h.jsxs)(o.A,{type:"button",variant:"outline",size:"sm",onClick:()=>{a.length<c&&s([...a,l()])},disabled:n||a.length>=c,icon:(0,h.jsx)(j.A,{className:"w-4 h-4"}),children:["Add ",t.slice(0,-1)," "]})]}),i&&(0,h.jsx)("p",{className:"mb-4 text-sm text-red-600",children:i}),(0,h.jsxs)("div",{className:"space-y-4",children:[a.map(((e,t)=>(0,h.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:u(e,t)}),(0,h.jsx)(o.A,{type:"button",variant:"outline",size:"sm",onClick:()=>(e=>{const t=a.filter(((t,a)=>a!==e));s(t)})(t),disabled:n,icon:(0,h.jsx)(y.A,{className:"w-4 h-4"}),className:"text-red-600 hover:text-red-700 border-red-300 hover:border-red-400",children:"Remove"})]}),(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.map((r=>{const l={key:r.name,label:r.label,name:`${r.name}_${t}`,type:r.type,value:e[r.name]||"",onChange:e=>{const l="number"===r.type?parseFloat(e.target.value)||0:e.target.value;((e,t,r)=>{const l=a.map(((a,s)=>s===e?{...a,[t]:r}:a));s(l)})(t,r.name,l)},required:r.required||!1,placeholder:r.placeholder||"",disabled:n,className:"textarea"===r.type?"md:col-span-2":""};return r.options&&(l.options=r.options),(0,h.jsx)(x.A,{...l})}))})]},t))),0===a.length&&(0,h.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,h.jsxs)("p",{className:"text-sm",children:["No ",t.toLowerCase()," added yet."]}),(0,h.jsxs)("p",{className:"text-xs mt-1",children:['Click "Add ',t.slice(0,-1),'" to get started.']})]})]})]})};var A=a(934);function C(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))}const S=s.forwardRef(C),$=e=>{let{formData:t,errors:a,onInputChange:s,onImagesChange:r,onAttributesChange:l,onVariantsChange:n,createEmptyAttribute:o,createEmptyVariant:c,attributeFieldConfigs:m,variantFieldConfigs:u,disabled:g=!1}=e;return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Basic Information"}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,h.jsx)(x.A,{label:"Product Name",name:"name",type:"text",value:t.name||"",onChange:s,error:a.name,required:!0,disabled:g,placeholder:"Enter product name"}),(0,h.jsx)(x.A,{label:"SKU",name:"sku",type:"text",value:t.sku||"",onChange:s,error:a.sku,required:!0,disabled:g,placeholder:"e.g., WBH-PRO-001"}),(0,h.jsx)(x.A,{label:"Category",name:"category",type:"select",value:t.category||"",onChange:s,error:a.category,required:!0,disabled:g,options:[{value:"Electronics",label:"Electronics"},{value:"Accessories",label:"Accessories"},{value:"Clothing",label:"Clothing"},{value:"Home & Garden",label:"Home & Garden"},{value:"Sports",label:"Sports"},{value:"Books",label:"Books"},{value:"Other",label:"Other"}]}),(0,h.jsx)(x.A,{label:"Status",name:"status",type:"select",value:t.status||"active",onChange:s,error:a.status,required:!0,disabled:g,options:[{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"out_of_stock",label:"Out of Stock"}]}),(0,h.jsx)(x.A,{label:"Price",name:"price",type:"number",value:t.price||0,onChange:s,error:a.price,required:!0,disabled:g,placeholder:"0.00"}),(0,h.jsx)(x.A,{label:"Stock Quantity",name:"stock",type:"number",value:t.stock||0,onChange:s,error:a.stock,required:!0,disabled:g,placeholder:"0"}),(0,h.jsx)(x.A,{label:"Minimum Stock Level",name:"minimumStock",type:"number",value:t.minimumStock||0,onChange:s,error:a.minimumStock,required:!0,disabled:g,placeholder:"0"})]}),(0,h.jsx)(x.A,{label:"Description",name:"description",type:"textarea",value:t.description||"",onChange:s,error:a.description,disabled:g,placeholder:"Enter product description..."})]})}),(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(d.A,{className:"h-5 w-5 text-gray-400"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Product Images"})]}),(0,h.jsx)(w,{label:"Product Images",name:"images",value:t.images||[],onChange:r,error:a.images,disabled:g,maxFiles:10,maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"]})]})}),(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(A.A,{className:"h-5 w-5 text-gray-400"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Product Attributes"})]}),(0,h.jsx)(k,{label:"Attributes",value:t.attributes||[],onChange:l,fieldConfigs:m,createEmpty:o,error:a.attributes||void 0,disabled:g,maxItems:20,itemLabel:e=>e.name||"New Attribute"})]})}),(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(S,{className:"h-5 w-5 text-gray-400"}),(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Product Variants"})]}),(0,h.jsx)(k,{label:"Variants",value:t.variants||[],onChange:n,fieldConfigs:u,createEmpty:c,error:a.variants||void 0,disabled:g,maxItems:50,itemLabel:e=>e.name||"New Variant"})]})})]})};var E=a(9705),L=a(8736),P=a(3893),B=a(6773),I=a(724),q=a(8267),M=a(5442),R=a(9422),U=a(2811);const F=()=>{const{id:e}=(0,r.g)(),t=(0,r.Zp)(),{showError:a,showInfo:d,showSuccess:m}=(0,E.A)(),{updateProduct:u,uploadProductImages:x}=(0,L.h)(),[p,v]=(0,s.useState)(null),[b,f]=(0,s.useState)(!0),[j,N]=(0,s.useState)(!1),[w,k]=(0,s.useState)(!1),[C,F]=(0,s.useState)({}),[O,W]=(0,s.useState)({}),H=(0,s.useRef)(a),V=(0,s.useRef)(d),z=(0,s.useRef)(m),D=(0,s.useRef)(t);(0,s.useEffect)((()=>{H.current=a,V.current=d,z.current=m,D.current=t}),[a,d,m,t]),(0,s.useEffect)((()=>{p&&F({name:p.name,sku:p.sku,category:p.category,price:p.price,stock:p.stock,minimumStock:p.minimumStock,status:p.status,description:p.description||"",images:p.images||[],attributes:p.attributes||[],variants:p.variants||[]})}),[p]);(0,s.useEffect)((()=>{if(!e)return H.current("No product ID provided"),void D.current(I.b.SUPPLIERS);(async()=>{try{f(!0),await new Promise((e=>setTimeout(e,500)));v({id:e,name:"Wireless Bluetooth Headphones Pro",sku:"WBH-PRO-001",category:"Electronics",price:199.99,stock:85,minimumStock:10,status:"active",description:"Premium wireless headphones with active noise cancellation, 30-hour battery life, and superior sound quality. Perfect for music lovers and professionals who demand the best audio experience.",image:"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",images:["https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop","https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&fit=crop","https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop","https://images.unsplash.com/photo-1572536147248-ac59a8abfa4b?w=400&h=400&fit=crop"],attributes:[{id:"1",name:"Brand",value:"AudioTech",type:"text"},{id:"2",name:"Weight",value:"250",type:"number",unit:"grams"},{id:"3",name:"Battery Life",value:"30",type:"number",unit:"hours"},{id:"4",name:"Wireless",value:"true",type:"boolean"},{id:"5",name:"Noise Cancellation",value:"Active",type:"select"},{id:"6",name:"Frequency Response",value:"20Hz - 20kHz",type:"text"}],variants:[{id:"1",name:"Black",sku:"WBH-PRO-001-BLK",price:199.99,stock:45,attributes:{color:"Black",size:"Standard"},image:"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=150&h=150&fit=crop"},{id:"2",name:"White",sku:"WBH-PRO-001-WHT",price:199.99,stock:25,attributes:{color:"White",size:"Standard"},image:"https://images.unsplash.com/photo-1484704849700-f032a568e944?w=150&h=150&fit=crop"},{id:"3",name:"Silver",sku:"WBH-PRO-001-SLV",price:219.99,stock:15,attributes:{color:"Silver",size:"Standard"},image:"https://images.unsplash.com/photo-1583394838336-acd977736f90?w=150&h=150&fit=crop"}],createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-20T14:45:00Z"})}catch(t){console.error("Error fetching product:",t),H.current("Failed to load product details")}finally{f(!1)}})()}),[e]);if(b)return(0,h.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,h.jsx)(c.A,{size:"lg"})});if(!p)return(0,h.jsxs)("div",{className:"text-center py-12",children:[(0,h.jsx)(q.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,h.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Product not found"}),(0,h.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"The product you're looking for doesn't exist or has been removed."}),(0,h.jsx)("div",{className:"mt-6",children:(0,h.jsx)(o.A,{variant:"primary",onClick:()=>D.current(I.b.SUPPLIERS),icon:(0,h.jsx)(M.A,{className:"w-4 h-4"}),children:"Back to Suppliers"})})]});const _=(T=p.stock,Z=p.minimumStock,"out_of_stock"===p.status||0===T?{text:"Out of Stock",color:"text-red-600"}:T<=Z?{text:"Low Stock",color:"text-yellow-600"}:{text:"In Stock",color:"text-green-600"});var T,Z;return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)(l.A,{title:p.name,description:"Complete product information and details",breadcrumbs:[{label:"Suppliers",path:I.b.SUPPLIERS},{label:p.name}],actions:(0,h.jsx)("div",{className:"flex space-x-3",children:j?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(o.A,{onClick:async()=>{if((()=>{const e=(0,B.l)(C,{name:[B.tU.required("Product name is required")],sku:[B.tU.required("SKU is required"),B.tU.sku()],category:[B.tU.required("Category is required")],price:[B.tU.required("Price is required"),B.tU.price()],stock:[B.tU.required("Stock is required"),B.tU.stock()],minimumStock:[B.tU.required("Minimum stock is required"),B.tU.minimumStock(),B.tU.stockConsistency()]});return W(e),0===Object.keys(e).length})()&&p&&e){k(!0);try{var t,a;const s=(null===(t=C.images)||void 0===t?void 0:t.filter((e=>e instanceof File)))||[],r=(null===(a=C.images)||void 0===a?void 0:a.filter((e=>"string"===typeof e)))||[];let l=[];if(s.length>0){l=(await x(e,s)).imageUrls}const i=[...r,...l],n={};C.name&&(n.name=C.name),C.sku&&(n.sku=C.sku),C.category&&(n.category=C.category),void 0!==C.price&&(n.price=C.price),void 0!==C.stock&&(n.stock=C.stock),void 0!==C.minimumStock&&(n.minimumStock=C.minimumStock),C.status&&(n.status=C.status),void 0!==C.description&&(n.description=C.description),i.length>0&&(n.images=i),C.attributes&&(n.attributes=C.attributes),C.variants&&(n.variants=C.variants);const o=await u(e,n);v(o),N(!1),z.current("Product updated successfully")}catch(s){console.error("Error updating product:",s),H.current("Failed to update product")}finally{k(!1)}}},icon:(0,h.jsx)(R.A,{className:"w-4 h-4"}),variant:"primary",loading:w,disabled:w,children:"Save Changes"}),(0,h.jsx)(o.A,{onClick:()=>{N(!1),W({}),p&&F({name:p.name,sku:p.sku,category:p.category,price:p.price,stock:p.stock,minimumStock:p.minimumStock,status:p.status,description:p.description||"",images:p.images||[],attributes:p.attributes||[],variants:p.variants||[]})},icon:(0,h.jsx)(y.A,{className:"w-4 h-4"}),variant:"outline",disabled:w,children:"Cancel"})]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(o.A,{onClick:()=>{N(!0)},icon:(0,h.jsx)(U.A,{className:"w-4 h-4"}),variant:"primary",children:"Edit Product"}),(0,h.jsx)(o.A,{onClick:()=>D.current(-1),icon:(0,h.jsx)(M.A,{className:"w-4 h-4"}),variant:"outline",children:"Go Back"})]})})}),j?(0,h.jsx)($,{formData:C,errors:O,onInputChange:e=>{const{name:t,value:a,type:s}=e.target,r="number"===s?parseFloat(a)||0:a;F((e=>({...e,[t]:r}))),O[t]&&W((e=>({...e,[t]:""})))},onImagesChange:e=>{F((t=>({...t,images:e})))},onAttributesChange:e=>{F((t=>({...t,attributes:e})))},onVariantsChange:e=>{F((t=>({...t,variants:e})))},createEmptyAttribute:()=>({id:`attr_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:"",value:"",type:"text"}),createEmptyVariant:()=>({id:`var_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:"",sku:"",price:0,stock:0,attributes:{}}),attributeFieldConfigs:[{name:"name",label:"Attribute Name",type:"text",required:!0,placeholder:"e.g., Brand, Weight, Color"},{name:"value",label:"Value",type:"text",required:!0,placeholder:"e.g., AudioTech, 250g, Black"},{name:"type",label:"Type",type:"select",required:!0,options:[{value:"text",label:"Text"},{value:"number",label:"Number"},{value:"boolean",label:"Boolean"},{value:"select",label:"Select"}]},{name:"unit",label:"Unit (Optional)",type:"text",placeholder:"e.g., grams, hours, cm"}],variantFieldConfigs:[{name:"name",label:"Variant Name",type:"text",required:!0,placeholder:"e.g., Black, Large, Premium"},{name:"sku",label:"SKU",type:"text",required:!0,placeholder:"e.g., WBH-PRO-001-BLK"},{name:"price",label:"Price",type:"number",required:!0,placeholder:"0.00"},{name:"stock",label:"Stock",type:"number",required:!0,placeholder:"0"}],disabled:w}):(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,h.jsx)(i.A,{children:(0,h.jsx)(g,{images:p.images||(p.image?[p.image]:[]),productName:p.name,className:"h-96"})}),(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:p.name}),(0,h.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["SKU: ",p.sku]})]}),(0,h.jsx)("div",{children:(0,h.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:(0,P.vv)(p.price)})}),p.description&&(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Description"}),(0,h.jsx)("p",{className:"text-gray-700",children:p.description})]}),(0,h.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Minimum Stock Level"}),(0,h.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:p.minimumStock})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"In Stock Now"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:p.stock}),(0,h.jsx)("p",{className:`text-sm ${_.color}`,children:_.text})]})]})]}),(0,h.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,h.jsx)(n.A,{status:(e=>{switch(e){case"active":return"active";case"inactive":default:return"pending";case"out_of_stock":return"rejected"}})(p.status),type:"supplier"}),(0,h.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:p.category})]})]})})]})}),!j&&p.attributes&&p.attributes.length>0&&(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(A.A,{className:"h-5 w-5 text-gray-400"}),(0,h.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Product Attributes"})]}),(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.attributes.map((e=>(0,h.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,h.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:e.name}),(0,h.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:"boolean"===e.type?"true"===e.value?"Yes":"No":`${e.value}${e.unit?` ${e.unit}`:""}`})]},e.id)))})]})}),!j&&p.variants&&p.variants.length>0&&(0,h.jsx)(i.A,{children:(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)(S,{className:"h-5 w-5 text-gray-400"}),(0,h.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Product Variants"})]}),(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.variants.map((e=>(0,h.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,h.jsxs)("div",{className:"flex items-start space-x-3",children:[e.image?(0,h.jsx)("img",{src:e.image,alt:e.name,className:"h-16 w-16 rounded-lg object-cover"}):(0,h.jsx)("div",{className:"h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center",children:(0,h.jsx)(q.A,{className:"h-6 w-6 text-gray-400"})}),(0,h.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,h.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,h.jsx)("p",{className:"text-xs text-gray-500 font-mono",children:e.sku}),(0,h.jsx)("p",{className:"text-sm font-semibold text-gray-900 mt-1",children:(0,P.vv)(e.price)}),(0,h.jsxs)("p",{className:"text-xs text-gray-600",children:["Stock: ",e.stock]}),(0,h.jsx)("div",{className:"mt-2 space-y-1",children:Object.entries(e.attributes).map((e=>{let[t,a]=e;return(0,h.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,h.jsxs)("span",{className:"text-gray-500 capitalize",children:[t,":"]}),(0,h.jsx)("span",{className:"text-gray-900",children:a})]},t)}))})]})]})},e.id)))})]})})]})}},2806:(e,t,a)=>{a.d(t,{A:()=>c});var s=a(5043),r=a(5475),l=a(5501),i=a(6365),n=a(579);const o=e=>{let{title:t,description:a,actions:s,breadcrumbs:o,className:c="",testId:d}=e;return(0,n.jsxs)("div",{className:`mb-6 ${c}`,"data-testid":d,children:[o&&o.length>0&&(0,n.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,n.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,n.jsx)("li",{children:(0,n.jsx)(r.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,n.jsx)(l.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,n.jsxs)("li",{className:"flex items-center",children:[(0,n.jsx)(i.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,n.jsx)(r.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,n.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),a&&"string"===typeof a?(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a}):a]}),s&&(0,n.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:s})]})]})},c=(0,s.memo)(o)},2811:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const l=s.forwardRef(r)},3593:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(5043),r=a(579);const l=e=>{let{title:t,subtitle:a,children:s,className:l="",bodyClassName:i="",headerClassName:n="",footerClassName:o="",icon:c,footer:d,onClick:m,hoverable:u=!1,noPadding:h=!1,bordered:g=!0,loading:x=!1,testId:p}=e;const v=`\n    bg-white rounded-xl ${g?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${l}\n  `,b=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${n}\n  `,f=`\n    ${h?"":"p-6"}\n    ${i}\n  `,y=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return x?(0,r.jsxs)("div",{className:v,"data-testid":p,children:[(t||a||c)&&(0,r.jsxs)("div",{className:b,children:[(0,r.jsxs)("div",{className:"w-full",children:[t&&(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),a&&(0,r.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),c&&(0,r.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,r.jsx)("div",{className:f,children:(0,r.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),d&&(0,r.jsx)("div",{className:y,children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,r.jsxs)("div",{className:v,onClick:m,"data-testid":p,children:[(t||a||c)&&(0,r.jsxs)("div",{className:b,children:[(0,r.jsxs)("div",{children:["string"===typeof t?(0,r.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof a?(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a}):a]}),c&&(0,r.jsx)("div",{className:"text-primary",children:c})]}),(0,r.jsx)("div",{className:f,children:s}),d&&(0,r.jsx)("div",{className:y,children:d})]})},i=(0,s.memo)(l)},3893:(e,t,a)=>{a.d(t,{Yq:()=>s,v7:()=>l,vv:()=>r});const s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const a=new Date(e),s={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",s).format(a)}catch(a){return console.error("Error formatting date:",a),e}},r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(a,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(s){return console.error("Error formatting currency:",s),`${t} ${e.toFixed(2)}`}},l=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,t)).toFixed(2))} ${["Bytes","KB","MB","GB","TB"][t]}`}},3927:(e,t,a)=>{a.d(t,{A:()=>r});a(5043);var s=a(579);const r=e=>{let{size:t="md",className:a="",variant:r="spinner",color:l="#F28B22",useCurrentColor:i=!1}=e;const n={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=i?"currentColor":l;return"spinner"===r?(0,s.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${n[t].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===r?(0,s.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${a}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===r?(0,s.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${n[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===r?(0,s.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${n[t].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,s.jsx)("div",{className:`${n[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},4692:(e,t,a)=>{a.d(t,{A:()=>c});a(5043);var s=a(4538),r=a(7012),l=a(7098),i=a(5889),n=a(3867),o=a(579);const c=e=>{let{status:t,type:a="user",className:c=""}=e;if(!t)return(0,o.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${c}`,children:"Unknown"});const d=t.toLowerCase();let m="",u=null;"active"===d||"verified"===d||"completed"===d?(m="bg-green-100 text-green-800",u=(0,o.jsx)(s.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(m="bg-blue-100 text-blue-800",u=(0,o.jsx)(r.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(m="bg-red-100 text-red-800",u=(0,o.jsx)(l.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(m="bg-purple-100 text-purple-800",u=(0,o.jsx)(i.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(m="bg-yellow-100 text-yellow-800",u=(0,o.jsx)(n.A,{className:"w-4 h-4 mr-1"})):m="bg-gray-100 text-gray-800";const h=t?t.charAt(0).toUpperCase()+t.slice(1):"Unknown";return(0,o.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${m} ${c}`,children:[u,h]})}},5442:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const l=s.forwardRef(r)},5889:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const l=s.forwardRef(r)},5901:(e,t,a)=>{a.d(t,{A:()=>r});a(5043);var s=a(579);const r=e=>{let{label:t,name:a,type:r="text",value:l,onChange:i,error:n,required:o=!1,placeholder:c="",options:d=[],className:m="",disabled:u=!1,loading:h=!1}=e;const g="mt-1 block w-full rounded-md shadow-sm sm:text-sm "+(n?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary focus:ring-primary");return(0,s.jsxs)("div",{className:`${m}`,children:[(0,s.jsxs)("label",{htmlFor:a,className:"block text-sm font-medium text-gray-700",children:[t," ",o&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(()=>{switch(r){case"textarea":return(0,s.jsx)("textarea",{id:a,name:a,value:l,onChange:i,className:g,placeholder:c,disabled:u});case"select":return(0,s.jsx)("select",{id:a,name:a,value:l,onChange:i,className:g,disabled:u||h,children:h?(0,s.jsx)("option",{value:"",children:"Loading..."}):d.map((e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)))});case"checkbox":return(0,s.jsx)("input",{type:"checkbox",id:a,name:a,checked:l,onChange:i,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",disabled:u});default:return(0,s.jsx)("input",{type:r,id:a,name:a,value:l,onChange:i,className:g,placeholder:c,disabled:u})}})(),n&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n})]})}},6365:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const l=s.forwardRef(r)},6773:(e,t,a)=>{a.d(t,{l:()=>h,tU:()=>g});const s=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),r=e=>/^\+?[0-9]{10,15}$/.test(e),l=e=>{try{return new URL(e),!0}catch(t){return!1}},i=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),n=e=>/^[0-9]+$/.test(e),o=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),c=e=>/^[a-zA-Z0-9]+$/.test(e),d=e=>{const t=new Date(e);return!isNaN(t.getTime())},m=(e,t)=>e===t,u=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),h=(e,t)=>{const a={};return Object.entries(t).forEach((t=>{let[s,r]=t;const l=s,i=((e,t,a,s)=>{const r=Array.isArray(a)?a:[a];for(const l of r)if(!l.validator(t,s))return l.message;return""})(0,e[l],r,e);i&&(a[l]=i)})),a},g={required:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:s,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:r,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,t)=>({validator:t=>((e,t)=>e.length>=t)(t,e),message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validator:t=>((e,t)=>e.length<=t)(t,e),message:t||`Must be no more than ${e} characters`}),numeric:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:u,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,t)=>m(e,null===t||void 0===t?void 0:t.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,t)=>m(e,null===t||void 0===t?void 0:t.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,t)=>!t||!t.stock||e<=t.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:t=>!!Array.isArray(t)&&t.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},7907:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(5043),r=a(579);const l=e=>{let{children:t,variant:a="primary",size:s="md",className:l="",onClick:i,disabled:n=!1,type:o="button",icon:c,iconPosition:d="left",fullWidth:m=!1,loading:u=!1,rounded:h=!1,href:g,target:x,rel:p,title:v,ariaLabel:b,testId:f}=e;const y=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[a]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[s]}\n    ${n?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${h?"rounded-full":"rounded-lg"}\n    ${l}\n  `,j=(0,r.jsxs)(r.Fragment,{children:[u&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!u&&(0,r.jsx)("span",{className:"mr-2",children:c}),t,c&&"right"===d&&(0,r.jsx)("span",{className:"ml-2",children:c})]});return g?(0,r.jsx)("a",{href:g,className:y,target:x,rel:p||("_blank"===x?"noopener noreferrer":void 0),onClick:i,title:v,"aria-label":b,"data-testid":f,children:j}):(0,r.jsx)("button",{type:o,className:y,onClick:i,disabled:n||u,title:v,"aria-label":b,"data-testid":f,children:j})},i=(0,s.memo)(l)},8267:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"}))}const l=s.forwardRef(r)},8662:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const l=s.forwardRef(r)},9422:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(5043);function r(e,t){let{title:a,titleId:r,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const l=s.forwardRef(r)}}]);
//# sourceMappingURL=523.814c252c.chunk.js.map