{"version": 3, "file": "static/js/523.814c252c.chunk.js", "mappings": "gJACA,SAASA,EAASC,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBN,E,gKCTlD,MAqGA,EArGkDC,IAI3C,IAJ4C,OACjDkB,EAAM,YACNC,EAAW,UACXC,EAAY,IACbpB,EACC,MAAOqB,EAAmBC,IAAwBC,EAAAA,EAAAA,UAAS,GAErDC,EAAYN,GAAUA,EAAOO,OAAS,EACtCC,EAAoBF,GAAaN,EAAOO,OAAS,EAkBvD,OAAKD,GAYHG,EAAAA,EAAAA,MAAA,OAAKP,UAAW,aAAaA,IAAYQ,SAAA,EAEvCD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,gEAA+DQ,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,OACEC,IAAKZ,EAAOG,GACZU,IAAK,GAAGZ,aAAuBE,EAAoB,IACnDD,UAAU,+BAIXM,IACCC,EAAAA,EAAAA,MAAAK,EAAAA,SAAA,CAAAJ,SAAA,EACEC,EAAAA,EAAAA,KAAA,UACEI,QAzCSC,KACnBZ,GAAsBa,GACN,IAAdA,EAAkBjB,EAAOO,OAAS,EAAIU,EAAY,GACnD,EAuCSf,UAAU,+IACV,aAAW,iBAAgBQ,UAE3BC,EAAAA,EAAAA,KAACO,EAAAA,EAAe,CAAChB,UAAU,eAE7BS,EAAAA,EAAAA,KAAA,UACEI,QA1CKI,KACff,GAAsBa,GACpBA,IAAcjB,EAAOO,OAAS,EAAI,EAAIU,EAAY,GACnD,EAwCSf,UAAU,gJACV,aAAW,aAAYQ,UAEvBC,EAAAA,EAAAA,KAACS,EAAAA,EAAgB,CAAClB,UAAU,iBAMjCM,IACCC,EAAAA,EAAAA,MAAA,OAAKP,UAAU,wFAAuFQ,SAAA,CACnGP,EAAoB,EAAE,MAAIH,EAAOO,aAMvCC,IACCG,EAAAA,EAAAA,KAAA,OAAKT,UAAU,sCAAqCQ,SACjDV,EAAOqB,KAAI,CAACC,EAAOC,KAClBZ,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IA3DFQ,KACjBnB,EAAqBmB,EAAM,EA0DFC,CAAUD,GACzBrB,UAAW,kFACTqB,IAAUpB,EACN,kBACA,yCACHO,UAEHC,EAAAA,EAAAA,KAAA,OACEC,IAAKU,EACLT,IAAK,GAAGZ,eAAyBsB,EAAQ,IACzCrB,UAAU,gCAXPqB,WApDbZ,EAAAA,EAAAA,KAAA,OAAKT,UAAW,2DAA2DA,IAAYQ,UACrFD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaQ,SAAA,EAC1BC,EAAAA,EAAAA,KAAC9B,EAAAA,EAAS,CAACqB,UAAU,qCACrBS,EAAAA,EAAAA,KAAA,KAAGT,UAAU,6BAA4BQ,SAAC,4BAkE1C,E,cC/GV,SAASe,EAAW3C,EAIjBC,GAAQ,IAJU,MACnBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,uCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBsC,GCvBlD,SAASC,EAAa5C,EAInBC,GAAQ,IAJY,MACrBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,yCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBuC,G,kCCClD,MAkOA,EAlOgE5C,IAYzD,IAZ0D,MAC/D6C,EAAK,KACLC,EAAI,MACJC,EAAQ,GAAE,SACVC,EAAQ,MACRC,EAAK,SACLC,GAAW,EAAK,SAChBC,GAAW,EAAK,QAChBC,EAAU,QAAe,aACzBC,EAAe,CAAC,aAAc,YAAa,YAAa,cAAa,SACrEC,EAAW,GAAE,UACblC,EAAY,IACbpB,EACC,MAAOuD,EAAYC,IAAiBjC,EAAAA,EAAAA,WAAS,GACvCkC,GAAeC,EAAAA,EAAAA,QAAyB,MAExCC,GAAmBC,EAAAA,EAAAA,cAAaC,IACpC,MAAMC,EAAqB,GACrBC,EAAehB,EAAMtB,OAE3B,IAAK,IAAIuC,EAAI,EAAGA,EAAIH,EAAMpC,QAAWsC,EAAeD,EAAWrC,OAAU6B,EAAUU,IAAK,CACtF,MAAMC,EAAOJ,EAAMG,GACnB,IAAKC,EAAM,SAEX,MAAMC,GAAaC,EAAAA,EAAAA,IAAaF,EAAM,CACpCb,UACAC,iBAGEa,EAAWE,MACbN,EAAWO,KAAKJ,GAEhBK,QAAQrB,MAAM,0BAA2BiB,EAAWjB,MAExD,CAEIa,EAAWrC,OAAS,GACtBuB,EAAS,IAAID,KAAUe,GACzB,GACC,CAACf,EAAOC,EAAUI,EAASC,EAAcC,IAwCtCiB,EAAgBA,CAACC,EAAmBC,KACxC,GAAIA,EAAU,GAAKA,GAAW1B,EAAMtB,OAAQ,OAE5C,MAAMiD,EAAW,IAAI3B,IACd4B,GAAaD,EAASE,OAAOJ,EAAW,GAC3CG,IACFD,EAASE,OAAOH,EAAS,EAAGE,GAC5B3B,EAAS0B,GACX,EASIG,EAAiBZ,GACD,kBAATA,EACFA,EAEFa,IAAIC,gBAAgBd,GAG7B,OACEtC,EAAAA,EAAAA,MAAA,OAAKP,UAAWA,EAAUQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,SAAOP,UAAU,+CAA8CQ,SAAA,CAC5DiB,EAAM,IAAEK,IAAYrB,EAAAA,EAAAA,KAAA,QAAMT,UAAU,eAAcQ,SAAC,OACpDD,EAAAA,EAAAA,MAAA,QAAMP,UAAU,6BAA4BQ,SAAA,CAAC,IACzCmB,EAAMtB,OAAO,IAAE6B,EAAS,iBAK7BP,EAAMtB,OAAS,IACdI,EAAAA,EAAAA,KAAA,OAAKT,UAAU,4DAA2DQ,SACvEmB,EAAMR,KAAI,CAAC0B,EAAMxB,KAChBd,EAAAA,EAAAA,MAAA,OAAiBP,UAAU,iBAAgBQ,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OACEC,IAAK+C,EAAcZ,GACnBlC,IAAK,WAAWU,EAAQ,IACxBrB,UAAU,gEAIZO,EAAAA,EAAAA,MAAA,OAAKP,UAAU,qJAAoJQ,SAAA,CAEhKa,EAAQ,IACPZ,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACL/C,QAASA,IAAMsC,EAAc9B,EAAOA,EAAQ,GAC5CrB,UAAU,gEACVlB,MAAM,UAAS0B,UAEfC,EAAAA,EAAAA,KAACc,EAAW,CAACvB,UAAU,4BAK1BqB,EAAQM,EAAMtB,OAAS,IACtBI,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACL/C,QAASA,IAAMsC,EAAc9B,EAAOA,EAAQ,GAC5CrB,UAAU,gEACVlB,MAAM,YAAW0B,UAEjBC,EAAAA,EAAAA,KAACe,EAAa,CAACxB,UAAU,6BAK7BS,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACL/C,QAASA,IA9EHQ,KACpB,MAAMiC,EAAW3B,EAAMkC,QAAO,CAACC,EAAGlB,IAAMA,IAAMvB,IAC9CO,EAAS0B,EAAS,EA4EWS,CAAa1C,GAC5BrB,UAAU,4EACVlB,MAAM,eAAc0B,UAEpBC,EAAAA,EAAAA,KAACuD,EAAAA,EAAS,CAAChE,UAAU,iBAKd,IAAVqB,IACCZ,EAAAA,EAAAA,KAAA,OAAKT,UAAU,2EAA0EQ,SAAC,cA9CpFa,OAwDfM,EAAMtB,OAAS6B,IACd3B,EAAAA,EAAAA,MAAA,OACEP,UAAW,0HAEPmC,EAAa,yCAA2C,kCACxDN,EAAQ,iBAAmB,mBAC3BE,EAAW,gCAAkC,sDAEjDkC,WAlIgBC,IACtBA,EAAEC,iBACGpC,GACHK,GAAc,EAChB,EA+HMgC,YA5HiBF,IACvBA,EAAEC,iBACF/B,GAAc,EAAM,EA2HdiC,OAxHYH,IAIlB,GAHAA,EAAEC,iBACF/B,GAAc,GAEVL,EAAU,OAEd,MAAMU,EAAQyB,EAAEI,aAAa7B,MACzBA,GACFF,EAAiBE,EACnB,EAgHM5B,QA7FY0D,MACbxC,GAAYM,EAAamC,SAAW7C,EAAMtB,OAAS6B,GACtDG,EAAamC,QAAQC,OACvB,EA0F2BjE,SAAA,EAErBC,EAAAA,EAAAA,KAAA,SACEhB,IAAK4C,EACLuB,KAAK,OACLlC,KAAMA,EACNgD,OAAQzC,EAAa0C,KAAK,KAC1B/C,SArJqBsC,IAC7B,MAAMzB,EAAQyB,EAAEU,OAAOnC,MACnBA,GACFF,EAAiBE,GAGnByB,EAAEU,OAAOjD,MAAQ,EAAE,EAgJX3B,UAAU,SACV+B,SAAUA,EACV8C,UAAQ,KAGVtE,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,sBAAqBQ,SAChB,IAAjBmB,EAAMtB,QACLI,EAAAA,EAAAA,KAAC9B,EAAAA,EAAS,CAACqB,UAAU,6BAErBS,EAAAA,EAAAA,KAACqE,EAAAA,EAAQ,CAAC9E,UAAU,6BAGxBO,EAAAA,EAAAA,MAAA,OAAKP,UAAU,OAAMQ,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGT,UAAU,wBAAuBQ,SACjC2B,EACG,mBACiB,IAAjBR,EAAMtB,OACJ,0CACA,qBAGRE,EAAAA,EAAAA,MAAA,KAAGP,UAAU,6BAA4BQ,SAAA,CAAC,uBACnBuE,KAAKC,MAAMhD,EAAU,KAAO,MAAM,sBAOhEH,IAASpB,EAAAA,EAAAA,KAAA,KAAGT,UAAU,4BAA2BQ,SAAEqB,MAChD,ECnFV,QAlIA,SAA0BjD,GAWK,IAX2B,MACxD6C,EAAK,MACLE,EAAK,SACLC,EAAQ,aACRqD,EAAY,YACZC,EAAW,MACXrD,EAAK,SACLE,GAAW,EAAK,SAChBoD,EAAW,GAAE,UACbnF,EAAY,GAAE,UACdoF,GAC0BxG,EAC1B,MAkBMyG,EAAsBA,CAACC,EAASjE,KACpC,GAAI+D,EACF,OAAOA,EAAUE,EAAMjE,GAIzB,MAAMkE,EAAYD,EAAK5D,MAAQ4D,EAAKxG,OAASwG,EAAK7D,MAClD,OAAI8D,GAIG,QAAQlE,EAAQ,GAAG,EAG5B,OACEd,EAAAA,EAAAA,MAAA,OAAKP,UAAWA,EAAUQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yCAAwCQ,SAAA,EACrDD,EAAAA,EAAAA,MAAA,SAAOP,UAAU,0CAAyCQ,SAAA,CACvDiB,GACDlB,EAAAA,EAAAA,MAAA,QAAMP,UAAU,6BAA4BQ,SAAA,CAAC,IACzCmB,EAAMtB,OAAO,IAAE8E,EAAS,iBAI9B5E,EAAAA,EAAAA,MAACiF,EAAAA,EAAM,CACL5B,KAAK,SACL6B,QAAQ,UACRC,KAAK,KACL7E,QA9CU8E,KACZhE,EAAMtB,OAAS8E,GACjBvD,EAAS,IAAID,EAAOuD,KACtB,EA4CMnD,SAAUA,GAAYJ,EAAMtB,QAAU8E,EACtCS,MAAMnF,EAAAA,EAAAA,KAACqE,EAAAA,EAAQ,CAAC9E,UAAU,YAAaQ,SAAA,CACxC,OACMiB,EAAMoE,MAAM,GAAI,GAAG,UAI3BhE,IAASpB,EAAAA,EAAAA,KAAA,KAAGT,UAAU,4BAA2BQ,SAAEqB,KAEpDtB,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,CACvBmB,EAAMR,KAAI,CAACmE,EAAMjE,KAChBd,EAAAA,EAAAA,MAAA,OAAiBP,UAAU,mDAAkDQ,SAAA,EAC3ED,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yCAAwCQ,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAC9C6E,EAAoBC,EAAMjE,MAE7BZ,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACL5B,KAAK,SACL6B,QAAQ,UACRC,KAAK,KACL7E,QAASA,IA7DDQ,KACpB,MAAMyE,EAAWnE,EAAMkC,QAAO,CAACC,EAAGlB,IAAMA,IAAMvB,IAC9CO,EAASkE,EAAS,EA2DS/B,CAAa1C,GAC5BU,SAAUA,EACV6D,MAAMnF,EAAAA,EAAAA,KAACuD,EAAAA,EAAS,CAAChE,UAAU,YAC3BA,UAAU,sEAAqEQ,SAChF,eAKHC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,wCAAuCQ,SACnDyE,EAAa9D,KAAK4E,IACjB,MAAMC,EAAkB,CACtBC,IAAKF,EAAOrE,KACZD,MAAOsE,EAAOtE,MACdC,KAAM,GAAGqE,EAAOrE,QAAQL,IACxBuC,KAAMmC,EAAOnC,KACbjC,MAAO2D,EAAKS,EAAOrE,OAAS,GAC5BE,SAAWsC,IACT,MAAMgC,EAA6B,WAAhBH,EAAOnC,KACtBuC,WAAWjC,EAAEU,OAAOjD,QAAU,EAC9BuC,EAAEU,OAAOjD,MA5ENyE,EAAC/E,EAAegF,EAAeH,KACtD,MAAMJ,EAAWnE,EAAMR,KAAI,CAACmE,EAAM1C,IAChCA,IAAMvB,EAAQ,IAAKiE,EAAM,CAACe,GAAQH,GAAeZ,IAEnD1D,EAASkE,EAAS,EAyEFM,CAAiB/E,EAAO0E,EAAOrE,KAAMwE,EAAW,EAElDpE,SAAUiE,EAAOjE,WAAY,EAC7BwE,YAAaP,EAAOO,aAAe,GACnCvE,SAAUA,EACV/B,UAA2B,aAAhB+F,EAAOnC,KAAsB,gBAAkB,IAQ5D,OAJImC,EAAOQ,UACTP,EAAWO,QAAUR,EAAOQ,UAGvB9F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,IAAKR,GAAc,QA3ChC3E,KAiDM,IAAjBM,EAAMtB,SACLE,EAAAA,EAAAA,MAAA,OAAKP,UAAU,iCAAgCQ,SAAA,EAC7CD,EAAAA,EAAAA,MAAA,KAAGP,UAAU,UAASQ,SAAA,CAAC,MAAIiB,EAAMgF,cAAc,kBAC/ClG,EAAAA,EAAAA,MAAA,KAAGP,UAAU,eAAcQ,SAAA,CAAC,cAAYiB,EAAMoE,MAAM,GAAI,GAAG,+BAMvE,E,aCjKA,SAASa,EAAyB9H,EAI/BC,GAAQ,IAJwB,MACjCC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,ylBAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByH,GC+NlD,EA5MwD9H,IAYjD,IAZkD,SACvD+H,EAAQ,OACRC,EAAM,cACNC,EAAa,eACbC,EAAc,mBACdC,EAAkB,iBAClBC,EAAgB,qBAChBC,EAAoB,mBACpBC,EAAkB,sBAClBC,EAAqB,oBACrBC,EAAmB,SACnBrF,GAAW,GACZnD,EAiBC,OACE2B,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EAExBC,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,yCAAwCQ,SAAC,uBAEvDD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,wCAAuCQ,SAAA,EACpDC,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,eACNC,KAAK,OACLkC,KAAK,OACLjC,MAAOgF,EAASjF,MAAQ,GACxBE,SAAUiF,EACVhF,MAAO+E,EAAOlF,KACdI,UAAQ,EACRC,SAAUA,EACVuE,YAAY,wBAGd7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,MACNC,KAAK,MACLkC,KAAK,OACLjC,MAAOgF,EAASW,KAAO,GACvB1F,SAAUiF,EACVhF,MAAO+E,EAAOU,IACdxF,UAAQ,EACRC,SAAUA,EACVuE,YAAY,uBAGd7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,WACNC,KAAK,WACLkC,KAAK,SACLjC,MAAOgF,EAASY,UAAY,GAC5B3F,SAAUiF,EACVhF,MAAO+E,EAAOW,SACdzF,UAAQ,EACRC,SAAUA,EACVwE,QAzDY,CACtB,CAAE5E,MAAO,cAAeF,MAAO,eAC/B,CAAEE,MAAO,cAAeF,MAAO,eAC/B,CAAEE,MAAO,WAAYF,MAAO,YAC5B,CAAEE,MAAO,gBAAiBF,MAAO,iBACjC,CAAEE,MAAO,SAAUF,MAAO,UAC1B,CAAEE,MAAO,QAASF,MAAO,SACzB,CAAEE,MAAO,QAASF,MAAO,aAqDjBhB,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,SACNC,KAAK,SACLkC,KAAK,SACLjC,MAAOgF,EAASa,QAAU,SAC1B5F,SAAUiF,EACVhF,MAAO+E,EAAOY,OACd1F,UAAQ,EACRC,SAAUA,EACVwE,QA3DU,CACpB,CAAE5E,MAAO,SAAUF,MAAO,UAC1B,CAAEE,MAAO,WAAYF,MAAO,YAC5B,CAAEE,MAAO,eAAgBF,MAAO,oBA2DxBhB,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,QACNC,KAAK,QACLkC,KAAK,SACLjC,MAAOgF,EAASc,OAAS,EACzB7F,SAAUiF,EACVhF,MAAO+E,EAAOa,MACd3F,UAAQ,EACRC,SAAUA,EACVuE,YAAY,UAGd7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,iBACNC,KAAK,QACLkC,KAAK,SACLjC,MAAOgF,EAASe,OAAS,EACzB9F,SAAUiF,EACVhF,MAAO+E,EAAOc,MACd5F,UAAQ,EACRC,SAAUA,EACVuE,YAAY,OAGd7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,sBACNC,KAAK,eACLkC,KAAK,SACLjC,MAAOgF,EAASgB,cAAgB,EAChC/F,SAAUiF,EACVhF,MAAO+E,EAAOe,aACd7F,UAAQ,EACRC,SAAUA,EACVuE,YAAY,UAIhB7F,EAAAA,EAAAA,KAAC+F,EAAAA,EAAS,CACR/E,MAAM,cACNC,KAAK,cACLkC,KAAK,WACLjC,MAAOgF,EAASiB,aAAe,GAC/BhG,SAAUiF,EACVhF,MAAO+E,EAAOgB,YACd7F,SAAUA,EACVuE,YAAY,uCAMlB7F,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAAC9B,EAAAA,EAAS,CAACqB,UAAU,2BACrBS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,uBAGpDC,EAAAA,EAAAA,KAACoH,EAAmB,CAClBpG,MAAM,iBACNC,KAAK,SACLC,MAAOgF,EAAS7G,QAAU,GAC1B8B,SAAUkF,EACVjF,MAAO+E,EAAO9G,OACdiC,SAAUA,EACVG,SAAU,GACVF,QAAS,QACTC,aAAc,CAAC,aAAc,YAAa,YAAa,sBAM7DxB,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAACqH,EAAAA,EAAO,CAAC9H,UAAU,2BACnBS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,2BAGpDC,EAAAA,EAAAA,KAACsH,EAAiB,CAChBtG,MAAM,aACNE,MAAOgF,EAASqB,YAAc,GAC9BpG,SAAUmF,EACV9B,aAAckC,EACdjC,YAAa+B,EACbpF,MAAO+E,EAAOoB,iBAAcC,EAC5BlG,SAAUA,EACVoD,SAAU,GACVC,UAAYE,GAA2BA,EAAK5D,MAAQ,wBAM1DjB,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAACiG,EAAyB,CAAC1G,UAAU,2BACrCS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,yBAGpDC,EAAAA,EAAAA,KAACsH,EAAiB,CAChBtG,MAAM,WACNE,MAAOgF,EAASuB,UAAY,GAC5BtG,SAAUoF,EACV/B,aAAcmC,EACdlC,YAAagC,EACbrF,MAAO+E,EAAOsB,eAAYD,EAC1BlG,SAAUA,EACVoD,SAAU,GACVC,UAAYE,GAAyBA,EAAK5D,MAAQ,uBAIpD,E,6FC5MV,MAyjBA,EAzjBqCyG,KACnC,MAAM,GAAEzI,IAAO0I,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OACX,UAAEC,EAAS,SAAEC,EAAQ,YAAEC,IAAgBC,EAAAA,EAAAA,MACvC,cAAEC,EAAa,oBAAEC,IAAwBC,EAAAA,EAAAA,MAGxCC,EAASC,IAAc5I,EAAAA,EAAAA,UAAiC,OACxD6I,EAAWC,IAAgB9I,EAAAA,EAAAA,WAAS,IAGpC+I,EAAWC,IAAgBhJ,EAAAA,EAAAA,WAAS,IACpCiJ,EAAUC,IAAelJ,EAAAA,EAAAA,WAAS,IAClCwG,EAAU2C,IAAenJ,EAAAA,EAAAA,UAAmC,CAAC,IAC7DyG,EAAQ2C,IAAapJ,EAAAA,EAAAA,UAAiC,CAAC,GAGxDqJ,GAAelH,EAAAA,EAAAA,QAAOiG,GACtBkB,GAAcnH,EAAAA,EAAAA,QAAOkG,GACrBkB,GAAiBpH,EAAAA,EAAAA,QAAOmG,GACxBkB,GAAcrH,EAAAA,EAAAA,QAAO+F,IAG3BuB,EAAAA,EAAAA,YAAU,KACRJ,EAAahF,QAAU+D,EACvBkB,EAAYjF,QAAUgE,EACtBkB,EAAelF,QAAUiE,EACzBkB,EAAYnF,QAAU6D,CAAQ,GAC7B,CAACE,EAAWC,EAAUC,EAAaJ,KAGtCuB,EAAAA,EAAAA,YAAU,KACJd,GACFQ,EAAY,CACV5H,KAAMoH,EAAQpH,KACd4F,IAAKwB,EAAQxB,IACbC,SAAUuB,EAAQvB,SAClBE,MAAOqB,EAAQrB,MACfC,MAAOoB,EAAQpB,MACfC,aAAcmB,EAAQnB,aACtBH,OAAQsB,EAAQtB,OAChBI,YAAakB,EAAQlB,aAAe,GACpC9H,OAAQgJ,EAAQhJ,QAAU,GAC1BkI,WAAYc,EAAQd,YAAc,GAClCE,SAAUY,EAAQZ,UAAY,IAElC,GACC,CAACY,KAsIJc,EAAAA,EAAAA,YAAU,KACR,IAAKlK,EAGH,OAFA8J,EAAahF,QAAQ,+BACrBmF,EAAYnF,QAAQqF,EAAAA,EAAOC,WAKRC,WACnB,IACEd,GAAa,SAGP,IAAIe,SAAQC,GAAWC,WAAWD,EAAS,OA6DjDlB,EA1DqC,CACnCrJ,GAAIA,EACJgC,KAAM,oCACN4F,IAAK,cACLC,SAAU,cACVE,MAAO,OACPC,MAAO,GACPC,aAAc,GACdH,OAAQ,SACRI,YAAa,iMACbxG,MAAO,oFACPtB,OAAQ,CACN,oFACA,oFACA,oFACA,qFAEFkI,WAAY,CACV,CAAEtI,GAAI,IAAKgC,KAAM,QAASC,MAAO,YAAaiC,KAAM,QACpD,CAAElE,GAAI,IAAKgC,KAAM,SAAUC,MAAO,MAAOiC,KAAM,SAAUuG,KAAM,SAC/D,CAAEzK,GAAI,IAAKgC,KAAM,eAAgBC,MAAO,KAAMiC,KAAM,SAAUuG,KAAM,SACpE,CAAEzK,GAAI,IAAKgC,KAAM,WAAYC,MAAO,OAAQiC,KAAM,WAClD,CAAElE,GAAI,IAAKgC,KAAM,qBAAsBC,MAAO,SAAUiC,KAAM,UAC9D,CAAElE,GAAI,IAAKgC,KAAM,qBAAsBC,MAAO,eAAgBiC,KAAM,SAEtEsE,SAAU,CACR,CACExI,GAAI,IACJgC,KAAM,QACN4F,IAAK,kBACLG,MAAO,OACPC,MAAO,GACPM,WAAY,CAAEoC,MAAO,QAAS1E,KAAM,YACpCtE,MAAO,qFAET,CACE1B,GAAI,IACJgC,KAAM,QACN4F,IAAK,kBACLG,MAAO,OACPC,MAAO,GACPM,WAAY,CAAEoC,MAAO,QAAS1E,KAAM,YACpCtE,MAAO,qFAET,CACE1B,GAAI,IACJgC,KAAM,SACN4F,IAAK,kBACLG,MAAO,OACPC,MAAO,GACPM,WAAY,CAAEoC,MAAO,SAAU1E,KAAM,YACrCtE,MAAO,sFAGXiJ,UAAW,uBACXC,UAAW,wBAIf,CAAE,MAAOzI,GACPqB,QAAQrB,MAAM,0BAA2BA,GACzC2H,EAAahF,QAAQ,iCACvB,CAAC,QACCyE,GAAa,EACf,GAGFsB,EAAc,GACb,CAAC7K,IAiEJ,GAAIsJ,EACF,OACEvI,EAAAA,EAAAA,KAAA,OAAKT,UAAU,gDAA+CQ,UAC5DC,EAAAA,EAAAA,KAAC+J,EAAAA,EAAc,CAAC9E,KAAK,SAK3B,IAAKoD,EACH,OACEvI,EAAAA,EAAAA,MAAA,OAAKP,UAAU,oBAAmBQ,SAAA,EAChCC,EAAAA,EAAAA,KAACgK,EAAAA,EAAQ,CAACzK,UAAU,qCACpBS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,yCAAwCQ,SAAC,uBACvDC,EAAAA,EAAAA,KAAA,KAAGT,UAAU,6BAA4BQ,SAAC,uEAG1CC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,OAAMQ,UACnBC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACLC,QAAQ,UACR5E,QAASA,IAAM8I,EAAYnF,QAAQqF,EAAAA,EAAOC,WAC1ClE,MAAMnF,EAAAA,EAAAA,KAACiK,EAAAA,EAAa,CAAC1K,UAAU,YAAaQ,SAC7C,2BAQT,MAAMmK,GAzCkBjD,EAyCWoB,EAAQpB,MAzCJC,EAyCWmB,EAAQnB,aAxCzC,iBAwCuDmB,EAAQtB,QAxCnC,IAAVE,EACxB,CAAEkD,KAAM,eAAgBR,MAAO,gBAC7B1C,GAASC,EACX,CAAEiD,KAAM,YAAaR,MAAO,mBAE5B,CAAEQ,KAAM,WAAYR,MAAO,mBANfS,IAACnD,EAAeC,EA2CvC,OACEpH,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBC,EAAAA,EAAAA,KAACqK,EAAAA,EAAU,CACThM,MAAOgK,EAAQpH,KACfkG,YAAY,2CACZmD,YAAa,CACX,CAAEtJ,MAAO,YAAauJ,KAAMnB,EAAAA,EAAOC,WACnC,CAAErI,MAAOqH,EAAQpH,OAEnBuJ,SACExK,EAAAA,EAAAA,KAAA,OAAKT,UAAU,iBAAgBQ,SAC5B0I,GACC3I,EAAAA,EAAAA,MAAAK,EAAAA,SAAA,CAAAJ,SAAA,EACEC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACL3E,QAnPGkJ,UACjB,GA5CuBmB,MACvB,MAAMC,GAAmBC,EAAAA,EAAAA,GAAazE,EAAU,CAC9CjF,KAAM,CAAC2J,EAAAA,GAAgBvJ,SAAS,6BAChCwF,IAAK,CAAC+D,EAAAA,GAAgBvJ,SAAS,mBAAoBuJ,EAAAA,GAAgB/D,OACnEC,SAAU,CAAC8D,EAAAA,GAAgBvJ,SAAS,yBACpC2F,MAAO,CAAC4D,EAAAA,GAAgBvJ,SAAS,qBAAsBuJ,EAAAA,GAAgB5D,SACvEC,MAAO,CAAC2D,EAAAA,GAAgBvJ,SAAS,qBAAsBuJ,EAAAA,GAAgB3D,SACvEC,aAAc,CACZ0D,EAAAA,GAAgBvJ,SAAS,6BACzBuJ,EAAAA,GAAgB1D,eAChB0D,EAAAA,GAAgBC,sBAKpB,OADA/B,EAAU4B,GACsC,IAAzCjM,OAAOqM,KAAKJ,GAAkB9K,MAAY,EA6B5C6K,IAAuBpC,GAAYpJ,EAAxC,CAIA2J,GAAY,GACZ,IAAK,IAADmC,EAAAC,EAEF,MAAMC,GAA4B,QAAfF,EAAA7E,EAAS7G,cAAM,IAAA0L,OAAA,EAAfA,EAAiB3H,QAAQ8H,GAAqBA,aAAeC,SAAS,GACnFC,GAA2B,QAAfJ,EAAA9E,EAAS7G,cAAM,IAAA2L,OAAA,EAAfA,EAAiB5H,QAAQ8H,GAAsC,kBAARA,MAAqB,GAG9F,IAAIG,EAA8B,GAClC,GAAIJ,EAAWrL,OAAS,EAAG,CAEzByL,SAD2BlD,EAAoBlJ,EAAIgM,IAClBG,SACnC,CAGA,MAAME,EAAe,IAAIF,KAAcC,GAGjCE,EAAuC,CAAC,EAC1CrF,EAASjF,OAAMsK,EAAWtK,KAAOiF,EAASjF,MAC1CiF,EAASW,MAAK0E,EAAW1E,IAAMX,EAASW,KACxCX,EAASY,WAAUyE,EAAWzE,SAAWZ,EAASY,eAC/BU,IAAnBtB,EAASc,QAAqBuE,EAAWvE,MAAQd,EAASc,YACvCQ,IAAnBtB,EAASe,QAAqBsE,EAAWtE,MAAQf,EAASe,YAChCO,IAA1BtB,EAASgB,eAA4BqE,EAAWrE,aAAehB,EAASgB,cACxEhB,EAASa,SAAQwE,EAAWxE,OAASb,EAASa,aACrBS,IAAzBtB,EAASiB,cAA2BoE,EAAWpE,YAAcjB,EAASiB,aACtEmE,EAAa1L,OAAS,IAAG2L,EAAWlM,OAASiM,GAC7CpF,EAASqB,aAAYgE,EAAWhE,WAAarB,EAASqB,YACtDrB,EAASuB,WAAU8D,EAAW9D,SAAWvB,EAASuB,UAGtD,MAAM+D,QAAuBtD,EAAcjJ,EAAIsM,GAC/CjD,EAAWkD,GACX9C,GAAa,GACbO,EAAelF,QAAQ,+BAEzB,CAAE,MAAO3C,GACPqB,QAAQrB,MAAM,0BAA2BA,GACzC2H,EAAahF,QAAQ,2BACvB,CAAC,QACC6E,GAAY,EACd,CA3CA,CA2CA,EAsMczD,MAAMnF,EAAAA,EAAAA,KAACyL,EAAAA,EAAS,CAAClM,UAAU,YAC3ByF,QAAQ,UACR0G,QAAS/C,EACTrH,SAAUqH,EAAS5I,SACpB,kBAGDC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACL3E,QAjRKuL,KACnBjD,GAAa,GACbI,EAAU,CAAC,GAEPT,GACFQ,EAAY,CACV5H,KAAMoH,EAAQpH,KACd4F,IAAKwB,EAAQxB,IACbC,SAAUuB,EAAQvB,SAClBE,MAAOqB,EAAQrB,MACfC,MAAOoB,EAAQpB,MACfC,aAAcmB,EAAQnB,aACtBH,OAAQsB,EAAQtB,OAChBI,YAAakB,EAAQlB,aAAe,GACpC9H,OAAQgJ,EAAQhJ,QAAU,GAC1BkI,WAAYc,EAAQd,YAAc,GAClCE,SAAUY,EAAQZ,UAAY,IAElC,EAgQctC,MAAMnF,EAAAA,EAAAA,KAACuD,EAAAA,EAAS,CAAChE,UAAU,YAC3ByF,QAAQ,UACR1D,SAAUqH,EAAS5I,SACpB,eAKHD,EAAAA,EAAAA,MAAAK,EAAAA,SAAA,CAAAJ,SAAA,EACEC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACL3E,QAhSGwL,KACjBlD,GAAa,EAAK,EAgSJvD,MAAMnF,EAAAA,EAAAA,KAAC6L,EAAAA,EAAU,CAACtM,UAAU,YAC5ByF,QAAQ,UAASjF,SAClB,kBAGDC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAM,CACL3E,QAASA,IAAM8I,EAAYnF,SAAS,GACpCoB,MAAMnF,EAAAA,EAAAA,KAACiK,EAAAA,EAAa,CAAC1K,UAAU,YAC/ByF,QAAQ,UAASjF,SAClB,mBASV0I,GAECzI,EAAAA,EAAAA,KAAC8L,EAAe,CACd5F,SAAUA,EACVC,OAAQA,EACRC,cAjXmB3C,IACzB,MAAM,KAAExC,EAAI,MAAEC,EAAK,KAAEiC,GAASM,EAAEU,OAC1B4H,EAA0B,WAAT5I,EAAoBuC,WAAWxE,IAAU,EAAIA,EAEpE2H,GAAYmD,IAAI,IACXA,EACH,CAAC/K,GAAO8K,MAIN5F,EAAOlF,IACT6H,GAAUkD,IAAI,IACTA,EACH,CAAC/K,GAAO,MAEZ,EAmWMoF,eAhWoBhH,IAC1BwJ,GAAYmD,IAAI,IACXA,EACH3M,YACC,EA6VGiH,mBA1VwBiB,IAC9BsB,GAAYmD,IAAI,IACXA,EACHzE,gBACC,EAuVGhB,iBApVsBkB,IAC5BoB,GAAYmD,IAAI,IACXA,EACHvE,cACC,EAiVGjB,qBA3JqBA,KAAA,CAC3BvH,GAAI,QAAQgN,KAAKC,SAAS5H,KAAK6H,SAASC,SAAS,IAAIC,UAAU,EAAG,KAClEpL,KAAM,GACNC,MAAO,GACPiC,KAAM,SAwJAsD,mBArJmBA,KAAA,CACzBxH,GAAI,OAAOgN,KAAKC,SAAS5H,KAAK6H,SAASC,SAAS,IAAIC,UAAU,EAAG,KACjEpL,KAAM,GACN4F,IAAK,GACLG,MAAO,EACPC,MAAO,EACPM,WAAY,CAAC,IAgJPb,sBA5IsB,CAC5B,CAAEzF,KAAM,OAAQD,MAAO,iBAAkBmC,KAAM,OAAiB9B,UAAU,EAAMwE,YAAa,8BAC7F,CAAE5E,KAAM,QAASD,MAAO,QAASmC,KAAM,OAAiB9B,UAAU,EAAMwE,YAAa,gCACrF,CAAE5E,KAAM,OAAQD,MAAO,OAAQmC,KAAM,SAAmB9B,UAAU,EAAMyE,QAAS,CAC/E,CAAE5E,MAAO,OAAQF,MAAO,QACxB,CAAEE,MAAO,SAAUF,MAAO,UAC1B,CAAEE,MAAO,UAAWF,MAAO,WAC3B,CAAEE,MAAO,SAAUF,MAAO,YAE5B,CAAEC,KAAM,OAAQD,MAAO,kBAAmBmC,KAAM,OAAiB0C,YAAa,2BAoIxEc,oBAhIoB,CAC1B,CAAE1F,KAAM,OAAQD,MAAO,eAAgBmC,KAAM,OAAiB9B,UAAU,EAAMwE,YAAa,+BAC3F,CAAE5E,KAAM,MAAOD,MAAO,MAAOmC,KAAM,OAAiB9B,UAAU,EAAMwE,YAAa,yBACjF,CAAE5E,KAAM,QAASD,MAAO,QAASmC,KAAM,SAAmB9B,UAAU,EAAMwE,YAAa,QACvF,CAAE5E,KAAM,QAASD,MAAO,QAASmC,KAAM,SAAmB9B,UAAU,EAAMwE,YAAa,MA6HjFvE,SAAUqH,KAIZ3I,EAAAA,EAAAA,KAAAG,EAAAA,SAAA,CAAAJ,UACED,EAAAA,EAAAA,MAAA,OAAKP,UAAU,wCAAuCQ,SAAA,EAEpDC,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHC,EAAAA,EAAAA,KAACsM,EAAY,CACXjN,OAAQgJ,EAAQhJ,SAAWgJ,EAAQ1H,MAAQ,CAAC0H,EAAQ1H,OAAS,IAC7DrB,YAAa+I,EAAQpH,KACrB1B,UAAU,YAKdS,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EAExBD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,mCAAkCQ,SAAEsI,EAAQpH,QAC1DnB,EAAAA,EAAAA,MAAA,KAAGP,UAAU,6BAA4BQ,SAAA,CAAC,QAAMsI,EAAQxB,WAI1D7G,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,QAAMT,UAAU,mCAAkCQ,UAAEwM,EAAAA,EAAAA,IAAelE,EAAQrB,WAI5EqB,EAAQlB,cACPrH,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,yCAAwCQ,SAAC,iBACvDC,EAAAA,EAAAA,KAAA,KAAGT,UAAU,gBAAeQ,SAAEsI,EAAQlB,kBAK1CrH,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yBAAwBQ,SAAA,EACrCD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,yBAClDC,EAAAA,EAAAA,KAAA,KAAGT,UAAU,sCAAqCQ,SAAEsI,EAAQnB,mBAE9DpH,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,kBAClDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGT,UAAU,sCAAqCQ,SAAEsI,EAAQpB,SAC5DjH,EAAAA,EAAAA,KAAA,KAAGT,UAAW,WAAW2K,EAAYP,QAAQ5J,SAAEmK,EAAYC,iBAMjErK,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAACwM,EAAAA,EAAW,CAACzF,OAhLCA,KAC5B,OAAQA,GACN,IAAK,SACH,MAAO,SACT,IAAK,WAIL,QACE,MAAO,UAHT,IAAK,eACH,MAAO,WAGX,EAsKmC0F,CAAqBpE,EAAQtB,QAAS5D,KAAK,cAChEnD,EAAAA,EAAAA,KAAA,QAAMT,UAAU,oGAAmGQ,SAChHsI,EAAQvB,yBAUrB2B,GAAaJ,EAAQd,YAAcc,EAAQd,WAAW3H,OAAS,IAC/DI,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAACqH,EAAAA,EAAO,CAAC9H,UAAU,2BACnBS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,2BAEpDC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,uDAAsDQ,SAClEsI,EAAQd,WAAW7G,KAAKgM,IACvB5M,EAAAA,EAAAA,MAAA,OAAwBP,UAAU,4BAA2BQ,SAAA,EAC3DC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAE2M,EAAUzL,QAC7DjB,EAAAA,EAAAA,KAAA,MAAIT,UAAU,6BAA4BQ,SACpB,YAAnB2M,EAAUvJ,KACc,SAApBuJ,EAAUxL,MAAmB,MAAQ,KACtC,GAAGwL,EAAUxL,QAAQwL,EAAUhD,KAAO,IAAIgD,EAAUhD,OAAS,SAL3DgD,EAAUzN,cAgB5BwJ,GAAaJ,EAAQZ,UAAYY,EAAQZ,SAAS7H,OAAS,IAC3DI,EAAAA,EAAAA,KAAC4G,EAAAA,EAAI,CAAA7G,UACHD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWQ,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8BAA6BQ,SAAA,EAC1CC,EAAAA,EAAAA,KAACiG,EAAyB,CAAC1G,UAAU,2BACrCS,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAC,yBAEpDC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,uDAAsDQ,SAClEsI,EAAQZ,SAAS/G,KAAKsE,IACrBhF,EAAAA,EAAAA,KAAA,OAAsBT,UAAU,0EAAyEQ,UACvGD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,6BAA4BQ,SAAA,CACxCiF,EAAQrE,OACPX,EAAAA,EAAAA,KAAA,OACEC,IAAK+E,EAAQrE,MACbT,IAAK8E,EAAQ/D,KACb1B,UAAU,uCAGZS,EAAAA,EAAAA,KAAA,OAAKT,UAAU,oEAAmEQ,UAChFC,EAAAA,EAAAA,KAACgK,EAAAA,EAAQ,CAACzK,UAAU,6BAGxBO,EAAAA,EAAAA,MAAA,OAAKP,UAAU,iBAAgBQ,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,oCAAmCQ,SAAEiF,EAAQ/D,QAC3DjB,EAAAA,EAAAA,KAAA,KAAGT,UAAU,kCAAiCQ,SAAEiF,EAAQ6B,OACxD7G,EAAAA,EAAAA,KAAA,KAAGT,UAAU,2CAA0CQ,UACpDwM,EAAAA,EAAAA,IAAevH,EAAQgC,UAE1BlH,EAAAA,EAAAA,MAAA,KAAGP,UAAU,wBAAuBQ,SAAA,CAAC,UAAQiF,EAAQiC,UAGrDjH,EAAAA,EAAAA,KAAA,OAAKT,UAAU,iBAAgBQ,SAC5BtB,OAAOkO,QAAQ3H,EAAQuC,YAAY7G,KAAIvC,IAAA,IAAEqH,EAAKtE,GAAM/C,EAAA,OACnD2B,EAAAA,EAAAA,MAAA,OAAeP,UAAU,+BAA8BQ,SAAA,EACrDD,EAAAA,EAAAA,MAAA,QAAMP,UAAU,2BAA0BQ,SAAA,CAAEyF,EAAI,QAChDxF,EAAAA,EAAAA,KAAA,QAAMT,UAAU,gBAAeQ,SAAEmB,MAFzBsE,EAGJ,aA3BNR,EAAQ/F,eAsCxB,C,uFCnkBV,MAAMoL,EAAwClM,IAOvC,IAPwC,MAC7CE,EAAK,YACL8I,EAAW,QACXqD,EAAO,YACPF,EAAW,UACX/K,EAAY,GAAE,OACdqN,GACDzO,EACC,OACE2B,EAAAA,EAAAA,MAAA,OACEP,UAAW,QAAQA,IACnB,cAAaqN,EAAO7M,SAAA,CAGnBuK,GAAeA,EAAY1K,OAAS,IACnCI,EAAAA,EAAAA,KAAA,OAAKT,UAAU,YAAY,aAAW,aAAYQ,UAChDD,EAAAA,EAAAA,MAAA,MAAIP,UAAU,oDAAmDQ,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAAC6M,EAAAA,GAAI,CACHC,GAAG,IACHvN,UAAU,uCACV,aAAW,OAAMQ,UAEjBC,EAAAA,EAAAA,KAAC+M,EAAAA,EAAQ,CAACxN,UAAU,gBAIvB+K,EAAY5J,KAAI,CAACmE,EAAMjE,KACtBd,EAAAA,EAAAA,MAAA,MAAgBP,UAAU,oBAAmBQ,SAAA,EAC3CC,EAAAA,EAAAA,KAACS,EAAAA,EAAgB,CAAClB,UAAU,+BAC3BsF,EAAK0F,MAAQ3J,EAAQ0J,EAAY1K,OAAS,GACzCI,EAAAA,EAAAA,KAAC6M,EAAAA,GAAI,CACHC,GAAIjI,EAAK0F,KACThL,UAAU,qBAAoBQ,SAE7B8E,EAAK7D,SAGRhB,EAAAA,EAAAA,KAAA,QAAMT,UAAU,4BAA2BQ,SAAE8E,EAAK7D,UAV7CJ,WAmBjBd,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8EAA6EQ,SAAA,EAC1FD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIT,UAAU,mCAAkCQ,SAAE1B,IACjD8I,GAAsC,kBAAhBA,GACrBnH,EAAAA,EAAAA,KAAA,KAAGT,UAAU,6BAA4BQ,SAAEoH,IAE3CA,KAIHqD,IACCxK,EAAAA,EAAAA,KAAA,OAAKT,UAAU,oCAAmCQ,SAC/CyK,SAIH,EAIV,GAAewC,EAAAA,EAAAA,MAAK3C,E,gDC3FpB,SAASwB,EAAU1N,EAIhBC,GAAQ,IAJS,MAClBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBqN,E,yDCGlD,MAAMjF,EAA4BzI,IAgB3B,IAhB4B,MACjCE,EAAK,SACL4O,EAAQ,SACRlN,EAAQ,UACRR,EAAY,GAAE,cACd2N,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBjI,EAAI,OACJkI,EAAM,QACNjN,EAAO,UACPkN,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACf9B,GAAU,EAAK,OACfkB,GACDzO,EAEC,MAAMsP,EAAc,6BACID,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFlN,EAAU,iBAAmB,WAC7Bb,QAIEmO,EAAgB,mFAElBP,QAIEQ,EAAc,SAChBJ,EAAY,GAAK,cACjBL,QAIEU,EAAgB,4DAElBR,QAIJ,OAAI1B,GAEA5L,EAAAA,EAAAA,MAAA,OAAKP,UAAWkO,EAAa,cAAab,EAAO7M,SAAA,EAC7C1B,GAAS4O,GAAY9H,KACrBrF,EAAAA,EAAAA,MAAA,OAAKP,UAAWmO,EAAc3N,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAKP,UAAU,SAAQQ,SAAA,CACpB1B,IAAS2B,EAAAA,EAAAA,KAAA,OAAKT,UAAU,gDACxB0N,IAAYjN,EAAAA,EAAAA,KAAA,OAAKT,UAAU,wDAE7B4F,IAAQnF,EAAAA,EAAAA,KAAA,OAAKT,UAAU,uDAI5BS,EAAAA,EAAAA,KAAA,OAAKT,UAAWoO,EAAY5N,UAC1BC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,6CAGhB8N,IACCrN,EAAAA,EAAAA,KAAA,OAAKT,UAAWqO,EAAc7N,UAC5BC,EAAAA,EAAAA,KAAA,OAAKT,UAAU,sDAQvBO,EAAAA,EAAAA,MAAA,OACEP,UAAWkO,EACXrN,QAASA,EACT,cAAawM,EAAO7M,SAAA,EAElB1B,GAAS4O,GAAY9H,KACrBrF,EAAAA,EAAAA,MAAA,OAAKP,UAAWmO,EAAc3N,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,CACoB,kBAAV1B,GACN2B,EAAAA,EAAAA,KAAA,MAAIT,UAAU,qCAAoCQ,SAAE1B,IAEpDA,EAEmB,kBAAb4O,GACNjN,EAAAA,EAAAA,KAAA,KAAGT,UAAU,6BAA4BQ,SAAEkN,IAE3CA,KAGH9H,IAAQnF,EAAAA,EAAAA,KAAA,OAAKT,UAAU,eAAcQ,SAAEoF,QAI5CnF,EAAAA,EAAAA,KAAA,OAAKT,UAAWoO,EAAY5N,SAAEA,IAE7BsN,IACCrN,EAAAA,EAAAA,KAAA,OAAKT,UAAWqO,EAAc7N,SAC3BsN,MAGD,EAIV,GAAeL,EAAAA,EAAAA,MAAKpG,E,qDC3Hb,MAAMiH,EAAa,SAACC,GAA0E,IAAtDhI,EAAmCiI,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKD,EAAY,MAAO,IAExB,IACE,MAAME,EAAO,IAAI/B,KAAK6B,GAGhBG,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACFtI,GAGL,OAAO,IAAIuI,KAAKC,eAAe,QAASL,GAAgBM,OAAOP,EACjE,CAAE,MAAO5M,GAEP,OADAqB,QAAQrB,MAAM,yBAA0BA,GACjC0M,CACT,CACF,EAkBavB,EAAiB,SAC5BiC,GAGY,IAFZC,EAAgBV,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,MACnBW,EAAcX,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAIM,KAAKM,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBP,OAAOC,EACZ,CAAE,MAAOpN,GAEP,OADAqB,QAAQrB,MAAM,6BAA8BA,GACrC,GAAGqN,KAAYD,EAAOO,QAAQ,IACvC,CACF,EAkDaC,EAAkBC,IAC7B,GAAc,IAAVA,EAAa,MAAO,UAExB,MAEM9M,EAAImC,KAAK4K,MAAM5K,KAAK6K,IAAIF,GAAS3K,KAAK6K,IAFlC,OAIV,MAAO,GAAGzJ,YAAYuJ,EAAQ3K,KAAK8K,IAJzB,KAIgCjN,IAAI4M,QAAQ,OAHxC,CAAC,QAAS,KAAM,KAAM,KAAM,MAGyB5M,IAAI,C,uDC3GzE,MAsGA,EAtGsDhE,IAM/C,IANgD,KACrD8G,EAAO,KAAI,UACX1F,EAAY,GAAE,QACdyF,EAAU,UAAS,MACnB2E,EAAQ,UAAS,gBACjB0F,GAAkB,GACnBlR,EACC,MAAMmR,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiB1F,EAGxD,MAAgB,YAAZ3E,GAEAlF,EAAAA,EAAAA,MAAA,OACEP,UAAW,oCAAoCA,IAC/CwQ,KAAK,SACL,aAAW,UAAShQ,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACET,UAAW,wDAAwD+P,EAAQrK,GAAMuK,UACjFZ,MAAO,CACLoB,eAAgBF,EAChBG,iBAAkBH,MAGtB9P,EAAAA,EAAAA,KAAA,QAAMT,UAAU,UAASQ,SAAC,kBAMhB,SAAZiF,GAEAlF,EAAAA,EAAAA,MAAA,OACEP,UAAW,0DAA0DA,IACrEwQ,KAAK,SACL,aAAW,UAAShQ,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAMwK,wBAC5Bb,MAAO,CAAEsB,gBAAiBJ,MAE5B9P,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAMwK,wBAC5Bb,MAAO,CAAEsB,gBAAiBJ,MAE5B9P,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAMwK,wBAC5Bb,MAAO,CAAEsB,gBAAiBJ,MAE5B9P,EAAAA,EAAAA,KAAA,QAAMT,UAAU,UAASQ,SAAC,kBAMhB,UAAZiF,GAEAlF,EAAAA,EAAAA,MAAA,OACEP,UAAW,oCAAoCA,IAC/CwQ,KAAK,SACL,aAAW,UAAShQ,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAMyK,kCAC5Bd,MAAO,CAAEsB,gBAAiBJ,MAE5B9P,EAAAA,EAAAA,KAAA,QAAMT,UAAU,UAASQ,SAAC,kBAMhB,WAAZiF,GAEAlF,EAAAA,EAAAA,MAAA,OACEP,UAAW,oCAAoCA,IAC/CwQ,KAAK,SACL,aAAW,UAAShQ,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAM0K,oCAC5Bf,MAAO,CAAEjF,MAAOmG,GAAe/P,UAE/BC,EAAAA,EAAAA,KAAA,OACET,UAAW,GAAG+P,EAAQrK,GAAMyK,0CAC5Bd,MAAO,CAAEsB,gBAAiBJ,QAG9B9P,EAAAA,EAAAA,KAAA,QAAMT,UAAU,UAASQ,SAAC,kBAKzB,IAAI,C,yGC9Fb,MAiDA,EAjDgD5B,IAIzC,IAJ0C,OAC/C4I,EACA5D,KAAMgN,EAAQ,OAAM,UACpB5Q,EAAY,IACbpB,EAEC,IAAK4I,EACH,OACE/G,EAAAA,EAAAA,KAAA,QAAMT,UAAW,qGAAqGA,IAAYQ,SAAC,YAMvI,MAAMqQ,EAAYrJ,EAAOf,cACzB,IAAIqK,EAAa,GACblL,EAAO,KAGO,WAAdiL,GAAwC,aAAdA,GAA0C,cAAdA,GACxDC,EAAa,8BACblL,GAAOnF,EAAAA,EAAAA,KAACsQ,EAAAA,EAAe,CAAC/Q,UAAU,kBACX,YAAd6Q,GAAyC,eAAdA,GACpCC,EAAa,4BACblL,GAAOnF,EAAAA,EAAAA,KAACuQ,EAAAA,EAAS,CAAChR,UAAU,kBACL,WAAd6Q,GAAwC,aAAdA,GACnCC,EAAa,0BACblL,GAAOnF,EAAAA,EAAAA,KAACwQ,EAAAA,EAAW,CAACjR,UAAU,kBACP,YAAd6Q,GACTC,EAAa,gCACblL,GAAOnF,EAAAA,EAAAA,KAACyQ,EAAAA,EAAS,CAAClR,UAAU,kBACL,YAAd6Q,GACTC,EAAa,gCACblL,GAAOnF,EAAAA,EAAAA,KAAC0Q,EAAAA,EAAqB,CAACnR,UAAU,kBAExC8Q,EAAa,4BAIf,MAAMM,EAAkB5J,EAASA,EAAO6J,OAAO,GAAGC,cAAgB9J,EAAO3B,MAAM,GAAK,UAEpF,OACEtF,EAAAA,EAAAA,MAAA,QAAMP,UAAW,2EAA2E8Q,KAAc9Q,IAAYQ,SAAA,CACnHoF,EACAwL,IACI,C,gDC7DX,SAAS1G,EAAa9L,EAInBC,GAAQ,IAJY,MACrBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByL,E,gDCvBlD,SAASwG,EAAStS,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiS,E,uDCPlD,MA+FA,EA/F4CtS,IAarC,IAbsC,MAC3C6C,EAAK,KACLC,EAAI,KACJkC,EAAO,OAAM,MACbjC,EAAK,SACLC,EAAQ,MACRC,EAAK,SACLC,GAAW,EAAK,YAChBwE,EAAc,GAAE,QAChBC,EAAU,GAAE,UACZvG,EAAY,GAAE,SACd+B,GAAW,EAAK,QAChBoK,GAAU,GACXvN,EACC,MAAM2S,EAAe,sDACnB1P,EAAQ,yDAA2D,2DAqErE,OACEtB,EAAAA,EAAAA,MAAA,OAAKP,UAAW,GAAGA,IAAYQ,SAAA,EAC7BD,EAAAA,EAAAA,MAAA,SAAOiR,QAAS9P,EAAM1B,UAAU,0CAAyCQ,SAAA,CACtEiB,EAAM,IAAEK,IAAYrB,EAAAA,EAAAA,KAAA,QAAMT,UAAU,eAAcQ,SAAC,SArEtCiR,MAClB,OAAQ7N,GACN,IAAK,WACH,OACEnD,EAAAA,EAAAA,KAAA,YACEf,GAAIgC,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACV5B,UAAWuR,EACXjL,YAAaA,EACbvE,SAAUA,IAIhB,IAAK,SACH,OACEtB,EAAAA,EAAAA,KAAA,UACEf,GAAIgC,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACV5B,UAAWuR,EACXxP,SAAUA,GAAYoK,EAAQ3L,SAE7B2L,GACC1L,EAAAA,EAAAA,KAAA,UAAQkB,MAAM,GAAEnB,SAAC,eAEjB+F,EAAQpF,KAAIuQ,IACVjR,EAAAA,EAAAA,KAAA,UAA2BkB,MAAO+P,EAAO/P,MAAMnB,SAC5CkR,EAAOjQ,OADGiQ,EAAO/P,WAQ9B,IAAK,WACH,OACElB,EAAAA,EAAAA,KAAA,SACEmD,KAAK,WACLlE,GAAIgC,EACJA,KAAMA,EACNiQ,QAAShQ,EACTC,SAAUA,EACV5B,UAAU,kEACV+B,SAAUA,IAIhB,QACE,OACEtB,EAAAA,EAAAA,KAAA,SACEmD,KAAMA,EACNlE,GAAIgC,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACV5B,UAAWuR,EACXjL,YAAaA,EACbvE,SAAUA,IAGlB,EAQG0P,GACA5P,IAASpB,EAAAA,EAAAA,KAAA,KAAGT,UAAU,4BAA2BQ,SAAEqB,MAChD,C,gDC3GV,SAASX,EAAgBtC,EAItBC,GAAQ,IAJe,MACxBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiC,E,2CCR3C,MAAM0Q,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIxO,IAAIwO,IACD,CACT,CAAE,MAAOrQ,GACP,OAAO,CACT,GAGWsQ,EAAcxQ,GACX,OAAVA,QAA4BsG,IAAVtG,IACD,kBAAVA,EAA2BA,EAAMyQ,OAAO/R,OAAS,GACxDgS,MAAMC,QAAQ3Q,IAAeA,EAAMtB,OAAS,GAYrCkS,EAAa5Q,GACjB,WAAWmQ,KAAKnQ,GAGZ6Q,EAAa7Q,GACjB,sBAAsBmQ,KAAKnQ,GAGvB8Q,EAAkB9Q,GACtB,iBAAiBmQ,KAAKnQ,GAGlB+Q,EAAenE,IAC1B,MAAME,EAAO,IAAI/B,KAAK6B,GACtB,OAAQoE,MAAMlE,EAAKmE,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAASzS,OAAS,OAGjB,QAAQyR,KAAKgB,OAGb,QAAQhB,KAAKgB,OAGb,QAAQhB,KAAKgB,MAGb,sCAAsChB,KAAKgB,MAwBrC1H,EAAeA,CAC1B6H,EACA5H,KAEA,MAAMzE,EAA2C,CAAC,EAUlD,OARA1H,OAAOkO,QAAQ/B,GAAiB6H,SAAQtU,IAAyB,IAAvBuU,EAAWC,GAAMxU,EACzD,MAAMqH,EAAMkN,EACNtR,EA1BmBwR,EAC3BC,EACA3R,EACAyR,EACAzM,KAEA,MAAM4M,EAAYlB,MAAMC,QAAQc,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMI,KAAQD,EACjB,IAAKC,EAAKC,UAAU9R,EAAOgF,GACzB,OAAO6M,EAAKE,QAIhB,MAAO,EAAE,EAYOL,CAAcF,EAAWF,EAAOhN,GAAMmN,EAAOH,GACvDpR,IACF+E,EAAOX,GAAOpE,EAChB,IAGK+E,CAAM,EAIFyE,EAAkB,CAC7BvJ,SAAU,WAA2C,MAAsB,CACzE2R,UAAWtB,EACXuB,QAFwBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAEDqD,MAAO,WAAuD,MAAsB,CAClF4B,UAAW7B,EACX8B,QAFqBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDwD,MAAO,WAAsD,MAAsB,CACjFyB,UAAW1B,EACX2B,QAFqBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAED0D,IAAK,WAA6C,MAAsB,CACtEuB,UAAWxB,EACXyB,QAFmBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDmF,UAAWA,CAACC,EAAaF,KAAgB,CACvCD,UAAY9R,GA3GSgS,EAAChS,EAAeiS,IAChCjS,EAAMtB,QAAUuT,EA0GSD,CAAUhS,EAAOiS,GAC/CF,QAASA,GAAW,oBAAoBE,iBAG1CC,UAAWA,CAACC,EAAaJ,KAAgB,CACvCD,UAAY9R,GA5GSkS,EAAClS,EAAemS,IAChCnS,EAAMtB,QAAUyT,EA2GSD,CAAUlS,EAAOmS,GAC/CJ,QAASA,GAAW,wBAAwBI,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EN,UAAWlB,EACXmB,QAFuBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAEDwF,QAAS,WAAwD,MAAsB,CACrFP,UAAWjB,EACXkB,QAFuBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAEDyF,aAAc,WAAwD,MAAsB,CAC1FR,UAAWhB,EACXiB,QAF4BlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDC,KAAM,WAA8C,MAAsB,CACxEgF,UAAWf,EACXgB,QAFoBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDsE,SAAU,WAA2H,MAAsB,CACzJW,UAAWT,EACXU,QAFwBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAED0F,cAAe,WAA2C,MAAsB,CAC9ET,UAAWA,CAAC9R,EAAegF,IAAmBkM,EAAiBlR,EAAe,OAARgF,QAAQ,IAARA,OAAQ,EAARA,EAAUoM,iBAChFW,QAF6BlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAED2F,qBAAsB,WAA2C,MAAsB,CACrFV,UAAWA,CAAC9R,EAAegF,IAAmBkM,EAAiBlR,EAAe,OAARgF,QAAQ,IAARA,OAAQ,EAARA,EAAUmM,UAChFY,QAFoClF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDlH,IAAK,WAA6C,MAAsB,CACtEmM,UAAY9R,GAAkB,sBAAsBmQ,KAAKnQ,GACzD+R,QAFmBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAED/G,MAAO,WAA+C,MAAsB,CAC1EgM,UAAY9R,GAAkBA,EAAQ,GAAKA,GAAS,OACpD+R,QAFqBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAED9G,MAAO,WAAwD,MAAsB,CACnF+L,UAAY9R,GAAkByS,OAAOC,UAAU1S,IAAUA,GAAS,EAClE+R,QAFqBlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAED7G,aAAc,WAA6D,MAAsB,CAC/F8L,UAAY9R,GAAkByS,OAAOC,UAAU1S,IAAUA,GAAS,EAClE+R,QAF4BlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDlD,iBAAkB,WAAuE,MAAsB,CAC7GmI,UAAWA,CAAC9L,EAAsBhB,KAC3BA,IAAaA,EAASe,OACpBC,GAAgBhB,EAASe,MAElCgM,QALgClF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAED8F,cAAe,WAAkD,MAAsB,CACrFb,UAAY9R,GAAiB0Q,MAAMC,QAAQ3Q,IAAUA,EAAMtB,OAAS,EACpEqT,QAF6BlF,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAED+F,WAAY,eAACrS,EAAgBsM,UAAAnO,OAAA,QAAA4H,IAAAuG,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEiF,UAAY9R,KACL0Q,MAAMC,QAAQ3Q,IACZA,EAAMtB,QAAU6B,EAEzBwR,SALkDlF,UAAAnO,OAAA,EAAAmO,UAAA,QAAAvG,IAK9B,WAAW/F,mBAChC,E,yDCxMH,MAAMsD,EAAgC5G,IAmB/B,IAnBgC,SACrC4B,EAAQ,QACRiF,EAAU,UAAS,KACnBC,EAAO,KAAI,UACX1F,EAAY,GAAE,QACda,EAAO,SACPkB,GAAW,EAAK,KAChB6B,EAAO,SAAQ,KACfgC,EAAI,aACJ4O,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBtI,GAAU,EAAK,QACfuI,GAAU,EAAK,KACfC,EAAI,OACJ/P,EAAM,IACNgQ,EAAG,MACH9V,EAAK,UACL+V,EAAS,OACTxH,GACDzO,EACC,MAwBMkW,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTvK,KAAM,2EACNwK,KAAM,kFAiBW3P,WAdC,CAClB4P,GAAI,oBACJrF,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJgF,GAAI,qBAUU5P,WAPQ3D,EAAW,gCAAkC,yBAClD0S,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5C1U,QAGEuV,GACJhV,EAAAA,EAAAA,MAAAK,EAAAA,SAAA,CAAAJ,SAAA,CACG2L,IACC5L,EAAAA,EAAAA,MAAA,OACEP,UAAU,+CACVZ,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMkB,SAAA,EAElBC,EAAAA,EAAAA,KAAA,UACET,UAAU,aACVwV,GAAG,KACHC,GAAG,KACHC,EAAE,KACFlW,OAAO,eACPD,YAAY,OAEdkB,EAAAA,EAAAA,KAAA,QACET,UAAU,aACVX,KAAK,eACLQ,EAAE,uHAKP+F,GAAyB,SAAjB4O,IAA4BrI,IACnC1L,EAAAA,EAAAA,KAAA,QAAMT,UAAU,OAAMQ,SAAEoF,IAGzBpF,EAEAoF,GAAyB,UAAjB4O,IACP/T,EAAAA,EAAAA,KAAA,QAAMT,UAAU,OAAMQ,SAAEoF,OAM9B,OAAI+O,GAEAlU,EAAAA,EAAAA,KAAA,KACEkU,KAAMA,EACN3U,UAAW8U,EACXlQ,OAAQA,EACRgQ,IAAKA,IAAmB,WAAXhQ,EAAsB,2BAAwBqD,GAC3DpH,QAASA,EACT/B,MAAOA,EACP,aAAY+V,EACZ,cAAaxH,EAAO7M,SAEnB+U,KAOL9U,EAAAA,EAAAA,KAAA,UACEmD,KAAMA,EACN5D,UAAW8U,EACXjU,QAASA,EACTkB,SAAUA,GAAYoK,EACtBrN,MAAOA,EACP,aAAY+V,EACZ,cAAaxH,EAAO7M,SAEnB+U,GACM,EAIb,GAAe9H,EAAAA,EAAAA,MAAKjI,E,gDC9JpB,SAASiF,EAAQ7L,EAIdC,GAAQ,IAJO,MAChBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wFAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBwL,E,gDCvBlD,SAAS3F,EAAQlG,EAIdC,GAAQ,IAJO,MAChBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,2BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB6F,E,gDCvBlD,SAASoH,EAAStN,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiN,E", "sources": ["../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/ImageGallery.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js", "components/common/MultipleImageUpload.tsx", "components/common/DynamicArrayField.tsx", "../node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js", "components/common/ProductEditForm.tsx", "pages/ProductDetailsPage.tsx", "components/layout/PageHeader.tsx", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "components/common/Card.tsx", "utils/formatters.ts", "components/common/LoadingSpinner.tsx", "components/common/StatusBadge.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "components/common/FormField.tsx", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "utils/validation.ts", "components/common/Button.tsx", "../node_modules/@heroicons/react/24/outline/esm/CubeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "/**\n * Image Gallery Component\n *\n * A reusable component for displaying product images with carousel functionality\n */\n\nimport React, { useState } from 'react';\nimport { ChevronLeftIcon, ChevronRightIcon, PhotoIcon } from '@heroicons/react/24/outline';\n\ninterface ImageGalleryProps {\n  images: string[];\n  productName: string;\n  className?: string;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({\n  images,\n  productName,\n  className = ''\n}) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  const hasImages = images && images.length > 0;\n  const hasMultipleImages = hasImages && images.length > 1;\n\n  const goToPrevious = () => {\n    setCurrentImageIndex((prevIndex) => \n      prevIndex === 0 ? images.length - 1 : prevIndex - 1\n    );\n  };\n\n  const goToNext = () => {\n    setCurrentImageIndex((prevIndex) => \n      prevIndex === images.length - 1 ? 0 : prevIndex + 1\n    );\n  };\n\n  const goToImage = (index: number) => {\n    setCurrentImageIndex(index);\n  };\n\n  if (!hasImages) {\n    return (\n      <div className={`bg-gray-200 rounded-lg flex items-center justify-center ${className}`}>\n        <div className=\"text-center\">\n          <PhotoIcon className=\"mx-auto h-16 w-16 text-gray-400\" />\n          <p className=\"mt-2 text-sm text-gray-500\">No images available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Main Image Display */}\n      <div className=\"relative bg-gray-100 rounded-lg overflow-hidden aspect-square\">\n        <img\n          src={images[currentImageIndex]}\n          alt={`${productName} - Image ${currentImageIndex + 1}`}\n          className=\"w-full h-full object-cover\"\n        />\n        \n        {/* Navigation Arrows */}\n        {hasMultipleImages && (\n          <>\n            <button\n              onClick={goToPrevious}\n              className=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity\"\n              aria-label=\"Previous image\"\n            >\n              <ChevronLeftIcon className=\"h-5 w-5\" />\n            </button>\n            <button\n              onClick={goToNext}\n              className=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity\"\n              aria-label=\"Next image\"\n            >\n              <ChevronRightIcon className=\"h-5 w-5\" />\n            </button>\n          </>\n        )}\n\n        {/* Image Counter */}\n        {hasMultipleImages && (\n          <div className=\"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm\">\n            {currentImageIndex + 1} / {images.length}\n          </div>\n        )}\n      </div>\n\n      {/* Thumbnail Navigation */}\n      {hasMultipleImages && (\n        <div className=\"flex space-x-2 overflow-x-auto pb-2\">\n          {images.map((image, index) => (\n            <button\n              key={index}\n              onClick={() => goToImage(index)}\n              className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${\n                index === currentImageIndex\n                  ? 'border-blue-500'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <img\n                src={image}\n                alt={`${productName} thumbnail ${index + 1}`}\n                className=\"w-full h-full object-cover\"\n              />\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImageGallery;\n", "import * as React from \"react\";\nfunction ArrowUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowUpIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowDownIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownIcon);\nexport default ForwardRef;", "/**\n * Multiple Image Upload Component\n * \n * A reusable component for uploading and managing multiple images with drag and drop support.\n * Extends the existing ImageUpload pattern to handle arrays of images.\n */\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport { PhotoIcon, XMarkIcon, PlusIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';\nimport { validateFile } from '../../utils/errorHandling';\n\ninterface MultipleImageUploadProps {\n  label: string;\n  name: string;\n  value: (File | string)[];\n  onChange: (files: (File | string)[]) => void;\n  error?: string | undefined;\n  required?: boolean;\n  disabled?: boolean;\n  maxSize?: number; // in bytes\n  allowedTypes?: string[];\n  maxFiles?: number;\n  className?: string;\n}\n\nconst MultipleImageUpload: React.FC<MultipleImageUploadProps> = ({\n  label,\n  name,\n  value = [],\n  onChange,\n  error,\n  required = false,\n  disabled = false,\n  maxSize = 5 * 1024 * 1024, // 5MB default\n  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  maxFiles = 10,\n  className = ''\n}) => {\n  const [isDragOver, setIsDragOver] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = useCallback((files: FileList) => {\n    const validFiles: File[] = [];\n    const currentCount = value.length;\n\n    for (let i = 0; i < files.length && (currentCount + validFiles.length) < maxFiles; i++) {\n      const file = files[i];\n      if (!file) continue;\n\n      const validation = validateFile(file, {\n        maxSize,\n        allowedTypes\n      });\n\n      if (validation.valid) {\n        validFiles.push(file);\n      } else {\n        console.error('File validation failed:', validation.error);\n      }\n    }\n\n    if (validFiles.length > 0) {\n      onChange([...value, ...validFiles]);\n    }\n  }, [value, onChange, maxSize, allowedTypes, maxFiles]);\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files) {\n      handleFileSelect(files);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    if (!disabled) {\n      setIsDragOver(true);\n    }\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    if (disabled) return;\n\n    const files = e.dataTransfer.files;\n    if (files) {\n      handleFileSelect(files);\n    }\n  };\n\n  const handleRemove = (index: number) => {\n    const newFiles = value.filter((_, i) => i !== index);\n    onChange(newFiles);\n  };\n\n  const handleReorder = (fromIndex: number, toIndex: number) => {\n    if (toIndex < 0 || toIndex >= value.length) return;\n\n    const newFiles = [...value];\n    const [movedFile] = newFiles.splice(fromIndex, 1);\n    if (movedFile) {\n      newFiles.splice(toIndex, 0, movedFile);\n      onChange(newFiles);\n    }\n  };\n\n  const handleClick = () => {\n    if (!disabled && fileInputRef.current && value.length < maxFiles) {\n      fileInputRef.current.click();\n    }\n  };\n\n  const getPreviewUrl = (file: File | string): string => {\n    if (typeof file === 'string') {\n      return file;\n    }\n    return URL.createObjectURL(file);\n  };\n\n  return (\n    <div className={className}>\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n        {label} {required && <span className=\"text-red-500\">*</span>}\n        <span className=\"text-xs text-gray-500 ml-2\">\n          ({value.length}/{maxFiles} images)\n        </span>\n      </label>\n\n      {/* Image Grid */}\n      {value.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4\">\n          {value.map((file, index) => (\n            <div key={index} className=\"relative group\">\n              <img\n                src={getPreviewUrl(file)}\n                alt={`Preview ${index + 1}`}\n                className=\"w-full h-24 object-cover rounded-lg border border-gray-200\"\n              />\n              \n              {/* Controls */}\n              <div className=\"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-1\">\n                {/* Move Up */}\n                {index > 0 && (\n                  <button\n                    type=\"button\"\n                    onClick={() => handleReorder(index, index - 1)}\n                    className=\"p-1 bg-white rounded-full hover:bg-gray-100 transition-colors\"\n                    title=\"Move up\"\n                  >\n                    <ArrowUpIcon className=\"h-3 w-3 text-gray-600\" />\n                  </button>\n                )}\n                \n                {/* Move Down */}\n                {index < value.length - 1 && (\n                  <button\n                    type=\"button\"\n                    onClick={() => handleReorder(index, index + 1)}\n                    className=\"p-1 bg-white rounded-full hover:bg-gray-100 transition-colors\"\n                    title=\"Move down\"\n                  >\n                    <ArrowDownIcon className=\"h-3 w-3 text-gray-600\" />\n                  </button>\n                )}\n                \n                {/* Remove */}\n                <button\n                  type=\"button\"\n                  onClick={() => handleRemove(index)}\n                  className=\"p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n                  title=\"Remove image\"\n                >\n                  <XMarkIcon className=\"h-3 w-3\" />\n                </button>\n              </div>\n              \n              {/* Primary indicator */}\n              {index === 0 && (\n                <div className=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded\">\n                  Primary\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Upload Area */}\n      {value.length < maxFiles && (\n        <div\n          className={`\n            relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n            ${isDragOver ? 'border-primary bg-primary bg-opacity-5' : 'border-gray-300'}\n            ${error ? 'border-red-300' : ''}\n            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-gray-50'}\n          `}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={handleClick}\n        >\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            name={name}\n            accept={allowedTypes.join(',')}\n            onChange={handleFileInputChange}\n            className=\"hidden\"\n            disabled={disabled}\n            multiple\n          />\n\n          <div>\n            <div className=\"flex justify-center\">\n              {value.length === 0 ? (\n                <PhotoIcon className=\"h-12 w-12 text-gray-400\" />\n              ) : (\n                <PlusIcon className=\"h-8 w-8 text-gray-400\" />\n              )}\n            </div>\n            <div className=\"mt-4\">\n              <p className=\"text-sm text-gray-600\">\n                {isDragOver \n                  ? 'Drop images here' \n                  : value.length === 0 \n                    ? 'Click to upload or drag and drop images'\n                    : 'Add more images'\n                }\n              </p>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                PNG, JPG, GIF up to {Math.round(maxSize / 1024 / 1024)}MB each\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\n    </div>\n  );\n};\n\nexport default MultipleImageUpload;\n", "/**\n * Dynamic Array Field Component\n * \n * A reusable component for managing arrays of objects with add/remove/edit functionality.\n * Used for product attributes, variants, and other dynamic form sections.\n */\n\n\nimport { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport But<PERSON> from './Button';\nimport <PERSON><PERSON>ield from './FormField';\n\ninterface FieldConfig {\n  name: string;\n  label: string;\n  type: 'text' | 'number' | 'select' | 'textarea';\n  required?: boolean;\n  options?: { value: string; label: string }[];\n  placeholder?: string;\n}\n\ninterface DynamicArrayFieldProps<T> {\n  label: string;\n  value: T[];\n  onChange: (value: T[]) => void;\n  fieldConfigs: FieldConfig[];\n  createEmpty: () => T;\n  error?: string | undefined;\n  disabled?: boolean;\n  maxItems?: number;\n  className?: string;\n  itemLabel?: (item: T, index: number) => string;\n}\n\nfunction DynamicArrayField<T extends Record<string, any>>({\n  label,\n  value,\n  onChange,\n  fieldConfigs,\n  createEmpty,\n  error,\n  disabled = false,\n  maxItems = 20,\n  className = '',\n  itemLabel\n}: DynamicArrayFieldProps<T>) {\n  const handleAdd = () => {\n    if (value.length < maxItems) {\n      onChange([...value, createEmpty()]);\n    }\n  };\n\n  const handleRemove = (index: number) => {\n    const newValue = value.filter((_, i) => i !== index);\n    onChange(newValue);\n  };\n\n  const handleItemChange = (index: number, field: string, fieldValue: any) => {\n    const newValue = value.map((item, i) => \n      i === index ? { ...item, [field]: fieldValue } : item\n    );\n    onChange(newValue);\n  };\n\n  const getDefaultItemLabel = (item: T, index: number): string => {\n    if (itemLabel) {\n      return itemLabel(item, index);\n    }\n    \n    // Try to find a name or title field\n    const nameField = item.name || item.title || item.label;\n    if (nameField) {\n      return nameField;\n    }\n    \n    return `Item ${index + 1}`;\n  };\n\n  return (\n    <div className={className}>\n      <div className=\"flex items-center justify-between mb-4\">\n        <label className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          <span className=\"text-xs text-gray-500 ml-2\">\n            ({value.length}/{maxItems} items)\n          </span>\n        </label>\n        \n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleAdd}\n          disabled={disabled || value.length >= maxItems}\n          icon={<PlusIcon className=\"w-4 h-4\" />}\n        >\n          Add {label.slice(0, -1)} {/* Remove 's' from plural label */}\n        </Button>\n      </div>\n\n      {error && <p className=\"mb-4 text-sm text-red-600\">{error}</p>}\n\n      <div className=\"space-y-4\">\n        {value.map((item, index) => (\n          <div key={index} className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"text-sm font-medium text-gray-900\">\n                {getDefaultItemLabel(item, index)}\n              </h4>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleRemove(index)}\n                disabled={disabled}\n                icon={<XMarkIcon className=\"w-4 h-4\" />}\n                className=\"text-red-600 hover:text-red-700 border-red-300 hover:border-red-400\"\n              >\n                Remove\n              </Button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {fieldConfigs.map((config) => {\n                const fieldProps: any = {\n                  key: config.name,\n                  label: config.label,\n                  name: `${config.name}_${index}`,\n                  type: config.type,\n                  value: item[config.name] || '',\n                  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n                    const fieldValue = config.type === 'number'\n                      ? parseFloat(e.target.value) || 0\n                      : e.target.value;\n                    handleItemChange(index, config.name, fieldValue);\n                  },\n                  required: config.required || false,\n                  placeholder: config.placeholder || '',\n                  disabled: disabled,\n                  className: config.type === 'textarea' ? 'md:col-span-2' : ''\n                };\n\n                // Only add options if they exist\n                if (config.options) {\n                  fieldProps.options = config.options;\n                }\n\n                return <FormField {...fieldProps} />;\n              })}\n            </div>\n          </div>\n        ))}\n\n        {value.length === 0 && (\n          <div className=\"text-center py-8 text-gray-500\">\n            <p className=\"text-sm\">No {label.toLowerCase()} added yet.</p>\n            <p className=\"text-xs mt-1\">Click \"Add {label.slice(0, -1)}\" to get started.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default DynamicArrayField;\n", "import * as React from \"react\";\nfunction ClipboardDocumentListIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClipboardDocumentListIcon);\nexport default ForwardRef;", "/**\n * Product Edit Form Component\n * \n * A comprehensive form for editing product details including images, attributes, and variants.\n */\n\nimport React from 'react';\nimport Form<PERSON>ield from './FormField';\nimport MultipleImageUpload from './MultipleImageUpload';\nimport DynamicArrayField from './DynamicArrayField';\nimport Card from './Card';\nimport { TagIcon, ClipboardDocumentListIcon, PhotoIcon } from '@heroicons/react/24/outline';\nimport type { ProductAttribute, ProductVariant } from '../../features/suppliers/types';\n\n// Form data type that allows File objects for images\ninterface ProductFormData {\n  name: string;\n  sku: string;\n  category: string;\n  price: number;\n  stock: number;\n  minimumStock: number;\n  status: 'active' | 'inactive' | 'out_of_stock';\n  description: string;\n  images: (File | string)[];\n  attributes: ProductAttribute[];\n  variants: ProductVariant[];\n}\n\ninterface ProductEditFormProps {\n  formData: Partial<ProductFormData>;\n  errors: Record<string, string>;\n  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\n  onImagesChange: (images: (File | string)[]) => void;\n  onAttributesChange: (attributes: ProductAttribute[]) => void;\n  onVariantsChange: (variants: ProductVariant[]) => void;\n  createEmptyAttribute: () => ProductAttribute;\n  createEmptyVariant: () => ProductVariant;\n  attributeFieldConfigs: any[];\n  variantFieldConfigs: any[];\n  disabled?: boolean;\n}\n\nconst ProductEditForm: React.FC<ProductEditFormProps> = ({\n  formData,\n  errors,\n  onInputChange,\n  onImagesChange,\n  onAttributesChange,\n  onVariantsChange,\n  createEmptyAttribute,\n  createEmptyVariant,\n  attributeFieldConfigs,\n  variantFieldConfigs,\n  disabled = false\n}) => {\n  const categoryOptions = [\n    { value: 'Electronics', label: 'Electronics' },\n    { value: 'Accessories', label: 'Accessories' },\n    { value: 'Clothing', label: 'Clothing' },\n    { value: 'Home & Garden', label: 'Home & Garden' },\n    { value: 'Sports', label: 'Sports' },\n    { value: 'Books', label: 'Books' },\n    { value: 'Other', label: 'Other' }\n  ];\n\n  const statusOptions = [\n    { value: 'active', label: 'Active' },\n    { value: 'inactive', label: 'Inactive' },\n    { value: 'out_of_stock', label: 'Out of Stock' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Basic Product Information */}\n      <Card>\n        <div className=\"space-y-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Basic Information</h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <FormField\n              label=\"Product Name\"\n              name=\"name\"\n              type=\"text\"\n              value={formData.name || ''}\n              onChange={onInputChange}\n              error={errors.name}\n              required\n              disabled={disabled}\n              placeholder=\"Enter product name\"\n            />\n\n            <FormField\n              label=\"SKU\"\n              name=\"sku\"\n              type=\"text\"\n              value={formData.sku || ''}\n              onChange={onInputChange}\n              error={errors.sku}\n              required\n              disabled={disabled}\n              placeholder=\"e.g., WBH-PRO-001\"\n            />\n\n            <FormField\n              label=\"Category\"\n              name=\"category\"\n              type=\"select\"\n              value={formData.category || ''}\n              onChange={onInputChange}\n              error={errors.category}\n              required\n              disabled={disabled}\n              options={categoryOptions}\n            />\n\n            <FormField\n              label=\"Status\"\n              name=\"status\"\n              type=\"select\"\n              value={formData.status || 'active'}\n              onChange={onInputChange}\n              error={errors.status}\n              required\n              disabled={disabled}\n              options={statusOptions}\n            />\n\n            <FormField\n              label=\"Price\"\n              name=\"price\"\n              type=\"number\"\n              value={formData.price || 0}\n              onChange={onInputChange}\n              error={errors.price}\n              required\n              disabled={disabled}\n              placeholder=\"0.00\"\n            />\n\n            <FormField\n              label=\"Stock Quantity\"\n              name=\"stock\"\n              type=\"number\"\n              value={formData.stock || 0}\n              onChange={onInputChange}\n              error={errors.stock}\n              required\n              disabled={disabled}\n              placeholder=\"0\"\n            />\n\n            <FormField\n              label=\"Minimum Stock Level\"\n              name=\"minimumStock\"\n              type=\"number\"\n              value={formData.minimumStock || 0}\n              onChange={onInputChange}\n              error={errors.minimumStock}\n              required\n              disabled={disabled}\n              placeholder=\"0\"\n            />\n          </div>\n\n          <FormField\n            label=\"Description\"\n            name=\"description\"\n            type=\"textarea\"\n            value={formData.description || ''}\n            onChange={onInputChange}\n            error={errors.description}\n            disabled={disabled}\n            placeholder=\"Enter product description...\"\n          />\n        </div>\n      </Card>\n\n      {/* Product Images */}\n      <Card>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            <PhotoIcon className=\"h-5 w-5 text-gray-400\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Product Images</h3>\n          </div>\n          \n          <MultipleImageUpload\n            label=\"Product Images\"\n            name=\"images\"\n            value={formData.images || []}\n            onChange={onImagesChange}\n            error={errors.images}\n            disabled={disabled}\n            maxFiles={10}\n            maxSize={5 * 1024 * 1024} // 5MB\n            allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\n          />\n        </div>\n      </Card>\n\n      {/* Product Attributes */}\n      <Card>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            <TagIcon className=\"h-5 w-5 text-gray-400\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Product Attributes</h3>\n          </div>\n          \n          <DynamicArrayField\n            label=\"Attributes\"\n            value={formData.attributes || []}\n            onChange={onAttributesChange}\n            fieldConfigs={attributeFieldConfigs}\n            createEmpty={createEmptyAttribute}\n            error={errors.attributes || undefined}\n            disabled={disabled}\n            maxItems={20}\n            itemLabel={(item: ProductAttribute) => item.name || 'New Attribute'}\n          />\n        </div>\n      </Card>\n\n      {/* Product Variants */}\n      <Card>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            <ClipboardDocumentListIcon className=\"h-5 w-5 text-gray-400\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Product Variants</h3>\n          </div>\n          \n          <DynamicArrayField\n            label=\"Variants\"\n            value={formData.variants || []}\n            onChange={onVariantsChange}\n            fieldConfigs={variantFieldConfigs}\n            createEmpty={createEmptyVariant}\n            error={errors.variants || undefined}\n            disabled={disabled}\n            maxItems={50}\n            itemLabel={(item: ProductVariant) => item.name || 'New Variant'}\n          />\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default ProductEditForm;\n", "/**\n * Product Details Page\n *\n * This page displays comprehensive product information including images,\n * details, attributes, and variants.\n */\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport PageHeader from '../components/layout/PageHeader';\nimport Card from '../components/common/Card';\nimport StatusBadge from '../components/common/StatusBadge';\nimport Button from '../components/common/Button';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\n\nimport ImageGallery from '../components/common/ImageGallery';\nimport ProductEditForm from '../components/common/ProductEditForm';\nimport useNotification from '../hooks/useNotification';\n\nimport { useSuppliers } from '../features/suppliers/hooks/useSuppliers';\nimport { formatCurrency } from '../utils/formatters';\nimport { validateForm, validationRules } from '../utils/validation';\nimport { ROUTES } from '../constants/routes';\nimport {\n  ArrowLeftIcon,\n  PencilIcon,\n  CubeIcon,\n  TagIcon,\n  ClipboardDocumentListIcon,\n  CheckIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport type { SupplierProduct, ProductAttribute, ProductVariant } from '../features/suppliers/types';\n\n// Form data type that allows File objects for images\ninterface ProductFormData extends Omit<SupplierProduct, 'images'> {\n  images: (File | string)[];\n}\n\nconst ProductDetailsPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { showError, showInfo, showSuccess } = useNotification();\n  const { updateProduct, uploadProductImages } = useSuppliers();\n\n  // Product state\n  const [product, setProduct] = useState<SupplierProduct | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Edit mode state\n  const [isEditing, setIsEditing] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [formData, setFormData] = useState<Partial<ProductFormData>>({});\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  // Use refs to store stable references to notification functions\n  const showErrorRef = useRef(showError);\n  const showInfoRef = useRef(showInfo);\n  const showSuccessRef = useRef(showSuccess);\n  const navigateRef = useRef(navigate);\n\n  // Update refs when functions change\n  useEffect(() => {\n    showErrorRef.current = showError;\n    showInfoRef.current = showInfo;\n    showSuccessRef.current = showSuccess;\n    navigateRef.current = navigate;\n  }, [showError, showInfo, showSuccess, navigate]);\n\n  // Initialize form data when product loads\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        sku: product.sku,\n        category: product.category,\n        price: product.price,\n        stock: product.stock,\n        minimumStock: product.minimumStock,\n        status: product.status,\n        description: product.description || '',\n        images: product.images || [],\n        attributes: product.attributes || [],\n        variants: product.variants || []\n      });\n    }\n  }, [product]);\n\n  // Form handling functions\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const processedValue = type === 'number' ? parseFloat(value) || 0 : value;\n\n    setFormData(prev => ({\n      ...prev,\n      [name]: processedValue\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleImagesChange = (images: (File | string)[]) => {\n    setFormData(prev => ({\n      ...prev,\n      images\n    }));\n  };\n\n  const handleAttributesChange = (attributes: ProductAttribute[]) => {\n    setFormData(prev => ({\n      ...prev,\n      attributes\n    }));\n  };\n\n  const handleVariantsChange = (variants: ProductVariant[]) => {\n    setFormData(prev => ({\n      ...prev,\n      variants\n    }));\n  };\n\n  const validateFormData = () => {\n    const validationErrors = validateForm(formData, {\n      name: [validationRules.required('Product name is required')],\n      sku: [validationRules.required('SKU is required'), validationRules.sku()],\n      category: [validationRules.required('Category is required')],\n      price: [validationRules.required('Price is required'), validationRules.price()],\n      stock: [validationRules.required('Stock is required'), validationRules.stock()],\n      minimumStock: [\n        validationRules.required('Minimum stock is required'),\n        validationRules.minimumStock(),\n        validationRules.stockConsistency()\n      ]\n    });\n\n    setErrors(validationErrors);\n    return Object.keys(validationErrors).length === 0;\n  };\n\n  const handleEdit = () => {\n    setIsEditing(true);\n  };\n\n  const handleCancel = () => {\n    setIsEditing(false);\n    setErrors({});\n    // Reset form data to original product data\n    if (product) {\n      setFormData({\n        name: product.name,\n        sku: product.sku,\n        category: product.category,\n        price: product.price,\n        stock: product.stock,\n        minimumStock: product.minimumStock,\n        status: product.status,\n        description: product.description || '',\n        images: product.images || [],\n        attributes: product.attributes || [],\n        variants: product.variants || []\n      });\n    }\n  };\n\n  const handleSave = async () => {\n    if (!validateFormData() || !product || !id) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Separate files from URLs in images\n      const imageFiles = formData.images?.filter((img): img is File => img instanceof File) || [];\n      const imageUrls = formData.images?.filter((img): img is string => typeof img === 'string') || [];\n\n      // Upload new images if any\n      let uploadedImageUrls: string[] = [];\n      if (imageFiles.length > 0) {\n        const uploadResult = await uploadProductImages(id, imageFiles);\n        uploadedImageUrls = uploadResult.imageUrls;\n      }\n\n      // Combine existing URLs with newly uploaded URLs\n      const allImageUrls = [...imageUrls, ...uploadedImageUrls];\n\n      // Prepare update data\n      const updateData: Partial<SupplierProduct> = {};\n      if (formData.name) updateData.name = formData.name;\n      if (formData.sku) updateData.sku = formData.sku;\n      if (formData.category) updateData.category = formData.category;\n      if (formData.price !== undefined) updateData.price = formData.price;\n      if (formData.stock !== undefined) updateData.stock = formData.stock;\n      if (formData.minimumStock !== undefined) updateData.minimumStock = formData.minimumStock;\n      if (formData.status) updateData.status = formData.status;\n      if (formData.description !== undefined) updateData.description = formData.description;\n      if (allImageUrls.length > 0) updateData.images = allImageUrls;\n      if (formData.attributes) updateData.attributes = formData.attributes;\n      if (formData.variants) updateData.variants = formData.variants;\n\n      // Update product\n      const updatedProduct = await updateProduct(id, updateData);\n      setProduct(updatedProduct);\n      setIsEditing(false);\n      showSuccessRef.current('Product updated successfully');\n\n    } catch (error) {\n      console.error('Error updating product:', error);\n      showErrorRef.current('Failed to update product');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  useEffect(() => {\n    if (!id) {\n      showErrorRef.current('No product ID provided');\n      navigateRef.current(ROUTES.SUPPLIERS);\n      return;\n    }\n\n    // Mock data for development - replace with actual API call\n    const fetchProduct = async () => {\n      try {\n        setIsLoading(true);\n\n        // Simulate API delay\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // Mock product data with extended fields\n        const mockProduct: SupplierProduct = {\n          id: id,\n          name: 'Wireless Bluetooth Headphones Pro',\n          sku: 'WBH-PRO-001',\n          category: 'Electronics',\n          price: 199.99,\n          stock: 85,\n          minimumStock: 10,\n          status: 'active',\n          description: 'Premium wireless headphones with active noise cancellation, 30-hour battery life, and superior sound quality. Perfect for music lovers and professionals who demand the best audio experience.',\n          image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',\n          images: [\n            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',\n            'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&fit=crop',\n            'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop',\n            'https://images.unsplash.com/photo-1572536147248-ac59a8abfa4b?w=400&h=400&fit=crop'\n          ],\n          attributes: [\n            { id: '1', name: 'Brand', value: 'AudioTech', type: 'text' },\n            { id: '2', name: 'Weight', value: '250', type: 'number', unit: 'grams' },\n            { id: '3', name: 'Battery Life', value: '30', type: 'number', unit: 'hours' },\n            { id: '4', name: 'Wireless', value: 'true', type: 'boolean' },\n            { id: '5', name: 'Noise Cancellation', value: 'Active', type: 'select' },\n            { id: '6', name: 'Frequency Response', value: '20Hz - 20kHz', type: 'text' }\n          ],\n          variants: [\n            {\n              id: '1',\n              name: 'Black',\n              sku: 'WBH-PRO-001-BLK',\n              price: 199.99,\n              stock: 45,\n              attributes: { color: 'Black', size: 'Standard' },\n              image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=150&h=150&fit=crop'\n            },\n            {\n              id: '2',\n              name: 'White',\n              sku: 'WBH-PRO-001-WHT',\n              price: 199.99,\n              stock: 25,\n              attributes: { color: 'White', size: 'Standard' },\n              image: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=150&h=150&fit=crop'\n            },\n            {\n              id: '3',\n              name: 'Silver',\n              sku: 'WBH-PRO-001-SLV',\n              price: 219.99,\n              stock: 15,\n              attributes: { color: 'Silver', size: 'Standard' },\n              image: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=150&h=150&fit=crop'\n            }\n          ],\n          createdAt: '2024-01-15T10:30:00Z',\n          updatedAt: '2024-01-20T14:45:00Z'\n        };\n\n        setProduct(mockProduct);\n      } catch (error) {\n        console.error('Error fetching product:', error);\n        showErrorRef.current('Failed to load product details');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchProduct();\n  }, [id]);\n\n  // Helper functions for dynamic arrays\n  const createEmptyAttribute = (): ProductAttribute => ({\n    id: `attr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,\n    name: '',\n    value: '',\n    type: 'text'\n  });\n\n  const createEmptyVariant = (): ProductVariant => ({\n    id: `var_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,\n    name: '',\n    sku: '',\n    price: 0,\n    stock: 0,\n    attributes: {}\n  });\n\n  // Attribute field configurations\n  const attributeFieldConfigs = [\n    { name: 'name', label: 'Attribute Name', type: 'text' as const, required: true, placeholder: 'e.g., Brand, Weight, Color' },\n    { name: 'value', label: 'Value', type: 'text' as const, required: true, placeholder: 'e.g., AudioTech, 250g, Black' },\n    { name: 'type', label: 'Type', type: 'select' as const, required: true, options: [\n      { value: 'text', label: 'Text' },\n      { value: 'number', label: 'Number' },\n      { value: 'boolean', label: 'Boolean' },\n      { value: 'select', label: 'Select' }\n    ]},\n    { name: 'unit', label: 'Unit (Optional)', type: 'text' as const, placeholder: 'e.g., grams, hours, cm' }\n  ];\n\n  // Variant field configurations\n  const variantFieldConfigs = [\n    { name: 'name', label: 'Variant Name', type: 'text' as const, required: true, placeholder: 'e.g., Black, Large, Premium' },\n    { name: 'sku', label: 'SKU', type: 'text' as const, required: true, placeholder: 'e.g., WBH-PRO-001-BLK' },\n    { name: 'price', label: 'Price', type: 'number' as const, required: true, placeholder: '0.00' },\n    { name: 'stock', label: 'Stock', type: 'number' as const, required: true, placeholder: '0' }\n  ];\n\n  const getStatusBadgeStatus = (status: string): string => {\n    switch (status) {\n      case 'active':\n        return 'active';\n      case 'inactive':\n        return 'pending';\n      case 'out_of_stock':\n        return 'rejected';\n      default:\n        return 'pending';\n    }\n  };\n\n  const getStockStatus = (stock: number, minimumStock: number, status: string) => {\n    if (status === 'out_of_stock' || stock === 0) {\n      return { text: 'Out of Stock', color: 'text-red-600' };\n    } else if (stock <= minimumStock) {\n      return { text: 'Low Stock', color: 'text-yellow-600' };\n    } else {\n      return { text: 'In Stock', color: 'text-green-600' };\n    }\n  };\n\n\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-screen\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  if (!product) {\n    return (\n      <div className=\"text-center py-12\">\n        <CubeIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Product not found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          The product you're looking for doesn't exist or has been removed.\n        </p>\n        <div className=\"mt-6\">\n          <Button\n            variant=\"primary\"\n            onClick={() => navigateRef.current(ROUTES.SUPPLIERS)}\n            icon={<ArrowLeftIcon className=\"w-4 h-4\" />}\n          >\n            Back to Suppliers\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  const stockStatus = getStockStatus(product.stock, product.minimumStock, product.status);\n\n  return (\n    <div className=\"space-y-6\">\n      <PageHeader\n        title={product.name}\n        description=\"Complete product information and details\"\n        breadcrumbs={[\n          { label: 'Suppliers', path: ROUTES.SUPPLIERS },\n          { label: product.name }\n        ]}\n        actions={\n          <div className=\"flex space-x-3\">\n            {isEditing ? (\n              <>\n                <Button\n                  onClick={handleSave}\n                  icon={<CheckIcon className=\"w-4 h-4\" />}\n                  variant=\"primary\"\n                  loading={isSaving}\n                  disabled={isSaving}\n                >\n                  Save Changes\n                </Button>\n                <Button\n                  onClick={handleCancel}\n                  icon={<XMarkIcon className=\"w-4 h-4\" />}\n                  variant=\"outline\"\n                  disabled={isSaving}\n                >\n                  Cancel\n                </Button>\n              </>\n            ) : (\n              <>\n                <Button\n                  onClick={handleEdit}\n                  icon={<PencilIcon className=\"w-4 h-4\" />}\n                  variant=\"primary\"\n                >\n                  Edit Product\n                </Button>\n                <Button\n                  onClick={() => navigateRef.current(-1)}\n                  icon={<ArrowLeftIcon className=\"w-4 h-4\" />}\n                  variant=\"outline\"\n                >\n                  Go Back\n                </Button>\n              </>\n            )}\n          </div>\n        }\n      />\n\n      {isEditing ? (\n        /* Edit Form */\n        <ProductEditForm\n          formData={formData}\n          errors={errors}\n          onInputChange={handleInputChange}\n          onImagesChange={handleImagesChange}\n          onAttributesChange={handleAttributesChange}\n          onVariantsChange={handleVariantsChange}\n          createEmptyAttribute={createEmptyAttribute}\n          createEmptyVariant={createEmptyVariant}\n          attributeFieldConfigs={attributeFieldConfigs}\n          variantFieldConfigs={variantFieldConfigs}\n          disabled={isSaving}\n        />\n      ) : (\n        /* View Mode */\n        <>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Product Images */}\n            <Card>\n              <ImageGallery\n                images={product.images || (product.image ? [product.image] : [])}\n                productName={product.name}\n                className=\"h-96\"\n              />\n            </Card>\n\n            {/* Product Information */}\n            <Card>\n              <div className=\"space-y-6\">\n                {/* Product Name */}\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900\">{product.name}</h1>\n                  <p className=\"text-sm text-gray-500 mt-1\">SKU: {product.sku}</p>\n                </div>\n\n                {/* Product Price */}\n                <div>\n                  <span className=\"text-3xl font-bold text-gray-900\">{formatCurrency(product.price)}</span>\n                </div>\n\n                {/* Product Description */}\n                {product.description && (\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Description</h3>\n                    <p className=\"text-gray-700\">{product.description}</p>\n                  </div>\n                )}\n\n                {/* Stock Information */}\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-500\">Minimum Stock Level</h4>\n                    <p className=\"text-lg font-semibold text-gray-900\">{product.minimumStock}</p>\n                  </div>\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-500\">In Stock Now</h4>\n                    <div>\n                      <p className=\"text-lg font-semibold text-gray-900\">{product.stock}</p>\n                      <p className={`text-sm ${stockStatus.color}`}>{stockStatus.text}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Status and Category */}\n                <div className=\"flex items-center space-x-3\">\n                  <StatusBadge status={getStatusBadgeStatus(product.status)} type=\"supplier\" />\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                    {product.category}\n                  </span>\n                </div>\n              </div>\n            </Card>\n          </div>\n        </>\n      )}\n\n      {/* Product Attributes Section - Only show in view mode */}\n      {!isEditing && product.attributes && product.attributes.length > 0 && (\n        <Card>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <TagIcon className=\"h-5 w-5 text-gray-400\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">Product Attributes</h2>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {product.attributes.map((attribute) => (\n                <div key={attribute.id} className=\"bg-gray-50 rounded-lg p-4\">\n                  <dt className=\"text-sm font-medium text-gray-500\">{attribute.name}</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">\n                    {attribute.type === 'boolean'\n                      ? (attribute.value === 'true' ? 'Yes' : 'No')\n                      : `${attribute.value}${attribute.unit ? ` ${attribute.unit}` : ''}`\n                    }\n                  </dd>\n                </div>\n              ))}\n            </div>\n          </div>\n        </Card>\n      )}\n\n      {/* Product Variants Section - Only show in view mode */}\n      {!isEditing && product.variants && product.variants.length > 0 && (\n        <Card>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <ClipboardDocumentListIcon className=\"h-5 w-5 text-gray-400\" />\n              <h2 className=\"text-lg font-medium text-gray-900\">Product Variants</h2>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {product.variants.map((variant) => (\n                <div key={variant.id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                  <div className=\"flex items-start space-x-3\">\n                    {variant.image ? (\n                      <img\n                        src={variant.image}\n                        alt={variant.name}\n                        className=\"h-16 w-16 rounded-lg object-cover\"\n                      />\n                    ) : (\n                      <div className=\"h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center\">\n                        <CubeIcon className=\"h-6 w-6 text-gray-400\" />\n                      </div>\n                    )}\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-sm font-medium text-gray-900\">{variant.name}</h3>\n                      <p className=\"text-xs text-gray-500 font-mono\">{variant.sku}</p>\n                      <p className=\"text-sm font-semibold text-gray-900 mt-1\">\n                        {formatCurrency(variant.price)}\n                      </p>\n                      <p className=\"text-xs text-gray-600\">Stock: {variant.stock}</p>\n\n                      {/* Variant Attributes */}\n                      <div className=\"mt-2 space-y-1\">\n                        {Object.entries(variant.attributes).map(([key, value]) => (\n                          <div key={key} className=\"flex justify-between text-xs\">\n                            <span className=\"text-gray-500 capitalize\">{key}:</span>\n                            <span className=\"text-gray-900\">{value}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ProductDetailsPage;\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "import * as React from \"react\";\nfunction ArrowLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowLeftIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  name: string;\r\n  type?: string;\r\n  value: any;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  className?: string;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  name,\r\n  type = 'text',\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  placeholder = '',\r\n  options = [],\r\n  className = '',\r\n  disabled = false,\r\n  loading = false\r\n}) => {\r\n  const inputClasses = `mt-1 block w-full rounded-md shadow-sm sm:text-sm ${\r\n    error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary focus:ring-primary'\r\n  }`;\r\n  \r\n  const renderField = () => {\r\n    switch (type) {\r\n      case 'textarea':\r\n        return (\r\n          <textarea\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      case 'select':\r\n        return (\r\n          <select\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            disabled={disabled || loading}\r\n          >\r\n            {loading ? (\r\n              <option value=\"\">Loading...</option>\r\n            ) : (\r\n              options.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </select>\r\n        );\r\n      \r\n      case 'checkbox':\r\n        return (\r\n          <input\r\n            type=\"checkbox\"\r\n            id={name}\r\n            name={name}\r\n            checked={value}\r\n            onChange={onChange}\r\n            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      default:\r\n        return (\r\n          <input\r\n            type={type}\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div className={`${className}`}>\r\n      <label htmlFor={name} className=\"block text-sm font-medium text-gray-700\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      {renderField()}\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "import * as React from \"react\";\nfunction CubeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CubeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 12.75 6 6 9-13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;"], "names": ["PhotoIcon", "_ref", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "images", "productName", "className", "currentImageIndex", "setCurrentImageIndex", "useState", "hasImages", "length", "hasMultipleImages", "_jsxs", "children", "_jsx", "src", "alt", "_Fragment", "onClick", "goToPrevious", "prevIndex", "ChevronLeftIcon", "goToNext", "ChevronRightIcon", "map", "image", "index", "goToImage", "ArrowUpIcon", "ArrowDownIcon", "label", "name", "value", "onChange", "error", "required", "disabled", "maxSize", "allowedTypes", "maxFiles", "isDragOver", "setIsDragOver", "fileInputRef", "useRef", "handleFileSelect", "useCallback", "files", "validFiles", "currentCount", "i", "file", "validation", "validateFile", "valid", "push", "console", "handleReorder", "fromIndex", "toIndex", "newFiles", "movedFile", "splice", "getPreviewUrl", "URL", "createObjectURL", "type", "filter", "_", "handleRemove", "XMarkIcon", "onDragOver", "e", "preventDefault", "onDragLeave", "onDrop", "dataTransfer", "handleClick", "current", "click", "accept", "join", "target", "multiple", "PlusIcon", "Math", "round", "fieldConfigs", "createEmpty", "maxItems", "itemLabel", "getDefaultItemLabel", "item", "nameField", "<PERSON><PERSON>", "variant", "size", "handleAdd", "icon", "slice", "newValue", "config", "fieldProps", "key", "fieldValue", "parseFloat", "handleItemChange", "field", "placeholder", "options", "FormField", "toLowerCase", "ClipboardDocumentListIcon", "formData", "errors", "onInputChange", "onImagesChange", "onAttributesChange", "onVariantsChange", "createEmptyAttribute", "createEmptyVariant", "attributeFieldConfigs", "variantFieldConfigs", "Card", "sku", "category", "status", "price", "stock", "minimumStock", "description", "MultipleImageUpload", "TagIcon", "DynamicArrayField", "attributes", "undefined", "variants", "ProductDetailsPage", "useParams", "navigate", "useNavigate", "showError", "showInfo", "showSuccess", "useNotification", "updateProduct", "uploadProductImages", "useSuppliers", "product", "setProduct", "isLoading", "setIsLoading", "isEditing", "setIsEditing", "isSaving", "setIsSaving", "setFormData", "setErrors", "showErrorRef", "showInfoRef", "showSuccessRef", "navigateRef", "useEffect", "ROUTES", "SUPPLIERS", "async", "Promise", "resolve", "setTimeout", "unit", "color", "createdAt", "updatedAt", "fetchProduct", "LoadingSpinner", "CubeIcon", "ArrowLeftIcon", "stockStatus", "text", "getStockStatus", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbs", "path", "actions", "validateFormData", "validationErrors", "validateForm", "validationRules", "stockConsistency", "keys", "_formData$images", "_formData$images2", "imageFiles", "img", "File", "imageUrls", "uploadedImageUrls", "allImageUrls", "updateData", "updatedProduct", "CheckIcon", "loading", "handleCancel", "handleEdit", "PencilIcon", "ProductEditForm", "processedValue", "prev", "Date", "now", "random", "toString", "substring", "ImageGallery", "formatCurrency", "StatusBadge", "getStatusBadgeStatus", "attribute", "entries", "testId", "Link", "to", "HomeIcon", "memo", "subtitle", "bodyClassName", "headerClassName", "footerClassName", "footer", "hoverable", "noPadding", "bordered", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "formatDate", "dateString", "arguments", "date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "formatFileSize", "bytes", "floor", "log", "pow", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "borderTopColor", "borderRightColor", "backgroundColor", "_type", "statusKey", "colorClass", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "char<PERSON>t", "toUpperCase", "inputClasses", "htmlFor", "renderField", "option", "checked", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "isRequired", "trim", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "values", "for<PERSON>ach", "fieldName", "rules", "validateField", "_name", "ruleArray", "rule", "validator", "message", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "Number", "isInteger", "arrayNotEmpty", "imageArray", "iconPosition", "fullWidth", "rounded", "href", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "link", "xs", "xl", "content", "cx", "cy", "r"], "sourceRoot": ""}