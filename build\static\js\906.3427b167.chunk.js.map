{"version": 3, "file": "static/js/906.3427b167.chunk.js", "mappings": "gJACA,SAASA,EAAiBC,EAIvBC,GAAQ,IAJgB,MACzBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,iHAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBN,E,gDCvBlD,SAASmB,EAASlB,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBa,E,gDCvBlD,SAASC,EAASnB,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBc,E,gDCvBlD,SAASC,EAAWpB,EAIjBC,GAAQ,IAJU,MACnBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBe,E,oJCHlD,MAAMC,EAAuBA,KAC3B,MAAMC,GAAWC,EAAAA,EAAAA,OAGX,OAAEC,EAAM,UAAEC,IAAcC,EAAAA,EAAAA,MAEvBC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAoE,QACrGC,EAAaC,IAAkBF,EAAAA,EAAAA,WAAS,GAGzCG,GAAiBC,EAAAA,EAAAA,UAAQ,IACR,QAAjBN,EAA+BH,EAC5BA,EAAOU,QAAOC,GAASA,EAAMC,SAAWT,KAC9C,CAACH,EAAQG,IAGNU,GAAmBC,EAAAA,EAAAA,cAAaH,IACpCb,EAASiB,EAAAA,EAAOC,qBAAqBL,EAAMrB,IAAI,GAC9C,CAACQ,IAEEmB,GAAqBH,EAAAA,EAAAA,cAAY,KACrCP,GAAe,GAEfW,YAAW,KACTX,GAAe,GACfY,QAAQC,IAAI,sBAAsB,GACjC,KAAK,GACP,IAEH,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CACT/C,MAAM,SACNgD,YAAY,4CACZC,SACEH,EAAAA,EAAAA,KAACI,EAAAA,EAAM,CACLC,QAAQ,UACRC,MAAMN,EAAAA,EAAAA,KAACjD,EAAAA,EAAiB,CAAC+C,UAAU,YACnCS,QAASd,EACTe,QAAS1B,EAAYiB,SACtB,qBAMLF,EAAAA,EAAAA,MAACY,EAAAA,EAAI,CAAAV,SAAA,EACHC,EAAAA,EAAAA,KAACU,EAAAA,GAAW,CACV/B,aAAcA,EACdgC,eAAgB/B,IAGjBH,GACCuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACvCC,EAAAA,EAAAA,KAACY,EAAAA,EAAc,OAGjBZ,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CACRrC,OAAQQ,EACR8B,aAAczB,EACdnC,MAAO,GAAGyB,EAAaoC,OAAO,GAAGC,cAAgBrC,EAAasC,MAAM,cAAcjC,EAAekC,iBAInG,EAIV,GAAeC,EAAAA,EAAAA,MAAK9C,E", "sources": ["../node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "pages/OrdersPage.tsx"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownTrayIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "/**\r\n * Orders Page\r\n *\r\n * This page displays and manages orders in the system.\r\n */\r\n\r\nimport React, { useState, useMemo, useCallback, memo } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport Card from '../components/common/Card';\r\nimport Button from '../components/common/Button';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport { ArrowDownTrayIcon } from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../constants/routes';\r\nimport {\r\n  OrderList,\r\n  OrderFilter,\r\n  Order,\r\n  useOrders\r\n} from '../features/orders/index';\r\n\r\nconst OrdersPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n\r\n  // Use the useOrders hook for API integration\r\n  const { orders, isLoading } = useOrders();\r\n\r\n  const [activeFilter, setActiveFilter] = useState<'all' | 'pending' | 'approved' | 'completed' | 'rejected'>('all');\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Memoize filtered orders to prevent unnecessary recalculations\r\n  const filteredOrders = useMemo(() => {\r\n    if (activeFilter === 'all') return orders;\r\n    return orders.filter(order => order.status === activeFilter);\r\n  }, [orders, activeFilter]);\r\n\r\n  // Memoize event handlers to prevent unnecessary re-renders\r\n  const handleOrderClick = useCallback((order: Order) => {\r\n    navigate(ROUTES.getOrderDetailsRoute(order.id));\r\n  }, [navigate]);\r\n\r\n  const handleExportOrders = useCallback(() => {\r\n    setIsExporting(true);\r\n    // Simulate export process\r\n    setTimeout(() => {\r\n      setIsExporting(false);\r\n      console.log('Exporting orders...');\r\n    }, 1500);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title=\"Orders\"\r\n        description=\"Manage and track all orders in the system\"\r\n        actions={\r\n          <Button\r\n            variant=\"outline\"\r\n            icon={<ArrowDownTrayIcon className=\"h-5 w-5\" />}\r\n            onClick={handleExportOrders}\r\n            loading={isExporting}\r\n          >\r\n            Export Orders\r\n          </Button>\r\n        }\r\n      />\r\n\r\n      <Card>\r\n        <OrderFilter\r\n          activeFilter={activeFilter}\r\n          onFilterChange={setActiveFilter}\r\n        />\r\n\r\n        {isLoading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner />\r\n          </div>\r\n        ) : (\r\n          <OrderList\r\n            orders={filteredOrders}\r\n            onOrderClick={handleOrderClick}\r\n            title={`${activeFilter.charAt(0).toUpperCase() + activeFilter.slice(1)} Orders (${filteredOrders.length})`}\r\n          />\r\n        )}\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(OrdersPage);"], "names": ["ArrowDownTrayIcon", "_ref", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "TruckIcon", "ClockIcon", "XCircleIcon", "OrdersPage", "navigate", "useNavigate", "orders", "isLoading", "useOrders", "activeFilter", "setActiveFilter", "useState", "isExporting", "setIsExporting", "filteredOrders", "useMemo", "filter", "order", "status", "handleOrderClick", "useCallback", "ROUTES", "getOrderDetailsRoute", "handleExportOrders", "setTimeout", "console", "log", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "description", "actions", "<PERSON><PERSON>", "variant", "icon", "onClick", "loading", "Card", "OrderFilter", "onFilterChange", "LoadingSpinner", "OrderList", "onOrderClick", "char<PERSON>t", "toUpperCase", "slice", "length", "memo"], "sourceRoot": ""}