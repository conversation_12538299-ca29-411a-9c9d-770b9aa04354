{"version": 3, "file": "static/js/188.3688804d.chunk.js", "mappings": "uJAgBA,MAmCA,EAnCkCA,IAK3B,IAL4B,KACjCC,EAAI,UACJC,EAAS,SACTC,EAAQ,UACRC,EAAY,IACbJ,EACC,OACEK,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4BAA4BA,IAAYE,UACtDD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBE,SACnCL,EAAKM,KAAKC,IACT,MAAMC,EAAWD,EAAIE,KAAOR,EAE5B,OACEG,EAAAA,EAAAA,KAAA,UAEEM,QAASA,KAAOH,EAAII,UAAYT,EAASK,EAAIE,IAC7CN,UAAW,iNAGPK,EACE,8BACA,iGACFD,EAAII,SAAW,gCAAkC,mCAErDA,SAAUJ,EAAII,SAASN,SAEtBE,EAAIK,OAZAL,EAAIE,GAaF,OAIX,C,uFCtBV,MAAMI,EAAwCd,IAOvC,IAPwC,MAC7Ce,EAAK,YACLC,EAAW,QACXC,EAAO,YACPC,EAAW,UACXd,EAAY,GAAE,OACde,GACDnB,EACC,OACEoB,EAAAA,EAAAA,MAAA,OACEhB,UAAW,QAAQA,IACnB,cAAae,EAAOb,SAAA,CAGnBY,GAAeA,EAAYG,OAAS,IACnChB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAY,aAAW,aAAYE,UAChDc,EAAAA,EAAAA,MAAA,MAAIhB,UAAU,oDAAmDE,SAAA,EAC/DD,EAAAA,EAAAA,KAAA,MAAAC,UACED,EAAAA,EAAAA,KAACiB,EAAAA,GAAI,CACHC,GAAG,IACHnB,UAAU,uCACV,aAAW,OAAME,UAEjBD,EAAAA,EAAAA,KAACmB,EAAAA,EAAQ,CAACpB,UAAU,gBAIvBc,EAAYX,KAAI,CAACkB,EAAMC,KACtBN,EAAAA,EAAAA,MAAA,MAAgBhB,UAAU,oBAAmBE,SAAA,EAC3CD,EAAAA,EAAAA,KAACsB,EAAAA,EAAgB,CAACvB,UAAU,+BAC3BqB,EAAKG,MAAQF,EAAQR,EAAYG,OAAS,GACzChB,EAAAA,EAAAA,KAACiB,EAAAA,GAAI,CACHC,GAAIE,EAAKG,KACTxB,UAAU,qBAAoBE,SAE7BmB,EAAKZ,SAGRR,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BE,SAAEmB,EAAKZ,UAV7Ca,WAmBjBN,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8EAA6EE,SAAA,EAC1Fc,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,mCAAkCE,SAAES,IACjDC,GAAsC,kBAAhBA,GACrBX,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAEU,IAE3CA,KAIHC,IACCZ,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCE,SAC/CW,SAIH,EAIV,GAAeY,EAAAA,EAAAA,MAAKf,E,yDCjEpB,MAAMgB,EAA4B9B,IAgB3B,IAhB4B,MACjCe,EAAK,SACLgB,EAAQ,SACRzB,EAAQ,UACRF,EAAY,GAAE,cACd4B,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBC,EAAI,OACJC,EAAM,QACNzB,EAAO,UACP0B,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACfrB,GACDnB,EAEC,MAAMyC,EAAc,6BACIF,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrF1B,EAAU,iBAAmB,WAC7BP,QAIEsC,EAAgB,mFAElBT,QAIEU,EAAc,SAChBL,EAAY,GAAK,cACjBN,QAIEY,EAAgB,4DAElBV,QAIJ,OAAIM,GAEApB,EAAAA,EAAAA,MAAA,OAAKhB,UAAWqC,EAAa,cAAatB,EAAOb,SAAA,EAC7CS,GAASgB,GAAYI,KACrBf,EAAAA,EAAAA,MAAA,OAAKhB,UAAWsC,EAAcpC,SAAA,EAC5Bc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,SAAQE,SAAA,CACpBS,IAASV,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gDACxB2B,IAAY1B,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wDAE7B+B,IAAQ9B,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uDAI5BC,EAAAA,EAAAA,KAAA,OAAKD,UAAWuC,EAAYrC,UAC1BD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,6CAGhBgC,IACC/B,EAAAA,EAAAA,KAAA,OAAKD,UAAWwC,EAActC,UAC5BD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,sDAQvBgB,EAAAA,EAAAA,MAAA,OACEhB,UAAWqC,EACX9B,QAASA,EACT,cAAaQ,EAAOb,SAAA,EAElBS,GAASgB,GAAYI,KACrBf,EAAAA,EAAAA,MAAA,OAAKhB,UAAWsC,EAAcpC,SAAA,EAC5Bc,EAAAA,EAAAA,MAAA,OAAAd,SAAA,CACoB,kBAAVS,GACNV,EAAAA,EAAAA,KAAA,MAAID,UAAU,qCAAoCE,SAAES,IAEpDA,EAEmB,kBAAbgB,GACN1B,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAEyB,IAE3CA,KAGHI,IAAQ9B,EAAAA,EAAAA,KAAA,OAAKD,UAAU,eAAcE,SAAE6B,QAI5C9B,EAAAA,EAAAA,KAAA,OAAKD,UAAWuC,EAAYrC,SAAEA,IAE7B8B,IACC/B,EAAAA,EAAAA,KAAA,OAAKD,UAAWwC,EAActC,SAC3B8B,MAGD,EAIV,GAAeP,EAAAA,EAAAA,MAAKC,E,uDCxHpB,MAsGA,EAtGsD9B,IAM/C,IANgD,KACrD6C,EAAO,KAAI,UACXzC,EAAY,GAAE,QACd0C,EAAU,UAAS,MACnBC,EAAQ,UAAS,gBACjBC,GAAkB,GACnBhD,EACC,MAAMiD,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiBD,EAGxD,MAAgB,YAAZD,GAEA1B,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CsD,KAAK,SACL,aAAW,UAASpD,SAAA,EAEpBD,EAAAA,EAAAA,KAAA,OACED,UAAW,wDAAwD6C,EAAQJ,GAAMM,UACjFQ,MAAO,CACLC,eAAgBH,EAChBI,iBAAkBJ,MAGtBpD,EAAAA,EAAAA,KAAA,QAAMD,UAAU,UAASE,SAAC,kBAMhB,SAAZwC,GAEA1B,EAAAA,EAAAA,MAAA,OACEhB,UAAW,0DAA0DA,IACrEsD,KAAK,SACL,aAAW,UAASpD,SAAA,EAEpBD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5BpD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5BpD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5BpD,EAAAA,EAAAA,KAAA,QAAMD,UAAU,UAASE,SAAC,kBAMhB,UAAZwC,GAEA1B,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CsD,KAAK,SACL,aAAW,UAASpD,SAAA,EAEpBD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMQ,kCAC5BM,MAAO,CAAEG,gBAAiBL,MAE5BpD,EAAAA,EAAAA,KAAA,QAAMD,UAAU,UAASE,SAAC,kBAMhB,WAAZwC,GAEA1B,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CsD,KAAK,SACL,aAAW,UAASpD,SAAA,EAEpBD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMS,oCAC5BK,MAAO,CAAEZ,MAAOU,GAAenD,UAE/BD,EAAAA,EAAAA,KAAA,OACED,UAAW,GAAG6C,EAAQJ,GAAMQ,0CAC5BM,MAAO,CAAEG,gBAAiBL,QAG9BpD,EAAAA,EAAAA,KAAA,QAAMD,UAAU,UAASE,SAAC,kBAKzB,IAAI,C,mIC9Gb,SAASyD,EAAe/D,EAIrBgE,GAAQ,IAJc,MACvBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,uNAEP,CACA,MACA,EADiCX,EAAAA,WAAiBJ,G,iCCHlD,MA+CA,EA/CgD/D,IAGzC,IAH0C,UAC/CE,EAAS,YACT6E,GACD/E,EACC,MAwBMC,EAxBW,CACf,CACES,GAAI,UACJG,MAAO,UACPsB,MAAM9B,EAAAA,EAAAA,KAAC2E,EAAAA,EAAc,CAAC5E,UAAU,kBAElC,CACEM,GAAI,WACJG,MAAO,WACPsB,MAAM9B,EAAAA,EAAAA,KAAC0D,EAAe,CAAC3D,UAAU,kBAEnC,CACEM,GAAI,gBACJG,MAAO,gBACPsB,MAAM9B,EAAAA,EAAAA,KAAC4E,EAAAA,EAAQ,CAAC7E,UAAU,kBAE5B,CACEM,GAAI,WACJG,MAAO,WACPsB,MAAM9B,EAAAA,EAAAA,KAAC6E,EAAAA,EAAS,CAAC9E,UAAU,mBAKTG,KAAIC,IAAG,CAC3BE,GAAIF,EAAIE,GACRG,OACEO,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBE,SAAA,CAC/BE,EAAI2B,KACJ3B,EAAIK,aAKX,OACER,EAAAA,EAAAA,KAAC8E,EAAAA,EAAI,CACHlF,KAAMA,EACNC,UAAWA,EACXC,SAAWiF,GAAUL,EAAYK,IACjC,EC/DN,SAASC,EAAUrF,EAIhBgE,GAAQ,IAJS,MAClBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,iXACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,sFAEP,CACA,MACA,EADiCX,EAAAA,WAAiBkB,G,cCVlD,MA0HA,EA1HgDrF,IAKzC,IAL0C,QAC/CsF,EAAO,UACPC,EAAS,cACTC,EAAa,eACbC,GACDzF,EACC,MAAM0F,GAAeC,EAAAA,EAAAA,QAAyB,MAgB9C,OAAKL,GAWHlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,6EAA4EE,SAAA,EACzFc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,WAAUE,SAAA,EACvBD,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,IACAN,EAAQO,QAAUP,EAAQO,OAAOC,QAAU,CAAEC,IAAKT,EAAQO,QAC/DG,IAAKV,EAAQW,KACbA,KAAMX,EAAQW,KACdpD,KAAK,MACLzC,UAAU,cAEXmF,IACCnE,EAAAA,EAAAA,MAAA8E,EAAAA,SAAA,CAAA5F,SAAA,EACED,EAAAA,EAAAA,KAAA,UACEM,QAtCYwF,KACpBZ,GAAaG,EAAaU,SAC5BV,EAAaU,QAAQC,OACvB,EAoCYjG,UAAU,mFAAkFE,UAE5FD,EAAAA,EAAAA,KAACgF,EAAU,CAACjF,UAAU,6BAExBC,EAAAA,EAAAA,KAAA,SACEsE,IAAKe,EACLY,KAAK,OACLC,OAAO,UACPpG,SAzCYqG,IAA4C,IAADC,EACnE,MAAMC,EAAqB,QAAjBD,EAAGD,EAAEG,OAAOC,aAAK,IAAAH,OAAA,EAAdA,EAAiB,GAC1BC,GAAQjB,GACVA,EAAeiB,EACjB,EAsCYtG,UAAU,kBAKlBgB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,SAAQE,SAAA,EACrBD,EAAAA,EAAAA,KAAA,MAAID,UAAU,kCAAiCE,SAAEgF,EAAQW,QACzD5F,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAEgF,EAAQ5B,QACtCtC,EAAAA,EAAAA,MAAA,KAAGhB,UAAU,6BAA4BE,SAAA,CAAC,gBAAc,IAAIuG,KAAKvB,EAAQwB,UAAUC,+BAIvF1G,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wCAAuCE,SAAA,EACpDc,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAOD,UAAU,+CAA8CE,SAAC,cAC/DiF,GACClF,EAAAA,EAAAA,KAAA,SACEiG,KAAK,OACLL,KAAK,OACLe,MAAO1B,EAAQW,KACf9F,SAAUqF,EACVpF,UAAU,oIAGZC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAEgF,EAAQW,WAG1C7E,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAOD,UAAU,+CAA8CE,SAAC,kBAC/DiF,GACClF,EAAAA,EAAAA,KAAA,SACEiG,KAAK,QACLL,KAAK,QACLe,MAAO1B,EAAQ2B,MACf9G,SAAUqF,EACVpF,UAAU,oIAGZC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAEgF,EAAQ2B,YAG1C7F,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAOD,UAAU,+CAA8CE,SAAC,iBAC/DiF,GACClF,EAAAA,EAAAA,KAAA,SACEiG,KAAK,MACLL,KAAK,QACLe,MAAO1B,EAAQ4B,MACf/G,SAAUqF,EACVpF,UAAU,oIAGZC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAEgF,EAAQ4B,YAG1C9F,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAOD,UAAU,+CAA8CE,SAAC,UAChED,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAEgF,EAAQ5B,mBA1F5CrD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCE,UACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAC,yCA4F7B,EC1EV,EA9CkDN,IAM3C,IAN4C,QACjDmH,EAAO,SACPhH,EAAQ,KACR0C,EAAO,KAAI,SACXjC,GAAW,EAAK,UAChBR,EAAY,IACbJ,EAEC,MAAMoH,EAAc,CAClBlE,GAAI,CACFmE,UAAW,UACXC,OAAQ,UACRC,UAAWJ,EAAU,gBAAkB,iBAEzC5D,GAAI,CACF8D,UAAW,WACXC,OAAQ,UACRC,UAAWJ,EAAU,gBAAkB,iBAEzC3D,GAAI,CACF6D,UAAW,WACXC,OAAQ,UACRC,UAAWJ,EAAU,gBAAkB,kBAI3C,OACE9G,EAAAA,EAAAA,KAAA,UACEiG,KAAK,SACLlG,UAAW,wBAAwBgH,EAAYvE,GAAMwE,wJACnDF,EAAU,aAAe,iBACvBvG,EAAW,gCAAkC,oBAAoBR,IACrEO,QAASC,OAAW4G,EAAYrH,EAChCS,SAAUA,EACV8C,KAAK,SACL,eAAcyD,EAAQ7G,UAEtBD,EAAAA,EAAAA,KAAA,QACED,UAAW,gBAAgBgH,EAAYvE,GAAMyE,+DAC3CF,EAAYvE,GAAM0E,eAGf,EC0Bb,EAlE0DvH,IAKnD,IALoD,QACzDsF,EAAO,eACPmC,EAAc,iBACdC,EAAgB,oBAChBC,GACD3H,EAEC,OAAKsF,GAWHlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,cACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,iCAE5CD,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACL9E,QAAQ,UACRD,KAAK,KACLlC,QAAS+G,EAAiBpH,SAC3B,wBAKHc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,+BACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SACtCgF,EAAQuC,iBACL,oCACA,6CAGRxH,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQuC,iBACjB1H,SAAUA,IAAMsH,EAAe,0BAInCrG,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,oBACpDc,EAAAA,EAAAA,MAAA,KAAGhB,UAAU,6BAA4BE,SAAA,CAAC,eAAagF,EAAQyC,UAAU,YAAUzC,EAAQ0C,cAE7F3H,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACL9E,QAAQ,UACRD,KAAK,KACLzC,UAAU,+BACVO,QAASgH,EAAoBrH,SAC9B,gCAjDHD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCE,UACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAC,uCAmD7B,EC/EV,SAAS2H,EAAajI,EAInBgE,GAAQ,IAJY,MACrBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4+BACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCX,EAAAA,WAAiB8D,GC+FlD,EAzGkEjI,IAI3D,IAJ4D,QACjEsF,EAAO,eACPmC,EAAc,QACdS,GAAU,GACXlI,EAEC,OAAKsF,GAWHlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EAExBc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,+BAA8BE,SAAA,EAC3CD,EAAAA,EAAAA,KAAC4E,EAAAA,EAAQ,CAAC7E,UAAU,2BACpBC,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,+BAGtDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,yBACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,wCAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQ6C,qBAAqBlB,MACtC9G,SAAUA,IAAMsH,EAAe,6BAInCrG,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,wBACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,8CAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQ6C,qBAAqBC,KACtCjI,SAAUA,IAAMsH,EAAe,4BAInCrG,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,uBACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,gDAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQ6C,qBAAqBE,IACtClI,SAAUA,IAAMsH,EAAe,6BAMpCS,GAAW5C,EAAQgD,qBAClBlH,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,0CAAyCE,SAAA,EACtDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,+BAA8BE,SAAA,EAC3CD,EAAAA,EAAAA,KAAC4H,EAAa,CAAC7H,UAAU,2BACzBC,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,4BAGtDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,4BACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,+CAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQgD,mBAAmBC,SACpCpI,SAAUA,IAAMsH,EAAe,qCAInCrG,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,gBACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,gDAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQgD,mBAAmBE,UACpCrI,SAAUA,IAAMsH,EAAe,sCAInCrG,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kEAAiEE,SAAA,EAC9Ec,EAAAA,EAAAA,MAAA,OAAAd,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCE,SAAC,oCACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,2DAE5CD,EAAAA,EAAAA,KAACyH,EAAY,CACXX,QAAS7B,EAAQgD,mBAAmBG,sBACpCtI,SAAUA,IAAMsH,EAAe,wDAxFvCpH,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCE,UACpDD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAC,2CA2F7B,ECtHV,SAASoI,EAAmB1I,EAIzBgE,GAAQ,IAJkB,MAC3BjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,iRAEP,CACA,MACA,EADiCX,EAAAA,WAAiBuE,GCvBlD,SAASC,EAAO3I,EAIbgE,GAAQ,IAJM,MACfjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,oNAEP,CACA,MACA,EADiCX,EAAAA,WAAiBwE,GCvBlD,SAASC,EAAc5I,EAIpBgE,GAAQ,IAJa,MACtBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,sDAEP,CACA,MACA,EADiCX,EAAAA,WAAiByE,GCvBlD,SAASC,EAAO7I,EAIbgE,GAAQ,IAJM,MACfjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,scAEP,CACA,MACA,EADiCX,EAAAA,WAAiB0E,G,cCAlD,MAyFA,EAzFgD7I,IAGzC,IAH0C,WAC/C8I,EAAU,UACVC,GAAY,GACb/I,EAEC,MAAMgJ,EAAmB1C,IACvB,OAAQA,GACN,IAAK,QACH,OAAOjG,EAAAA,EAAAA,KAACqI,EAAmB,CAACtI,UAAU,YACxC,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAACsI,EAAO,CAACvI,UAAU,YAC5B,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAAC0D,EAAe,CAAC3D,UAAU,YACpC,IAAK,UACH,OAAOC,EAAAA,EAAAA,KAACuI,EAAc,CAACxI,UAAU,YACnC,IAAK,UACH,OAAOC,EAAAA,EAAAA,KAAC2E,EAAAA,EAAc,CAAC5E,UAAU,YACnC,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAACwI,EAAO,CAACzI,UAAU,YAC5B,IAAK,gBACH,OAAOC,EAAAA,EAAAA,KAAC4E,EAAAA,EAAQ,CAAC7E,UAAU,YAC7B,QACE,OAAOC,EAAAA,EAAAA,KAAC4I,EAAAA,EAAe,CAAC7I,UAAU,YACtC,EAIF,OAAI2I,GAEA1I,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCE,UACpDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,cAAaE,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yEACfC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,mCAQ1B,IAAtBwI,EAAWzH,QAEXhB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCE,UACpDD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,cAAaE,UAC1BD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeE,SAAC,6BAQrCD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWE,UACxBD,EAAAA,EAAAA,KAAA,MAAID,UAAU,QAAOE,SAClBwI,EAAWvI,KAAI,CAAC2I,EAAUC,KACzB9I,EAAAA,EAAAA,KAAA,MAAAC,UACEc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,gBAAeE,SAAA,CAC3B6I,IAAgBL,EAAWzH,OAAS,GACnChB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wDAAwD,cAAY,SAClF,MACJgB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,sCAAqCE,SAAA,EAClDD,EAAAA,EAAAA,KAAA,OAAAC,UACED,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeE,UAC5BD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uGAAsGE,UACnHD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeE,SAC3B4I,EAAS/G,MAAQ6G,EAAgBE,EAAS5C,eAKnDjG,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBE,UACpCc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBE,SAAA,EACpCD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BE,SAAE4I,EAASE,WACrD/I,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oBAAmBE,SAAE,IAAIuG,KAAKqC,EAASG,MAAMC,+BAlB9DJ,EAASxI,WA2BpB,E,kCClGH,MAAM6I,EAAa,CAIxBC,WAAYC,UACV,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAiB,YAClD,OAAOC,EAAAA,GAAmBC,QAAQJ,EAAU,UAAW,UACzD,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFE,cAAeR,UACb,IACE,MAAMC,QAAiBC,EAAAA,EAAUO,IAAiB,WAAYC,GAC9D,OAAON,EAAAA,GAAmBO,OAAOV,EAAU,UAAW,UACxD,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFM,qBAAsBZ,UACpB,IACE,MAAMa,EAAW,IAAIC,SACrBD,EAASE,OAAO,UAAW9D,GAE3B,MAAMgD,QAAiBC,EAAAA,EAAUO,IAAiB,mBAAoBI,EAAU,CAC9EG,QAAS,CACP,eAAgB,yBAIpB,OAAOZ,EAAAA,GAAmBO,OAAOV,EAAU,kBAAmB,UAChE,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFW,eAAgBjB,UACd,IAEE,aADME,EAAAA,EAAUO,IAAI,oBAAqBS,GAClC,CAAEC,SAAS,EACpB,CAAE,MAAOb,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFc,qBAAsBpB,UACpB,IACE,MAAMC,QAAiBC,EAAAA,EAAUmB,OAAoB,oBACrD,OAAOjB,EAAAA,GAAmBO,OAAOV,EAAU,kBAAmB,UAChE,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFgB,aAActB,UACZ,IACE,MAAMnE,QAAgBiE,EAAWc,qBAAqB3D,GAEtD,MAAO,CAAEsE,UADS1F,EAAQO,QAAU,GAEtC,CAAE,MAAOkE,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFkB,kBAAmBxB,UACjB,IACE,MAAMC,QAAiBC,EAAAA,EAAUO,IAA0D,uBAAwB,CAAEgB,gBAErH,OADarB,EAAAA,GAAmBO,OAAOV,EAAU,cAAe,WACpDwB,WACd,CAAE,MAAOnB,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFoB,eAAgB1B,UACd,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAuB,oBAAqB,CAAEwB,WAC/E,OAAOvB,EAAAA,GAAmBwB,QAAQ3B,EAAU,gBAAgB,EAC9D,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,IAIJ,I,cC5GO,MA+OP,EA/O0BuB,KACxB,MAAOhG,EAASiG,IAAcC,EAAAA,EAAAA,UAAyB,OAChDzC,EAAW0C,IAAgBD,EAAAA,EAAAA,WAAS,IACpCzB,EAAO2B,IAAYF,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEG,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAsBlG,EAAAA,EAAAA,QAAOgG,GAC7BG,GAAoBnG,EAAAA,EAAAA,SAAO,GAC3BoG,GAAWpG,EAAAA,EAAAA,QAAoD,MAC/DqG,EAAY,KAGlBC,EAAAA,EAAAA,YAAU,KACRJ,EAAoBzF,QAAUuF,CAAgB,IAIhD,MAAMO,GAAeC,EAAAA,EAAAA,cAAY,MAC1BJ,EAAS3F,SACPS,KAAKuF,MAAQL,EAAS3F,QAAQiG,UAAYL,GAChD,CAACA,IAGEM,GAAeH,EAAAA,EAAAA,cAAY1C,iBAE/B,KAFkD8C,UAAAlL,OAAA,QAAAmG,IAAA+E,UAAA,IAAAA,UAAA,KAE7BL,KAAkBH,EAAS3F,QAE9C,OADAmF,EAAWQ,EAAS3F,QAAQoG,MACrBT,EAAS3F,QAAQoG,KAG1Bf,GAAa,GACbC,EAAS,MACT,IACEe,QAAQC,IAAI,oCACZ,MAAMF,QAAajD,EAAWC,aAQ9B,OAPAiD,QAAQC,IAAI,6CAA8CF,GAC1DjB,EAAWiB,GAEXT,EAAS3F,QAAU,CACjBoG,OACAH,UAAWxF,KAAKuF,OAEXI,CACT,CAAE,MAAOG,GACPF,QAAQ1C,MAAM,uCAAwC4C,GACtD,MAAM5C,EAAQ4C,EACdjB,EAAS3B,GAGT,IAAI6C,EAAe,0BAgBnB,MAfI7C,EAAM8C,QAAQC,SAAS,WACzBF,EAAe,6DACN7C,EAAM8C,QAAQC,SAAS,QAAU/C,EAAM8C,QAAQC,SAAS,gBACjEF,EAAe,oEACN7C,EAAM8C,QAAQC,SAAS,QAAU/C,EAAM8C,QAAQC,SAAS,aACjEF,EAAe,qEACN7C,EAAM8C,QAAQC,SAAS,QAAU/C,EAAM8C,QAAQC,SAAS,aACjEF,EAAe,yCAGjBf,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,gBACP8L,QAASD,IAELD,CACR,CAAC,QACClB,GAAa,EACf,CACF,GAAG,CAACS,IAGEjC,GAAgBkC,EAAAA,EAAAA,cAAY1C,UAChCgC,GAAa,GACbC,EAAS,MACT,IACE,MAAMqB,QAAuBxD,EAAWU,cAAcE,GAYtD,OAXAoB,EAAWwB,GAEXhB,EAAS3F,QAAU,CACjBoG,KAAMO,EACNV,UAAWxF,KAAKuF,OAElBP,EAAoBzF,QAAQ,CAC1BE,KAAM,UACNvF,MAAO,UACP8L,QAAS,iCAEJE,CACT,CAAE,MAAOJ,GAOP,MANAjB,EAASiB,GACTd,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,QACP8L,QAAS,6BAELF,CACR,CAAC,QACClB,GAAa,EACf,IACC,IAGGV,GAAeoB,EAAAA,EAAAA,cAAY1C,UAC/BgC,GAAa,GACbC,EAAS,MACT,IACE,MAAM,UAAEV,SAAoBzB,EAAWwB,aAAarE,GAC9CqG,EAAiBzH,EAAU,IAAKA,EAASO,OAAQmF,GAAc,KAcrE,OAbAO,EAAWwB,GAEPA,IACFhB,EAAS3F,QAAU,CACjBoG,KAAMO,EACNV,UAAWxF,KAAKuF,QAGpBP,EAAoBzF,QAAQ,CAC1BE,KAAM,UACNvF,MAAO,UACP8L,QAAS,gCAEJ7B,CACT,CAAE,MAAO2B,GAOP,MANAjB,EAASiB,GACTd,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,QACP8L,QAAS,4BAELF,CACR,CAAC,QACClB,GAAa,EACf,IACC,CAACnG,IAGEoF,GAAiByB,EAAAA,EAAAA,cAAY1C,UACjCgC,GAAa,GACbC,EAAS,MACT,IACE,MAAMsB,QAAezD,EAAWmB,eAAeC,GAQ/C,OAPIqC,EAAOpC,SACTiB,EAAoBzF,QAAQ,CAC1BE,KAAM,UACNvF,MAAO,UACP8L,QAAS,kCAGNG,CACT,CAAE,MAAOL,GAOP,MANAjB,EAASiB,GACTd,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,QACP8L,QAAS,8BAELF,CACR,CAAC,QACClB,GAAa,EACf,IACC,IAGGR,GAAoBkB,EAAAA,EAAAA,cAAY1C,UACpCgC,GAAa,GACbC,EAAS,MACT,IACE,MAAMuB,QAA2B1D,EAAW0B,kBAAkBC,GACxD6B,EAAiBzH,EAAU,IAAKA,EAAS6C,qBAAsB8E,GAAuB,KAc5F,OAbA1B,EAAWwB,GAEPA,IACFhB,EAAS3F,QAAU,CACjBoG,KAAMO,EACNV,UAAWxF,KAAKuF,QAGpBP,EAAoBzF,QAAQ,CAC1BE,KAAM,UACNvF,MAAO,UACP8L,QAAS,qCAEJI,CACT,CAAE,MAAON,GAOP,MANAjB,EAASiB,GACTd,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,QACP8L,QAAS,iCAELF,CACR,CAAC,QACClB,GAAa,EACf,IACC,CAACnG,IAGE6F,GAAiBgB,EAAAA,EAAAA,cAAY1C,UACjCgC,GAAa,GACbC,EAAS,MACT,IAEE,aAD0BnC,EAAW4B,eAAeC,EAEtD,CAAE,MAAOuB,GAOP,MANAjB,EAASiB,GACTd,EAAoBzF,QAAQ,CAC1BE,KAAM,QACNvF,MAAO,QACP8L,QAAS,iCAELF,CACR,CAAC,QACClB,GAAa,EACf,IACC,IAUH,OAPAQ,EAAAA,EAAAA,YAAU,KACHH,EAAkB1F,UACrB0F,EAAkB1F,SAAU,EAC5BkG,IACF,GACC,CAACA,IAEG,CACLhH,UACAyD,YACAgB,QACAuC,eACArC,gBACAc,eACAL,iBACAO,oBACAE,iBACD,EC4DH,EA3R8B+B,KAC5B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MAEf9H,QAAS6E,EACTpB,UAAWsE,EACXtD,MAAOuD,EAAY,cACnBrD,EAAa,aACbc,EAAY,eACZI,GACEG,KAEGpL,EAAWqN,IAAgB/B,EAAAA,EAAAA,UAAqB,YAChDjG,EAAWiI,IAAgBhC,EAAAA,EAAAA,WAAS,IACpCiC,EAAUC,IAAelC,EAAAA,EAAAA,WAAS,IAClC1C,EAAY6E,IAAiBnC,EAAAA,EAAAA,UAA4B,KACzDoC,EAAmBC,IAAwBrC,EAAAA,EAAAA,WAAS,IAGpDsC,EAAqBC,IAA0BvC,EAAAA,EAAAA,UAA+B,CAAC,GAGhFlG,EAAU6E,EAAc,IAAKA,KAAgB2D,GAAwB,KAG3ErB,QAAQC,IAAI,8BAA+B,CACzCS,OACAhD,cACAkD,iBACAC,aAA0B,OAAZA,QAAY,IAAZA,OAAY,EAAZA,EAAcT,QAC5BiB,yBAIF7B,EAAAA,EAAAA,YAAU,KACR,GAAkB,aAAd/L,GAA4BiL,GAAwC,IAAtBrC,EAAWzH,OAAc,CAClDoI,WACrBoE,GAAqB,GACrB,IACE,MAAMG,QAAqB7C,IAC3BwC,EAAcK,EAChB,CAAE,MAAOjE,GACP0C,QAAQ1C,MAAM,+BAAgCA,GAE9C4D,EAAc,GAChB,CAAC,QACCE,GAAqB,EACvB,GAEFI,EACF,IACC,CAAC/N,EAAWiL,EAAgBrC,EAAWzH,SAE1C,MAsBM6M,EAAqB1H,IACzB,MAAM,KAAEP,EAAI,MAAEe,GAAUR,EAAEG,OAC1BoH,GAAuBI,IAAI,IACtBA,EACH,CAAClI,GAAOe,KACP,EAGCoH,EAAsBC,IAC1B,GAAK/I,EAEL,GAAI+I,EAAQC,WAAW,kBAAmB,CACxC,MAAMC,EAAmBF,EAAQG,MAAM,KAAK,GAC5C,GAAID,GAAoBA,KAAoBjJ,EAAQ6C,qBAAsB,CACxE,MAAMsG,EAAWF,EACjBR,GAAuBI,IAAI,IACtBA,EACHhG,qBAAsB,IACjB7C,EAAQ6C,wBACRgG,EAAKhG,qBACR,CAACsG,IAAYnJ,EAAQ6C,qBAAqBsG,OAGhD,CACF,MAAO,GAAIJ,EAAQC,WAAW,uBAAwB,CACpD,MAAMI,EAAwBL,EAAQG,MAAM,KAAK,GAE3CG,EAA4BrJ,EAAQgD,mBAC1C,GAAIoG,GAAyBC,GAA6BD,KAAyBC,EAA2B,CAC5G,MAAMC,EAAgBF,EACtBX,GAAuBI,IAAI,IACtBA,EACH7F,mBAAoB,IACfqG,KACAR,EAAK7F,mBACR,CAACsG,IAAiBD,EAA0BC,OAGlD,CACF,MAAO,GAAIP,KAAW/I,EAAS,CAC7B,MAAMuJ,EAAaR,EACnBN,GAAuBI,IAAI,IACtBA,EACH,CAACU,IAAcvJ,EAAQuJ,MAE3B,GAGIC,EAAuBA,KAC3BrC,QAAQC,IAAI,kBAAkB,EAG1BqC,EAA0BA,KAC9BtC,QAAQC,IAAI,uBAAuB,EAG/BsC,EAAqBvF,UACzB,GAAKsB,EAEL,UACQA,EAAarE,GAGnBqH,GAAuBI,IACrB,MAAQtI,OAAQoJ,KAAYC,GAASf,EACrC,OAAOe,CAAI,GAEf,CAAE,MAAOnF,GACP0C,QAAQ1C,MAAM,2BAA4BA,EAC5C,GAII7B,EAA4B,mBAAX,OAAP5C,QAAO,IAAPA,OAAO,EAAPA,EAAS5B,MAyCzB,OAAI2J,IAAmB/H,GAEnBlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CACTC,MAAM,aACNC,YAAY,2EAEdX,EAAAA,EAAAA,KAACyB,EAAAA,EAAI,CAAAxB,UACHD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yCAAwCE,UACrDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,cAAaE,SAAA,EAC1BD,EAAAA,EAAAA,KAAC8O,EAAAA,EAAc,CAACtM,KAAK,QACrBxC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BE,SAAC,iCASlDgN,IAAiBhI,GAEjBlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CACTC,MAAM,aACNC,YAAY,2EAEdX,EAAAA,EAAAA,KAACyB,EAAAA,EAAI,CAAAxB,UACHD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yCAAwCE,UACrDc,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,cAAaE,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oBAAmBE,SAAC,4BACjCD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBE,SAAEgN,EAAaT,oBAS1DvH,GAKHlE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWE,SAAA,EACxBD,EAAAA,EAAAA,KAACS,EAAAA,EAAU,CACTC,MAAM,aACNC,YAAY,wEACZC,QACgB,YAAdf,IACEqF,GACEnE,EAAAA,EAAAA,MAAA8E,EAAAA,SAAA,CAAA5F,SAAA,EACED,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACL9E,QAAQ,UACRnC,QAASA,KACP6M,GAAa,GACbO,EAAuB,CAAC,EAAE,EAC1BzN,SACH,YAGDD,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACLjH,QAvMU8I,UACxB,GAAKQ,GAAkB3E,EAAvB,CAEAoI,GAAY,GACZ,IACE,MAAM0B,EAAa,CACjBnJ,KAAMX,EAAQW,KACdgB,MAAO3B,EAAQ2B,MACfC,MAAO5B,EAAQ4B,SACX5B,EAAQO,QAAUP,EAAQO,OAAOC,QAAU,CAAED,OAAQP,EAAQO,eAG7DoE,EAAcmF,GACpBrB,EAAuB,CAAC,GACxBP,GAAa,EACf,CAAE,MAAOzD,GACP0C,QAAQ1C,MAAM,0BAA2BA,EAC3C,CAAC,QACC2D,GAAY,EACd,CAlBsC,CAkBtC,EAqLclL,QAASiL,EAASnN,SACnB,qBAKHD,EAAAA,EAAAA,KAACuH,EAAAA,EAAM,CACLjH,QAASA,IAAM6M,GAAa,GAAMlN,SACnC,qBAUTc,EAAAA,EAAAA,MAACU,EAAAA,EAAI,CAAAxB,SAAA,EACHD,EAAAA,EAAAA,KAACgP,EAAW,CACVnP,UAAWA,EACX6E,YAAawI,KAEflN,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAME,SA9HFgP,MACvB,OAAQpP,GACN,IAAK,WACH,OACEG,EAAAA,EAAAA,KAACkP,EAAgB,CACfjK,QAASA,EACTmC,eAAgB2G,EAChB1G,iBAAkBoH,EAClBnH,oBAAqBoH,IAG3B,IAAK,gBACH,OACE1O,EAAAA,EAAAA,KAACmP,EAAoB,CACnBlK,QAASA,EACTmC,eAAgB2G,EAChBlG,QAASA,IAGf,IAAK,WACH,OACE7H,EAAAA,EAAAA,KAACoP,EAAW,CACV3G,WAAYA,EACZC,UAAW6E,IAGjB,QACE,OACEvN,EAAAA,EAAAA,KAACqP,EAAW,CACVpK,QAASA,EACTC,UAAWA,EACXC,cAAe0I,EACfzI,eAAgBuJ,IAGxB,EA4FOM,WA/CA,IAkDD,C,gDC9SV,SAAS3N,EAAgB3B,EAItBgE,GAAQ,IAJe,MACxBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBxC,E,gDCvBlD,SAASuD,EAASlF,EAIfgE,GAAQ,IAJQ,MACjBjD,EAAK,QACLkD,KACGC,GACJlE,EACC,OAAoBmE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQnD,EAAqBoD,EAAAA,cAAoB,QAAS,CAC3DzD,GAAIuD,GACHlD,GAAS,KAAmBoD,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCX,EAAAA,WAAiBe,E,yDCSlD,MAAM0C,EAAgC5H,IAmB/B,IAnBgC,SACrCM,EAAQ,QACRwC,EAAU,UAAS,KACnBD,EAAO,KAAI,UACXzC,EAAY,GAAE,QACdO,EAAO,SACPC,GAAW,EAAK,KAChB0F,EAAO,SAAQ,KACfnE,EAAI,aACJwN,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBpN,GAAU,EAAK,QACfqN,GAAU,EAAK,KACfC,EAAI,OACJnJ,EAAM,IACNoJ,EAAG,MACHhP,EAAK,UACLiP,EAAS,OACT7O,GACDnB,EACC,MAwBMiQ,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRzF,QAAS,0EACT0F,KAAM,2EACNC,KAAM,kFAiBWzN,WAdC,CAClB0N,GAAI,oBACJtN,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJiN,GAAI,qBAUU5N,WAPQjC,EAAW,gCAAkC,yBAClDgP,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5CzP,QAGEgJ,GACJhI,EAAAA,EAAAA,MAAA8E,EAAAA,SAAA,CAAA5F,SAAA,CACGkC,IACCpB,EAAAA,EAAAA,MAAA,OACEhB,UAAU,+CACVkE,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMlE,SAAA,EAElBD,EAAAA,EAAAA,KAAA,UACED,UAAU,aACVsQ,GAAG,KACHC,GAAG,KACHC,EAAE,KACFlM,OAAO,eACPD,YAAY,OAEdpE,EAAAA,EAAAA,KAAA,QACED,UAAU,aACVmE,KAAK,eACLO,EAAE,uHAKP3C,GAAyB,SAAjBwN,IAA4BnN,IACnCnC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,OAAME,SAAE6B,IAGzB7B,EAEA6B,GAAyB,UAAjBwN,IACPtP,EAAAA,EAAAA,KAAA,QAAMD,UAAU,OAAME,SAAE6B,OAM9B,OAAI2N,GAEAzP,EAAAA,EAAAA,KAAA,KACEyP,KAAMA,EACN1P,UAAW6P,EACXtJ,OAAQA,EACRoJ,IAAKA,IAAmB,WAAXpJ,EAAsB,2BAAwBa,GAC3D7G,QAASA,EACTI,MAAOA,EACP,aAAYiP,EACZ,cAAa7O,EAAOb,SAEnB8I,KAOL/I,EAAAA,EAAAA,KAAA,UACEiG,KAAMA,EACNlG,UAAW6P,EACXtP,QAASA,EACTC,SAAUA,GAAY4B,EACtBzB,MAAOA,EACP,aAAYiP,EACZ,cAAa7O,EAAOb,SAEnB8I,GACM,EAIb,GAAevH,EAAAA,EAAAA,MAAK+F,E", "sources": ["components/common/Tabs.tsx", "components/layout/PageHeader.tsx", "components/common/Card.tsx", "components/common/LoadingSpinner.tsx", "../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "features/profile/components/ProfileTabs.tsx", "../node_modules/@heroicons/react/24/outline/esm/CameraIcon.js", "features/profile/components/ProfileInfo.tsx", "components/common/ToggleSwitch.tsx", "features/profile/components/SecuritySettings.tsx", "../node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js", "features/profile/components/NotificationSettings.tsx", "../node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js", "../node_modules/@heroicons/react/24/outline/esm/KeyIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CogIcon.js", "features/profile/components/ActivityLog.tsx", "features/profile/api/profileApi.ts", "features/profile/hooks/useProfile.ts", "pages/ProfilePage.tsx", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "components/common/Button.tsx"], "sourcesContent": ["// src/components/common/Tabs.tsx\r\nimport React from 'react';\r\n\r\nexport interface Tab {\r\n  id: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface TabsProps {\r\n  tabs: Tab[];\r\n  activeTab: string;\r\n  onChange: (tabId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nconst Tabs: React.FC<TabsProps> = ({\r\n  tabs,\r\n  activeTab,\r\n  onChange,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`border-b border-gray-200 ${className}`}>\r\n      <nav className=\"-mb-px flex space-x-8\">\r\n        {tabs.map((tab) => {\r\n          const isActive = tab.id === activeTab;\r\n          \r\n          return (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => !tab.disabled && onChange(tab.id)}\r\n              className={`\r\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\r\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\r\n                ${isActive\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}\r\n                ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\r\n              `}\r\n              disabled={tab.disabled}\r\n            >\r\n              {tab.label}\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Tabs;", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;", "/**\r\n * Profile Tabs Component\r\n * \r\n * This component displays the tabs for the profile page.\r\n */\r\n\r\nimport React from 'react';\r\nimport type { ProfileTab } from '../types/index';\r\nimport Tabs, { type Tab } from '../../../components/common/Tabs';\r\nimport {\r\n  UserCircleIcon,\r\n  ShieldCheckIcon,\r\n  BellIcon,\r\n  ClockIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface ProfileTabsProps {\r\n  activeTab: ProfileTab;\r\n  onTabChange: (tab: ProfileTab) => void;\r\n}\r\n\r\nconst ProfileTabs: React.FC<ProfileTabsProps> = ({\r\n  activeTab,\r\n  onTabChange\r\n}) => {\r\n  const tabsData = [\r\n    {\r\n      id: 'profile',\r\n      label: 'Profile',\r\n      icon: <UserCircleIcon className=\"w-5 h-5 mr-2\" />\r\n    },\r\n    {\r\n      id: 'security',\r\n      label: 'Security',\r\n      icon: <ShieldCheckIcon className=\"w-5 h-5 mr-2\" />\r\n    },\r\n    {\r\n      id: 'notifications',\r\n      label: 'Notifications',\r\n      icon: <BellIcon className=\"w-5 h-5 mr-2\" />\r\n    },\r\n    {\r\n      id: 'activity',\r\n      label: 'Activity',\r\n      icon: <ClockIcon className=\"w-5 h-5 mr-2\" />\r\n    }\r\n  ];\r\n\r\n  // Convert our tabs data to the format expected by the Tabs component\r\n  const tabs = tabsData.map(tab => ({\r\n    id: tab.id,\r\n    label: (\r\n      <div className=\"flex items-center\">\r\n        {tab.icon}\r\n        {tab.label}\r\n      </div>\r\n    )\r\n  }));\r\n\r\n  return (\r\n    <Tabs\r\n      tabs={tabs as unknown as Tab[]}\r\n      activeTab={activeTab}\r\n      onChange={(tabId) => onTabChange(tabId as ProfileTab)}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ProfileTabs;\r\n\r\n", "import * as React from \"react\";\nfunction CameraIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CameraIcon);\nexport default ForwardRef;", "/**\r\n * Profile Info Component\r\n * \r\n * This component displays and allows editing of the user's profile information.\r\n */\r\n\r\nimport React, { useRef } from 'react';\r\nimport type { UserProfile } from '../types/index';\r\nimport { CameraIcon } from '@heroicons/react/24/outline';\r\nimport Avatar from '../../../components/common/Avatar';\r\n\r\ninterface ProfileInfoProps {\r\n  profile: UserProfile | null;\r\n  isEditing: boolean;\r\n  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onAvatarChange?: (file: File) => void;\r\n}\r\n\r\nconst ProfileInfo: React.FC<ProfileInfoProps> = ({\r\n  profile,\r\n  isEditing,\r\n  onInputChange,\r\n  onAvatarChange\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleAvatarClick = () => {\r\n    if (isEditing && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file && onAvatarChange) {\r\n      onAvatarChange(file);\r\n    }\r\n  };\r\n\r\n  // Return null or loading state if profile is not available\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <p className=\"text-gray-500\">Profile information not available</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col sm:flex-row items-center gap-6 pb-6 border-b border-gray-100\">\r\n        <div className=\"relative\">\r\n          <Avatar\r\n            {...(profile.avatar && profile.avatar.trim() && { src: profile.avatar })}\r\n            alt={profile.name}\r\n            name={profile.name}\r\n            size=\"2xl\"\r\n            className=\"w-24 h-24\"\r\n          />\r\n          {isEditing && (\r\n            <>\r\n              <button\r\n                onClick={handleAvatarClick}\r\n                className=\"absolute bottom-0 right-0 bg-white rounded-full p-1.5 shadow-md hover:bg-gray-50\"\r\n              >\r\n                <CameraIcon className=\"w-5 h-5 text-gray-600\" />\r\n              </button>\r\n              <input\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={handleFileChange}\r\n                className=\"hidden\"\r\n              />\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-xl font-bold text-gray-800\">{profile.name}</h3>\r\n          <p className=\"text-gray-500\">{profile.role}</p>\r\n          <p className=\"text-sm text-gray-500 mt-1\">Member since {new Date(profile.joinDate).toLocaleDateString()}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                value={profile.name}\r\n                onChange={onInputChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-gray-800\">{profile.name}</p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email Address</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                value={profile.email}\r\n                onChange={onInputChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-gray-800\">{profile.email}</p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"tel\"\r\n                name=\"phone\"\r\n                value={profile.phone}\r\n                onChange={onInputChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-gray-800\">{profile.phone}</p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Role</label>\r\n            <p className=\"text-gray-800\">{profile.role}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileInfo;\r\n", "/**\r\n * Toggle Switch Component\r\n * \r\n * A reusable toggle switch component.\r\n */\r\n\r\nimport React from 'react';\r\n\r\ninterface ToggleSwitchProps {\r\n  enabled: boolean;\r\n  onChange: () => void;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst ToggleSwitch: React.FC<ToggleSwitchProps> = ({\r\n  enabled,\r\n  onChange,\r\n  size = 'md',\r\n  disabled = false,\r\n  className = ''\r\n}) => {\r\n  // Size classes\r\n  const sizeClasses = {\r\n    sm: {\r\n      container: 'h-5 w-9',\r\n      toggle: 'h-3 w-3',\r\n      translate: enabled ? 'translate-x-4' : 'translate-x-1'\r\n    },\r\n    md: {\r\n      container: 'h-6 w-11',\r\n      toggle: 'h-4 w-4',\r\n      translate: enabled ? 'translate-x-6' : 'translate-x-1'\r\n    },\r\n    lg: {\r\n      container: 'h-7 w-14',\r\n      toggle: 'h-5 w-5',\r\n      translate: enabled ? 'translate-x-8' : 'translate-x-1'\r\n    }\r\n  };\r\n\r\n  return (\r\n    <button\r\n      type=\"button\"\r\n      className={`relative inline-flex ${sizeClasses[size].container} items-center rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 ${\r\n        enabled ? 'bg-primary' : 'bg-gray-200'\r\n      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${className}`}\r\n      onClick={disabled ? undefined : onChange}\r\n      disabled={disabled}\r\n      role=\"switch\"\r\n      aria-checked={enabled}\r\n    >\r\n      <span \r\n        className={`inline-block ${sizeClasses[size].toggle} transform rounded-full bg-white transition-transform ${\r\n          sizeClasses[size].translate\r\n        }`} \r\n      />\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default ToggleSwitch;\r\n", "/**\r\n * Security Settings Component\r\n * \r\n * This component displays and allows editing of the user's security settings.\r\n */\r\n\r\nimport React from 'react';\r\nimport type { UserProfile } from '../types/index';\r\nimport Button from '../../../components/common/Button';\r\nimport ToggleSwitch from '../../../components/common/ToggleSwitch';\r\n\r\ninterface SecuritySettingsProps {\r\n  profile: UserProfile | null;\r\n  onToggleChange: (setting: string) => void;\r\n  onChangePassword: () => void;\r\n  onSignOutAllDevices: () => void;\r\n}\r\n\r\nconst SecuritySettings: React.FC<SecuritySettingsProps> = ({\r\n  profile,\r\n  onToggleChange,\r\n  onChangePassword,\r\n  onSignOutAllDevices\r\n}) => {\r\n  // Return null or loading state if profile is not available\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <p className=\"text-gray-500\">Security settings not available</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n        <div>\r\n          <h3 className=\"text-base font-medium text-gray-800\">Password</h3>\r\n          <p className=\"text-sm text-gray-500 mt-1\">Last changed 30 days ago</p>\r\n        </div>\r\n        <Button \r\n          variant=\"outline\" \r\n          size=\"sm\"\r\n          onClick={onChangePassword}\r\n        >\r\n          Change Password\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n        <div>\r\n          <h3 className=\"text-base font-medium text-gray-800\">Two-Factor Authentication</h3>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            {profile.twoFactorEnabled \r\n              ? 'Enabled - Using Authenticator App' \r\n              : 'Disabled - Enable for extra security'}\r\n          </p>\r\n        </div>\r\n        <ToggleSwitch\r\n          enabled={profile.twoFactorEnabled}\r\n          onChange={() => onToggleChange('twoFactorEnabled')}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n        <div>\r\n          <h3 className=\"text-base font-medium text-gray-800\">Login Sessions</h3>\r\n          <p className=\"text-sm text-gray-500 mt-1\">Last login: {profile.lastLogin} from IP {profile.lastIp}</p>\r\n        </div>\r\n        <Button \r\n          variant=\"outline\" \r\n          size=\"sm\" \r\n          className=\"text-red-600 hover:bg-red-50\"\r\n          onClick={onSignOutAllDevices}\r\n        >\r\n          Sign Out All Devices\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SecuritySettings;\r\n", "import * as React from \"react\";\nfunction Cog6ToothIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(Cog6ToothIcon);\nexport default ForwardRef;", "/**\r\n * Enhanced Notification Settings Component\r\n *\r\n * This component displays and allows editing of both personal and admin notification settings.\r\n * Consolidated from the previous Settings page notifications functionality.\r\n */\r\n\r\nimport React from 'react';\r\nimport type { UserProfile } from '../types/index';\r\nimport ToggleSwitch from '../../../components/common/ToggleSwitch';\r\nimport { BellIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface NotificationSettingsProps {\r\n  profile: UserProfile | null;\r\n  onToggleChange: (setting: string) => void;\r\n  isAdmin?: boolean;\r\n}\r\n\r\nconst NotificationSettings: React.FC<NotificationSettingsProps> = ({\r\n  profile,\r\n  onToggleChange,\r\n  isAdmin = false\r\n}) => {\r\n  // Return null or loading state if profile is not available\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <p className=\"text-gray-500\">Notification settings not available</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Personal Notification Preferences */}\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center gap-2 mb-4\">\r\n          <BellIcon className=\"h-5 w-5 text-gray-600\" />\r\n          <h2 className=\"text-lg font-semibold text-gray-800\">Personal Notifications</h2>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n          <div>\r\n            <h3 className=\"text-base font-medium text-gray-800\">Email Notifications</h3>\r\n            <p className=\"text-sm text-gray-500 mt-1\">Receive notifications via email</p>\r\n          </div>\r\n          <ToggleSwitch\r\n            enabled={profile.notificationsEnabled.email}\r\n            onChange={() => onToggleChange('notifications.email')}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n          <div>\r\n            <h3 className=\"text-base font-medium text-gray-800\">Push Notifications</h3>\r\n            <p className=\"text-sm text-gray-500 mt-1\">Receive push notifications in browser</p>\r\n          </div>\r\n          <ToggleSwitch\r\n            enabled={profile.notificationsEnabled.push}\r\n            onChange={() => onToggleChange('notifications.push')}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n          <div>\r\n            <h3 className=\"text-base font-medium text-gray-800\">SMS Notifications</h3>\r\n            <p className=\"text-sm text-gray-500 mt-1\">Receive important notifications via SMS</p>\r\n          </div>\r\n          <ToggleSwitch\r\n            enabled={profile.notificationsEnabled.sms}\r\n            onChange={() => onToggleChange('notifications.sms')}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Admin Notification Settings - Only show for admin users */}\r\n      {isAdmin && profile.adminNotifications && (\r\n        <div className=\"space-y-6 pt-6 border-t border-gray-200\">\r\n          <div className=\"flex items-center gap-2 mb-4\">\r\n            <Cog6ToothIcon className=\"h-5 w-5 text-gray-600\" />\r\n            <h2 className=\"text-lg font-semibold text-gray-800\">Admin Notifications</h2>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n            <div>\r\n              <h3 className=\"text-base font-medium text-gray-800\">New User Registrations</h3>\r\n              <p className=\"text-sm text-gray-500 mt-1\">Get notified when a new user registers</p>\r\n            </div>\r\n            <ToggleSwitch\r\n              enabled={profile.adminNotifications.newUsers}\r\n              onChange={() => onToggleChange('adminNotifications.newUsers')}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n            <div>\r\n              <h3 className=\"text-base font-medium text-gray-800\">New Orders</h3>\r\n              <p className=\"text-sm text-gray-500 mt-1\">Get notified when a new order is placed</p>\r\n            </div>\r\n            <ToggleSwitch\r\n              enabled={profile.adminNotifications.newOrders}\r\n              onChange={() => onToggleChange('adminNotifications.newOrders')}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between py-4 border-b border-gray-100\">\r\n            <div>\r\n              <h3 className=\"text-base font-medium text-gray-800\">Supplier Verification Requests</h3>\r\n              <p className=\"text-sm text-gray-500 mt-1\">Get notified when a supplier requests verification</p>\r\n            </div>\r\n            <ToggleSwitch\r\n              enabled={profile.adminNotifications.supplierVerifications}\r\n              onChange={() => onToggleChange('adminNotifications.supplierVerifications')}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationSettings;\r\n", "import * as React from \"react\";\nfunction ComputerDesktopIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ComputerDesktopIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction KeyIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(KeyIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PlusCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CogIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CogIcon);\nexport default ForwardRef;", "/**\r\n * Activity Log Component\r\n * \r\n * This component displays the user's activity log.\r\n */\r\n\r\nimport React from 'react';\r\nimport type { ActivityLogItem } from '../types/index';\r\nimport {\r\n  UserCircleIcon,\r\n  KeyIcon,\r\n  ShieldCheckIcon,\r\n  CheckCircleIcon,\r\n  ComputerDesktopIcon,\r\n  CogIcon,\r\n  BellIcon,\r\n  PlusCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface ActivityLogProps {\r\n  activities: ActivityLogItem[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst ActivityLog: React.FC<ActivityLogProps> = ({\r\n  activities,\r\n  isLoading = false\r\n}) => {\r\n  // Helper function to get icon based on activity type\r\n  const getActivityIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'login':\r\n        return <ComputerDesktopIcon className=\"h-5 w-5\" />;\r\n      case 'password':\r\n        return <KeyIcon className=\"h-5 w-5\" />;\r\n      case 'security':\r\n        return <ShieldCheckIcon className=\"h-5 w-5\" />;\r\n      case 'account':\r\n        return <PlusCircleIcon className=\"h-5 w-5\" />;\r\n      case 'profile':\r\n        return <UserCircleIcon className=\"h-5 w-5\" />;\r\n      case 'settings':\r\n        return <CogIcon className=\"h-5 w-5\" />;\r\n      case 'notifications':\r\n        return <BellIcon className=\"h-5 w-5\" />;\r\n      default:\r\n        return <CheckCircleIcon className=\"h-5 w-5\" />;\r\n    }\r\n  };\r\n\r\n  // Show loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto\"></div>\r\n            <p className=\"mt-2 text-sm text-gray-500\">Loading activity log...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show empty state\r\n  if (activities.length === 0) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <div className=\"text-center\">\r\n            <p className=\"text-gray-500\">No activity found</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flow-root\">\r\n        <ul className=\"-mb-8\">\r\n          {activities.map((activity, activityIdx) => (\r\n            <li key={activity.id}>\r\n              <div className=\"relative pb-8\">\r\n                {activityIdx !== activities.length - 1 ? (\r\n                  <span className=\"absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200\" aria-hidden=\"true\" />\r\n                ) : null}\r\n                <div className=\"relative flex items-start space-x-3\">\r\n                  <div>\r\n                    <div className=\"relative px-1\">\r\n                      <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 ring-8 ring-white icon-container\">\r\n                        <div className=\"text-gray-600\">\r\n                          {activity.icon || getActivityIcon(activity.type)}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1 py-1.5\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      <div className=\"font-medium text-gray-900\">{activity.content}</div>\r\n                      <span className=\"whitespace-nowrap\">{new Date(activity.date).toLocaleString()}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ActivityLog;\r\n", "/**\r\n * Profile API Service\r\n * \r\n * This file provides methods for interacting with the profile API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { UserProfile, ProfileUpdateRequest, PasswordChangeRequest, ActivityLogItem } from '../types';\r\n\r\nexport const profileApi = {\r\n  /**\r\n   * Get user profile\r\n   */\r\n  getProfile: async (): Promise<UserProfile> => {\r\n    try {\r\n      const response = await apiClient.get<UserProfile>('/profile');\r\n      return responseValidators.getById(response, 'profile', 'current');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update user profile\r\n   */\r\n  updateProfile: async (profileData: ProfileUpdateRequest): Promise<UserProfile> => {\r\n    try {\r\n      const response = await apiClient.put<UserProfile>('/profile', profileData);\r\n      return responseValidators.update(response, 'profile', 'current');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update profile picture\r\n   */\r\n  updateProfilePicture: async (file: File): Promise<UserProfile> => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('picture', file);\r\n\r\n      const response = await apiClient.put<UserProfile>('/profile/picture', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n\r\n      return responseValidators.update(response, 'profile picture', 'current');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Change password\r\n   */\r\n  changePassword: async (passwordData: PasswordChangeRequest): Promise<{ success: boolean }> => {\r\n    try {\r\n      await apiClient.put('/profile/password', passwordData);\r\n      return { success: true };\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete profile picture\r\n   */\r\n  deleteProfilePicture: async (): Promise<UserProfile> => {\r\n    try {\r\n      const response = await apiClient.delete<UserProfile>('/profile/picture');\r\n      return responseValidators.update(response, 'profile picture', 'current');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update avatar (alias for updateProfilePicture)\r\n   */\r\n  updateAvatar: async (file: File): Promise<{ avatarUrl: string }> => {\r\n    try {\r\n      const profile = await profileApi.updateProfilePicture(file);\r\n      const avatarUrl = profile.avatar || '';\r\n      return { avatarUrl };\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update profile preferences\r\n   */\r\n  updatePreferences: async (preferences: UserProfile['notificationsEnabled']): Promise<UserProfile['notificationsEnabled']> => {\r\n    try {\r\n      const response = await apiClient.put<{ preferences: UserProfile['notificationsEnabled'] }>('/profile/preferences', { preferences });\r\n      const data = responseValidators.update(response, 'preferences', 'current');\r\n      return data.preferences;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get activity log\r\n   */\r\n  getActivityLog: async (params?: { page?: number; limit?: number }): Promise<ActivityLogItem[]> => {\r\n    try {\r\n      const response = await apiClient.get<ActivityLogItem[]>('/profile/activity', { params });\r\n      return responseValidators.getList(response, 'activity log', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default profileApi;\r\n", "/**\r\n * Profile Hook\r\n * \r\n * This hook provides methods and state for working with the user profile.\r\n */\r\n\r\nimport { useState, useCallback, useEffect, useRef } from 'react';\r\nimport { Profile, ProfileUpdateData, PasswordChangeData } from '../types/index';\r\nimport profileApi from '../api/profileApi';\r\nimport useNotification from '../../../hooks/useNotification';\r\n\r\nexport const useProfile = () => {\r\n  const [profile, setProfile] = useState<Profile | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues with showNotification\r\n  const showNotificationRef = useRef(showNotification);\r\n  const hasInitialFetched = useRef(false);\r\n  const cacheRef = useRef<{ data: Profile; timestamp: number } | null>(null);\r\n  const CACHE_TTL = 5 * 60 * 1000; // 5 minutes\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n\r\n  // Check if cached data is still valid\r\n  const isCacheValid = useCallback(() => {\r\n    if (!cacheRef.current) return false;\r\n    return Date.now() - cacheRef.current.timestamp < CACHE_TTL;\r\n  }, [CACHE_TTL]);\r\n\r\n  // Fetch the current user's profile\r\n  const fetchProfile = useCallback(async (forceRefresh = false) => {\r\n    // Use cache if available and valid, unless force refresh is requested\r\n    if (!forceRefresh && isCacheValid() && cacheRef.current) {\r\n      setProfile(cacheRef.current.data);\r\n      return cacheRef.current.data;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      console.log('[useProfile] Fetching profile...');\r\n      const data = await profileApi.getProfile();\r\n      console.log('[useProfile] Profile fetched successfully:', data);\r\n      setProfile(data);\r\n      // Cache the data\r\n      cacheRef.current = {\r\n        data,\r\n        timestamp: Date.now()\r\n      };\r\n      return data;\r\n    } catch (err) {\r\n      console.error('[useProfile] Error fetching profile:', err);\r\n      const error = err as Error;\r\n      setError(error);\r\n\r\n      // Provide more specific error messages\r\n      let errorMessage = 'Failed to fetch profile';\r\n      if (error.message.includes('Network')) {\r\n        errorMessage = 'Network error. Please check your connection and try again.';\r\n      } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {\r\n        errorMessage = 'You are not authorized to view this profile. Please log in again.';\r\n      } else if (error.message.includes('404') || error.message.includes('Not found')) {\r\n        errorMessage = 'Profile not found. Please contact support if this issue persists.';\r\n      } else if (error.message.includes('500') || error.message.includes('Server')) {\r\n        errorMessage = 'Server error. Please try again later.';\r\n      }\r\n\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Profile Error',\r\n        message: errorMessage\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [isCacheValid]); // Remove showNotification dependency\r\n\r\n  // Update the current user's profile\r\n  const updateProfile = useCallback(async (profileData: ProfileUpdateData) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedProfile = await profileApi.updateProfile(profileData);\r\n      setProfile(updatedProfile);\r\n      // Update cache\r\n      cacheRef.current = {\r\n        data: updatedProfile,\r\n        timestamp: Date.now()\r\n      };\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Profile updated successfully'\r\n      });\r\n      return updatedProfile;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update profile'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // Remove showNotification dependency\r\n\r\n  // Update the current user's avatar\r\n  const updateAvatar = useCallback(async (file: File) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const { avatarUrl } = await profileApi.updateAvatar(file);\r\n      const updatedProfile = profile ? { ...profile, avatar: avatarUrl } : null;\r\n      setProfile(updatedProfile);\r\n      // Update cache\r\n      if (updatedProfile) {\r\n        cacheRef.current = {\r\n          data: updatedProfile,\r\n          timestamp: Date.now()\r\n        };\r\n      }\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Avatar updated successfully'\r\n      });\r\n      return avatarUrl;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update avatar'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [profile]); // Add profile dependency\r\n\r\n  // Change the current user's password\r\n  const changePassword = useCallback(async (passwordData: PasswordChangeData) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const result = await profileApi.changePassword(passwordData);\r\n      if (result.success) {\r\n        showNotificationRef.current({\r\n          type: 'success',\r\n          title: 'Success',\r\n          message: 'Password changed successfully'\r\n        });\r\n      }\r\n      return result;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to change password'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // Remove showNotification dependency\r\n\r\n  // Update profile preferences\r\n  const updatePreferences = useCallback(async (preferences: Profile['notificationsEnabled']) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedPreferences = await profileApi.updatePreferences(preferences);\r\n      const updatedProfile = profile ? { ...profile, notificationsEnabled: updatedPreferences } : null;\r\n      setProfile(updatedProfile);\r\n      // Update cache\r\n      if (updatedProfile) {\r\n        cacheRef.current = {\r\n          data: updatedProfile,\r\n          timestamp: Date.now()\r\n        };\r\n      }\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Preferences updated successfully'\r\n      });\r\n      return updatedPreferences;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update preferences'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [profile]); // Add profile dependency\r\n\r\n  // Get activity log for the current user\r\n  const getActivityLog = useCallback(async (params?: { page?: number; limit?: number }) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const activityLog = await profileApi.getActivityLog(params);\r\n      return activityLog;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch activity log'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // Remove showNotification dependency\r\n\r\n  // Load profile on mount (only if not already fetched)\r\n  useEffect(() => {\r\n    if (!hasInitialFetched.current) {\r\n      hasInitialFetched.current = true;\r\n      fetchProfile();\r\n    }\r\n  }, [fetchProfile]);\r\n\r\n  return {\r\n    profile,\r\n    isLoading,\r\n    error,\r\n    fetchProfile,\r\n    updateProfile,\r\n    updateAvatar,\r\n    changePassword,\r\n    updatePreferences,\r\n    getActivityLog\r\n  };\r\n};\r\n\r\nexport default useProfile;\r\n", "/**\r\n * Profile Page\r\n *\r\n * This page displays and allows editing of the user's profile.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Card from '../components/common/Card';\r\nimport Button from '../components/common/Button';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport useAuth from '../hooks/useAuth';\r\nimport {\r\n  ProfileTabs,\r\n  ProfileInfo,\r\n  SecuritySettings,\r\n  NotificationSettings,\r\n  ActivityLog,\r\n  useProfile,\r\n  UserProfile,\r\n  ProfileTab,\r\n  ActivityLogItem\r\n} from '../features/profile/index';\r\n\r\nconst ProfilePage: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const {\r\n    profile: profileData,\r\n    isLoading: profileLoading,\r\n    error: profileError,\r\n    updateProfile,\r\n    updateAvatar,\r\n    getActivityLog\r\n  } = useProfile();\r\n\r\n  const [activeTab, setActiveTab] = useState<ProfileTab>('profile');\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [activities, setActivities] = useState<ActivityLogItem[]>([]);\r\n  const [activitiesLoading, setActivitiesLoading] = useState(false);\r\n\r\n  // Create a working profile state that syncs with the hook\r\n  const [localProfileChanges, setLocalProfileChanges] = useState<Partial<UserProfile>>({});\r\n\r\n  // Merge hook profile data with local changes for editing\r\n  const profile = profileData ? { ...profileData, ...localProfileChanges } : null;\r\n\r\n  // Debug logging\r\n  console.log('[ProfilePage] Render state:', {\r\n    user,\r\n    profileData,\r\n    profileLoading,\r\n    profileError: profileError?.message,\r\n    localProfileChanges\r\n  });\r\n\r\n  // Load activity log when activity tab is accessed\r\n  useEffect(() => {\r\n    if (activeTab === 'activity' && getActivityLog && activities.length === 0) {\r\n      const loadActivities = async () => {\r\n        setActivitiesLoading(true);\r\n        try {\r\n          const activityData = await getActivityLog();\r\n          setActivities(activityData);\r\n        } catch (error) {\r\n          console.error('Failed to load activity log:', error);\r\n          // Fallback to empty array - error is handled by the hook\r\n          setActivities([]);\r\n        } finally {\r\n          setActivitiesLoading(false);\r\n        }\r\n      };\r\n      loadActivities();\r\n    }\r\n  }, [activeTab, getActivityLog, activities.length]);\r\n\r\n  const handleSaveProfile = async () => {\r\n    if (!updateProfile || !profile) return;\r\n\r\n    setIsSaving(true);\r\n    try {\r\n      const updateData = {\r\n        name: profile.name,\r\n        email: profile.email,\r\n        phone: profile.phone,\r\n        ...(profile.avatar && profile.avatar.trim() && { avatar: profile.avatar })\r\n      };\r\n\r\n      await updateProfile(updateData);\r\n      setLocalProfileChanges({}); // Clear local changes after successful save\r\n      setIsEditing(false);\r\n    } catch (error) {\r\n      console.error('Failed to save profile:', error);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setLocalProfileChanges(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleToggleChange = (setting: string) => {\r\n    if (!profile) return;\r\n\r\n    if (setting.startsWith('notifications.')) {\r\n      const notificationType = setting.split('.')[1];\r\n      if (notificationType && notificationType in profile.notificationsEnabled) {\r\n        const notifKey = notificationType as keyof typeof profile.notificationsEnabled;\r\n        setLocalProfileChanges(prev => ({\r\n          ...prev,\r\n          notificationsEnabled: {\r\n            ...profile.notificationsEnabled,\r\n            ...prev.notificationsEnabled,\r\n            [notifKey]: !profile.notificationsEnabled[notifKey]\r\n          }\r\n        }));\r\n      }\r\n    } else if (setting.startsWith('adminNotifications.')) {\r\n      const adminNotificationType = setting.split('.')[1];\r\n      // Safe access to adminNotifications with proper null checking\r\n      const currentAdminNotifications = profile.adminNotifications;\r\n      if (adminNotificationType && currentAdminNotifications && adminNotificationType in currentAdminNotifications) {\r\n        const adminNotifKey = adminNotificationType as keyof NonNullable<typeof profile.adminNotifications>;\r\n        setLocalProfileChanges(prev => ({\r\n          ...prev,\r\n          adminNotifications: {\r\n            ...currentAdminNotifications,\r\n            ...prev.adminNotifications,\r\n            [adminNotifKey]: !currentAdminNotifications[adminNotifKey]\r\n          }\r\n        }));\r\n      }\r\n    } else if (setting in profile) {\r\n      const profileKey = setting as keyof typeof profile;\r\n      setLocalProfileChanges(prev => ({\r\n        ...prev,\r\n        [profileKey]: !profile[profileKey]\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleChangePassword = () => {\r\n    console.log('Change password');\r\n  };\r\n\r\n  const handleSignOutAllDevices = () => {\r\n    console.log('Sign out all devices');\r\n  };\r\n\r\n  const handleAvatarChange = async (file: File) => {\r\n    if (!updateAvatar) return;\r\n\r\n    try {\r\n      await updateAvatar(file);\r\n      // The hook will update the profile state automatically\r\n      // We just need to clear any local avatar changes\r\n      setLocalProfileChanges(prev => {\r\n        const { avatar: _avatar, ...rest } = prev;\r\n        return rest;\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to update avatar:', error);\r\n    }\r\n  };\r\n\r\n  // Check if user is admin (in real app, this would come from auth context)\r\n  const isAdmin = profile?.role === 'Administrator';\r\n\r\n  const renderTabContent = () => {\r\n    switch (activeTab) {\r\n      case 'security':\r\n        return (\r\n          <SecuritySettings\r\n            profile={profile}\r\n            onToggleChange={handleToggleChange}\r\n            onChangePassword={handleChangePassword}\r\n            onSignOutAllDevices={handleSignOutAllDevices}\r\n          />\r\n        );\r\n      case 'notifications':\r\n        return (\r\n          <NotificationSettings\r\n            profile={profile}\r\n            onToggleChange={handleToggleChange}\r\n            isAdmin={isAdmin}\r\n          />\r\n        );\r\n      case 'activity':\r\n        return (\r\n          <ActivityLog\r\n            activities={activities}\r\n            isLoading={activitiesLoading}\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <ProfileInfo\r\n            profile={profile}\r\n            isEditing={isEditing}\r\n            onInputChange={handleInputChange}\r\n            onAvatarChange={handleAvatarChange}\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Show loading state while profile is being fetched\r\n  if (profileLoading && !profile) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <PageHeader\r\n          title=\"My Profile\"\r\n          description=\"View and manage your profile information and notification preferences\"\r\n        />\r\n        <Card>\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"text-center\">\r\n              <LoadingSpinner size=\"md\" />\r\n              <p className=\"mt-2 text-sm text-gray-500\">Loading profile...</p>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if profile failed to load\r\n  if (profileError && !profile) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <PageHeader\r\n          title=\"My Profile\"\r\n          description=\"View and manage your profile information and notification preferences\"\r\n        />\r\n        <Card>\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"text-center\">\r\n              <p className=\"text-red-600 mb-2\">Failed to load profile</p>\r\n              <p className=\"text-sm text-gray-500\">{profileError.message}</p>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Don't render if no profile data\r\n  if (!profile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title=\"My Profile\"\r\n        description=\"View and manage your profile information and notification preferences\"\r\n        actions={\r\n          activeTab === 'profile' && (\r\n            isEditing ? (\r\n              <>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => {\r\n                    setIsEditing(false);\r\n                    setLocalProfileChanges({}); // Clear unsaved changes\r\n                  }}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  onClick={handleSaveProfile}\r\n                  loading={isSaving}\r\n                >\r\n                  Save Changes\r\n                </Button>\r\n              </>\r\n            ) : (\r\n              <Button\r\n                onClick={() => setIsEditing(true)}\r\n              >\r\n                Edit Profile\r\n              </Button>\r\n            )\r\n          )\r\n        }\r\n      />\r\n\r\n\r\n\r\n      <Card>\r\n        <ProfileTabs\r\n          activeTab={activeTab}\r\n          onTabChange={setActiveTab}\r\n        />\r\n        <div className=\"mt-6\">\r\n          {renderTabContent()}\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfilePage;\r\n", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n"], "names": ["_ref", "tabs", "activeTab", "onChange", "className", "_jsx", "children", "map", "tab", "isActive", "id", "onClick", "disabled", "label", "<PERSON><PERSON><PERSON><PERSON>", "title", "description", "actions", "breadcrumbs", "testId", "_jsxs", "length", "Link", "to", "HomeIcon", "item", "index", "ChevronRightIcon", "path", "memo", "Card", "subtitle", "bodyClassName", "headerClassName", "footerClassName", "icon", "footer", "hoverable", "noPadding", "bordered", "loading", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "size", "variant", "color", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "style", "borderTopColor", "borderRightColor", "backgroundColor", "ShieldCheckIcon", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "onTabChange", "UserCircleIcon", "BellIcon", "ClockIcon", "Tabs", "tabId", "CameraIcon", "profile", "isEditing", "onInputChange", "onAvatarChange", "fileInputRef", "useRef", "Avatar", "avatar", "trim", "src", "alt", "name", "_Fragment", "handleAvatarClick", "current", "click", "type", "accept", "e", "_e$target$files", "file", "target", "files", "Date", "joinDate", "toLocaleDateString", "value", "email", "phone", "enabled", "sizeClasses", "container", "toggle", "translate", "undefined", "onToggleChange", "onChangePassword", "onSignOutAllDevices", "<PERSON><PERSON>", "twoFactorEnabled", "ToggleSwitch", "lastLogin", "lastIp", "Cog6ToothIcon", "isAdmin", "notificationsEnabled", "push", "sms", "adminNotifications", "newUsers", "newOrders", "supplierVerifications", "ComputerDesktopIcon", "KeyIcon", "PlusCircleIcon", "CogIcon", "activities", "isLoading", "getActivityIcon", "CheckCircleIcon", "activity", "activityIdx", "content", "date", "toLocaleString", "profileApi", "getProfile", "async", "response", "apiClient", "get", "responseValidators", "getById", "error", "handleApiError", "updateProfile", "put", "profileData", "update", "updateProfilePicture", "formData", "FormData", "append", "headers", "changePassword", "passwordData", "success", "deleteProfilePicture", "delete", "updateAvatar", "avatarUrl", "updatePreferences", "preferences", "getActivityLog", "params", "getList", "useProfile", "setProfile", "useState", "setIsLoading", "setError", "showNotification", "useNotification", "showNotificationRef", "hasInitialFetched", "cacheRef", "CACHE_TTL", "useEffect", "isCache<PERSON><PERSON>d", "useCallback", "now", "timestamp", "fetchProfile", "arguments", "data", "console", "log", "err", "errorMessage", "message", "includes", "updatedProfile", "result", "updatedPreferences", "ProfilePage", "user", "useAuth", "profileLoading", "profileError", "setActiveTab", "setIsEditing", "isSaving", "setIsSaving", "setActivities", "activitiesLoading", "setActivitiesLoading", "localProfileChanges", "setLocalProfileChanges", "activityData", "loadActivities", "handleInputChange", "prev", "handleToggleChange", "setting", "startsWith", "notificationType", "split", "notif<PERSON><PERSON>", "adminNotificationType", "currentAdminNotifications", "adminNotif<PERSON>ey", "<PERSON><PERSON><PERSON>", "handleChangePassword", "handleSignOutAllDevices", "handleAvatarChange", "_avatar", "rest", "LoadingSpinner", "updateData", "ProfileTabs", "renderTabContent", "SecuritySettings", "NotificationSettings", "ActivityLog", "ProfileInfo", "iconPosition", "fullWidth", "rounded", "href", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "text", "link", "xs", "xl", "cx", "cy", "r"], "sourceRoot": ""}