"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[139],{7907:(e,r,t)=>{t.d(r,{A:()=>n});var s=t(5043),i=t(579);const a=e=>{let{children:r,variant:t="primary",size:s="md",className:a="",onClick:n,disabled:l=!1,type:o="button",icon:c,iconPosition:d="left",fullWidth:x=!1,loading:g=!1,rounded:m=!1,href:h,target:b,rel:p,title:u,ariaLabel:f,testId:y}=e;const v=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[t]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[s]}\n    ${l?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${x?"w-full":""}\n    ${m?"rounded-full":"rounded-lg"}\n    ${a}\n  `,j=(0,i.jsxs)(i.Fragment,{children:[g&&(0,i.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,i.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,i.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!g&&(0,i.jsx)("span",{className:"mr-2",children:c}),r,c&&"right"===d&&(0,i.jsx)("span",{className:"ml-2",children:c})]});return h?(0,i.jsx)("a",{href:h,className:v,target:b,rel:p||("_blank"===b?"noopener noreferrer":void 0),onClick:n,title:u,"aria-label":f,"data-testid":y,children:j}):(0,i.jsx)("button",{type:o,className:v,onClick:n,disabled:l||g,title:u,"aria-label":f,"data-testid":y,children:j})},n=(0,s.memo)(a)},8139:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l});t(5043);var s=t(5501),i=t(7907),a=t(724),n=t(579);const l=()=>(0,n.jsx)("div",{className:"min-h-[80vh] flex flex-col items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-9xl font-bold text-gray-200",children:"404"}),(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Page Not Found"}),(0,n.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"The page you are looking for doesn't exist or has been moved."})]}),(0,n.jsx)("div",{className:"mt-8",children:(0,n.jsx)(i.A,{variant:"primary",size:"lg",icon:(0,n.jsx)(s.A,{className:"h-5 w-5"}),iconPosition:"left",href:a.b.DASHBOARD,children:"Back to Dashboard"})})]})})}}]);
//# sourceMappingURL=139.952ba1fe.chunk.js.map