{"version": 3, "file": "static/js/88.18982e91.chunk.js", "mappings": "wJAgBO,MAAMA,EAAgBA,CAC3BC,EACAC,KAEA,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,KACvCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEK,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAgBC,EAAAA,EAAAA,QAAOZ,GACvBa,GAAsBD,EAAAA,EAAAA,QAAOH,GAC7BK,GAAgBF,EAAAA,EAAAA,QAAOX,EAAQc,YAC/BC,GAAoBJ,EAAAA,EAAAA,SAAO,IAGjCK,EAAAA,EAAAA,YAAU,KACRN,EAAcO,QAAUlB,EACxBa,EAAoBK,QAAUT,EAC9BK,EAAcI,QAAUjB,EAAQc,UAAU,IAG5C,MAAMI,GAAgBC,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IACE,MAAMc,QAAaX,EAAcO,QAAQK,OAAOC,GAEhD,OADArB,EAAYmB,GACLA,CACT,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGuB,GAAgBT,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IAEE,aADqBG,EAAcO,QAAQY,QAAQC,EAErD,CAAE,MAAON,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG0B,GAAeZ,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,IACE,MAAMyB,QAAkBtB,EAAcO,QAAQgB,OAAOZ,GAOrD,OANAnB,GAAYgC,GAAQ,IAAIA,EAAMF,KAC9BpB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBe,CACT,CAAE,MAAOR,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG8B,GAAehB,EAAAA,EAAAA,cAAYC,MAAOU,EAAYT,KAClDhB,GAAa,GACbE,EAAS,MACT,IACE,MAAM6B,QAAsB1B,EAAcO,QAAQoB,OAAOP,EAAIT,GAS7D,OARAnB,GAAYgC,GAAQA,EAAKI,KAAIC,GAC1BA,EAAeT,KAAOA,EAAKM,EAAgBG,MAE9C3B,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBmB,CACT,CAAE,MAAOZ,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGmC,GAAerB,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,UACQG,EAAcO,QAAQwB,OAAOX,GACnC5B,GAAYgC,GAAQA,EAAKQ,QAAOH,GAAWA,EAAeT,KAAOA,MACjElB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,gCAE9B,CAAE,MAAOO,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAuCH,OApCAW,EAAAA,EAAAA,YAAU,KACR,IAA6B,IAAzBhB,EAAQ2C,eAA2B5B,EAAkBE,QAAS,CAChE2B,QAAQC,IAAI,8CAA8C7C,EAAQc,cAClEC,EAAkBE,SAAU,EAE5B,MAAM0B,EAAevB,UACnBf,GAAa,GACbE,EAAS,MACT,IACEqC,QAAQC,IAAI,mCAAmC7C,EAAQc,cACvD,MAAMO,QAAatB,EAAWuB,SAC9BsB,QAAQC,IAAI,qCAAqC7C,EAAQc,cAAeO,GACxEnB,EAAYmB,EACd,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EACdoB,QAAQtC,MAAM,kCAAkCN,EAAQc,cAAeR,GACvEC,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmB3B,EAAQc,cAExC,CAAC,QACC8B,QAAQC,IAAI,sCAAsC7C,EAAQc,cAC1DT,GAAa,EACf,GAGFsC,GACF,IACC,CACD5C,EACAC,EAAQc,WACRd,EAAQ2C,eAGH,CACL1C,WACAG,YACAE,QACAY,gBACAU,gBACAG,eACAI,eACAK,eACAtC,cACD,C,uFCpLH,MAAM4C,EAAwCC,IAOvC,IAPwC,MAC7CrB,EAAK,YACLsB,EAAW,QACXC,EAAO,YACPC,EAAW,UACXC,EAAY,GAAE,OACdC,GACDL,EACC,OACEM,EAAAA,EAAAA,MAAA,OACEF,UAAW,QAAQA,IACnB,cAAaC,EAAOE,SAAA,CAGnBJ,GAAeA,EAAYK,OAAS,IACnCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAY,aAAW,aAAYG,UAChDD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oDAAmDG,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAG,IACHP,UAAU,uCACV,aAAW,OAAMG,UAEjBE,EAAAA,EAAAA,KAACG,EAAAA,EAAQ,CAACR,UAAU,gBAIvBD,EAAYZ,KAAI,CAACsB,EAAMC,KACtBR,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,oBAAmBG,SAAA,EAC3CE,EAAAA,EAAAA,KAACM,EAAAA,EAAgB,CAACX,UAAU,+BAC3BS,EAAKG,MAAQF,EAAQX,EAAYK,OAAS,GACzCC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAIE,EAAKG,KACTZ,UAAU,qBAAoBG,SAE7BM,EAAKI,SAGRR,EAAAA,EAAAA,KAAA,QAAML,UAAU,4BAA2BG,SAAEM,EAAKI,UAV7CH,WAmBjBR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EG,SAAA,EAC1FD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCG,SAAE5B,IACjDsB,GAAsC,kBAAhBA,GACrBQ,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BG,SAAEN,IAE3CA,KAIHC,IACCO,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oCAAmCG,SAC/CL,SAIH,EAIV,GAAegB,EAAAA,EAAAA,MAAKnB,E,gDC3FpB,SAASoB,EAAUnB,EAIhBoB,GAAQ,IAJS,MAClBzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCX,EAAAA,WAAiBJ,E,gDCvBlD,SAASJ,EAAgBf,EAItBoB,GAAQ,IAJe,MACxBzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBR,E,2CCR3C,MAAMoB,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAOlF,GACP,OAAO,CACT,GAGWoF,EAAcC,GACX,OAAVA,QAA4BC,IAAVD,IACD,kBAAVA,EAA2BA,EAAME,OAAOtC,OAAS,GACxDuC,MAAMC,QAAQJ,IAAeA,EAAMpC,OAAS,GAYrCyC,EAAaL,GACjB,WAAWP,KAAKO,GAGZM,EAAaN,GACjB,sBAAsBP,KAAKO,GAGvBO,EAAkBP,GACtB,iBAAiBP,KAAKO,GAGlBQ,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAASnD,OAAS,OAGjB,QAAQ6B,KAAKsB,OAGb,QAAQtB,KAAKsB,OAGb,QAAQtB,KAAKsB,MAGb,sCAAsCtB,KAAKsB,MAwBrCG,EAAeA,CAC1BC,EACAC,KAEA,MAAMC,EAA2C,CAAC,EAUlD,OARAzC,OAAO0C,QAAQF,GAAiBG,SAAQnE,IAAyB,IAAvBoE,EAAWC,GAAMrE,EACzD,MAAMsE,EAAMF,EACN7G,EA1BmBgH,EAC3BC,EACA5B,EACAyB,EACAI,KAEA,MAAMC,EAAY3B,MAAMC,QAAQqB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMM,KAAQD,EACjB,IAAKC,EAAKC,UAAUhC,EAAO6B,GACzB,OAAOE,EAAK/F,QAIhB,MAAO,EAAE,EAYO2F,CAAcH,EAAWL,EAAOO,GAAMD,EAAON,GACvDxG,IACF0G,EAAOK,GAAO/G,EAChB,IAGK0G,CAAM,EAIFD,EAAkB,CAC7Ba,SAAU,WAA2C,MAAsB,CACzED,UAAWjC,EACX/D,QAFwBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAED1C,MAAO,WAAuD,MAAsB,CAClFwC,UAAWzC,EACXvD,QAFqBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDvC,MAAO,WAAsD,MAAsB,CACjFqC,UAAWtC,EACX1D,QAFqBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAEDrC,IAAK,WAA6C,MAAsB,CACtEmC,UAAWpC,EACX5D,QAFmBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDC,UAAWA,CAACC,EAAapG,KAAgB,CACvCgG,UAAYhC,GA3GSmC,EAACnC,EAAeoC,IAChCpC,EAAMpC,QAAUwE,EA0GSD,CAAUnC,EAAOoC,GAC/CpG,QAASA,GAAW,oBAAoBoG,iBAG1CC,UAAWA,CAACC,EAAatG,KAAgB,CACvCgG,UAAYhC,GA5GSqC,EAACrC,EAAesC,IAChCtC,EAAMpC,QAAU0E,EA2GSD,CAAUrC,EAAOsC,GAC/CtG,QAASA,GAAW,wBAAwBsG,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EP,UAAW3B,EACXrE,QAFuBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAEDM,QAAS,WAAwD,MAAsB,CACrFR,UAAW1B,EACXtE,QAFuBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAEDO,aAAc,WAAwD,MAAsB,CAC1FT,UAAWzB,EACXvE,QAF4BkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDxB,KAAM,WAA8C,MAAsB,CACxEsB,UAAWxB,EACXxE,QAFoBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDnB,SAAU,WAA2H,MAAsB,CACzJiB,UAAWf,EACXjF,QAFwBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAEDQ,cAAe,WAA2C,MAAsB,CAC9EV,UAAWA,CAAChC,EAAe6B,IAAmBf,EAAiBd,EAAe,OAAR6B,QAAQ,IAARA,OAAQ,EAARA,EAAUb,iBAChFhF,QAF6BkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAEDS,qBAAsB,WAA2C,MAAsB,CACrFX,UAAWA,CAAChC,EAAe6B,IAAmBf,EAAiBd,EAAe,OAAR6B,QAAQ,IAARA,OAAQ,EAARA,EAAUd,UAChF/E,QAFoCkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDU,IAAK,WAA6C,MAAsB,CACtEZ,UAAYhC,GAAkB,sBAAsBP,KAAKO,GACzDhE,QAFmBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDW,MAAO,WAA+C,MAAsB,CAC1Eb,UAAYhC,GAAkBA,EAAQ,GAAKA,GAAS,OACpDhE,QAFqBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDY,MAAO,WAAwD,MAAsB,CACnFd,UAAYhC,GAAkB+C,OAAOC,UAAUhD,IAAUA,GAAS,EAClEhE,QAFqBkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDe,aAAc,WAA6D,MAAsB,CAC/FjB,UAAYhC,GAAkB+C,OAAOC,UAAUhD,IAAUA,GAAS,EAClEhE,QAF4BkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDgB,iBAAkB,WAAuE,MAAsB,CAC7GlB,UAAWA,CAACiB,EAAsBpB,KAC3BA,IAAaA,EAASiB,OACpBG,GAAgBpB,EAASiB,MAElC9G,QALgCkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDiB,cAAe,WAAkD,MAAsB,CACrFnB,UAAYhC,GAAiBG,MAAMC,QAAQJ,IAAUA,EAAMpC,OAAS,EACpE5B,QAF6BkG,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAEDkB,WAAY,eAACC,EAAgBnB,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEF,UAAYhC,KACLG,MAAMC,QAAQJ,IACZA,EAAMpC,QAAUyF,EAEzBrH,SALkDkG,UAAAtE,OAAA,EAAAsE,UAAA,QAAAjC,IAK9B,WAAWoD,mBAChC,E,gDCxOH,SAASC,EAAWlG,EAIjBoB,GAAQ,IAJU,MACnBzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCX,EAAAA,WAAiB2E,E,6ECMlD,MAAMC,EAA8BnG,IAiB7B,IAjB8B,OACnCoG,EAAM,QACNC,EAAO,MACP1H,EAAK,SACL4B,EAAQ,KACR+F,EAAO,KAAI,OACXC,EAAM,WACNC,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfvG,EAAY,GAAE,cACdwG,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtB1G,GACDL,EACC,MAAMgH,GAAWpJ,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAK,EAAAA,EAAAA,YAAU,KACR,MAAMgJ,EAAgBC,IAChBV,GAAwB,WAAVU,EAAE5C,KAClB+B,GACF,EASF,OANID,IACFe,SAASC,iBAAiB,UAAWH,GAErCE,SAASE,KAAKC,MAAMC,SAAW,UAG1B,KACLJ,SAASK,oBAAoB,UAAWP,GACxCE,SAASE,KAAKC,MAAMC,SAAW,MAAM,CACtC,GACA,CAACnB,EAAQC,EAASG,KAGrBvI,EAAAA,EAAAA,YAAU,KACR,IAAKmI,IAAWY,EAAS9I,QAAS,OAElC,MAAMuJ,EAAoBT,EAAS9I,QAAQwJ,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBjH,OAAc,OAEpC,MAAMmH,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBjH,OAAS,GAE3DqH,EAAgBX,IACN,QAAVA,EAAE5C,MAEF4C,EAAEY,SACAX,SAASY,gBAAkBJ,IAC7BC,EAAYI,QACZd,EAAEe,kBAGAd,SAASY,gBAAkBH,IAC7BD,EAAaK,QACbd,EAAEe,kBAEN,EAMF,OAHAd,SAASC,iBAAiB,UAAWS,GACrCF,EAAaK,QAEN,KACLb,SAASK,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAACzB,KAECA,EAAQ,OAAO,KAGpB,MAUM8B,GACJ5H,EAAAA,EAAAA,MAAC6H,EAAAA,SAAQ,CAAA5H,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACEL,UAAW,gEAAgE2G,IAC3EqB,QAAS3B,EAAuBJ,OAAUxD,EAC1C,cAAa,GAAGxC,gBAIlBI,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qCAAoCG,UACjDE,EAAAA,EAAAA,KAAA,OAAKL,UAAW,yBAAyBuG,EAAW,SAAW,yCAAyCpG,UACtGD,EAAAA,EAAAA,MAAA,OACEyB,IAAKiF,EACL5G,UAAW,GAxBD,CAClBiI,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BpC,2GAA8GlG,IACxIgI,QAAUlB,GAAMA,EAAEyB,kBAClB,cAAatI,EAAOE,SAAA,EAGpBD,EAAAA,EAAAA,MAAA,OAAKF,UAAW,wEAAwEyG,IAAkBtG,SAAA,CACtF,kBAAV5B,GACN8B,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCG,SAAE5B,IAErDA,EAED+H,IACCjG,EAAAA,EAAAA,KAAA,UACE/B,KAAK,SACL0B,UAAU,wGACVgI,QAAS/B,EACT,aAAW,cACX,cAAa,GAAGhG,iBAAsBE,UAEtCE,EAAAA,EAAAA,KAACmI,EAAAA,EAAS,CAACxI,UAAU,kBAM3BK,EAAAA,EAAAA,KAAA,OAAKL,UAAW,aAAawG,IAAgBrG,SAC1CA,IAIFgG,IACC9F,EAAAA,EAAAA,KAAA,OAAKL,UAAW,4EAA4E0G,IAAkBvG,SAC3GgG,cAUf,OAAOsC,EAAAA,EAAAA,cAAaX,EAAcf,SAASE,KAAK,EAGlD,GAAenG,EAAAA,EAAAA,MAAKiF,E,gDClLpB,SAAS2C,EAAQ9I,EAIdoB,GAAQ,IAJO,MAChBzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,2BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBuH,E,gDCvBlD,SAASC,EAAS/I,EAIfoB,GAAQ,IAJQ,MACjBzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCX,EAAAA,WAAiBwH,E,gDCvBlD,SAASC,EAAOhJ,EAIboB,GAAQ,IAJM,MACfzC,EAAK,QACL0C,KACGC,GACJtB,EACC,OAAoBuB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DxC,GAAIsC,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCX,EAAAA,WAAiByH,E,wICDlD,MA4GA,EA5GkDhJ,IAQ3C,IAR4C,WACjDiJ,EAAU,gBACVC,EAAe,eACfC,EAAc,eACdC,EAAc,iBACdC,EAAgB,MAChB1K,EAAQ,aAAY,QACpB2K,GAAU,GACXtJ,EACC,MAAMuJ,EAAU,CACd,CACEjF,IAAK,KACLrD,MAAO,KACPuI,UAAU,EACVC,OAAS7G,IACPnC,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBG,SAAEqC,KAG7C,CACE0B,IAAK,OACLrD,MAAO,OACPuI,UAAU,EACVC,OAAS7G,IACPnC,EAAAA,EAAAA,KAAA,QAAML,UAAU,4BAA2BG,SAAEqC,KAGjD,CAAE0B,IAAK,cAAerD,MAAO,cAAeuI,UAAU,GACtD,CACElF,IAAK,eACLrD,MAAO,WACPuI,UAAU,EACVC,OAAS7G,IACPnC,EAAAA,EAAAA,KAAA,QAAML,UAAU,cAAaG,SAAEqC,KAGnC,CACE0B,IAAK,SACLrD,MAAO,SACPuI,UAAU,EACVC,OAAS7G,IAELtC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBG,SAAA,CACrB,WAAVqC,GACCnC,EAAAA,EAAAA,KAACiJ,EAAAA,EAAe,CAACtJ,UAAU,iCAE3BK,EAAAA,EAAAA,KAACyF,EAAAA,EAAW,CAAC9F,UAAU,+BAEzBK,EAAAA,EAAAA,KAAA,QAAAF,SAAOqC,EAAM+G,OAAO,GAAGC,cAAgBhH,EAAMiH,MAAM,SAK3D,CAAEvF,IAAK,YAAarD,MAAO,aAAcuI,UAAU,GACnD,CACElF,IAAK,UACLrD,MAAO,UACPwI,OAAQA,CAACK,EAAQC,KACfzJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BG,SAAA,CACzC4I,IACC1I,EAAAA,EAAAA,KAAA,UACEL,UAAU,sEACVgI,QAAUlB,IACRA,EAAEyB,kBACFQ,EAAeY,EAAS,EACxBxJ,UAEFE,EAAAA,EAAAA,KAACuI,EAAAA,EAAO,CAAC5I,UAAU,cAGtBgJ,IACC3I,EAAAA,EAAAA,KAAA,UACEL,UAAU,uEACVgI,QAAUlB,IACRA,EAAEyB,kBACFS,EAAeW,EAAS,EACxBxJ,UAEFE,EAAAA,EAAAA,KAACU,EAAAA,EAAU,CAACf,UAAU,cAGzBiJ,IACC5I,EAAAA,EAAAA,KAAA,UACEL,UAAU,sEACVgI,QAAUlB,IACRA,EAAEyB,kBACFU,EAAiBU,EAAS,EAC1BxJ,UAEFE,EAAAA,EAAAA,KAACsI,EAAAA,EAAS,CAAC3I,UAAU,mBAQjC,OACEK,EAAAA,EAAAA,KAACuJ,EAAAA,EAAS,CACRT,QAASA,EACTjL,KAAM2K,EACNgB,WAAYf,EACZvK,MAAOA,EACPuL,YAAY,EACZZ,QAASA,GACT,E,4BClHN,MAmJA,EAnJwDtJ,IAIjD,IAJkD,SACvDmK,EAAQ,SACRC,EAAQ,UACR/M,GAAY,GACb2C,EACC,MAAOyE,EAAU4F,IAAejN,EAAAA,EAAAA,UAA2B,CACzDkN,KAAM,GACNrK,YAAa,GACbsK,OAAQ,SACRC,sBAAsB,EACtBC,sBAAsB,KAGjBxG,EAAQyG,IAAatN,EAAAA,EAAAA,UAAiC,CAAC,GAExDuN,EAAgBzD,IACpB,MAAM,KAAEoD,EAAI,MAAE1H,GAAUsE,EAAE0D,OAC1BP,GAAYlL,IAAI,IAAUA,EAAM,CAACmL,GAAO1H,MAGpCqB,EAAOqG,IACTI,GAAUvL,IAAI,IAAUA,EAAM,CAACmL,GAAO,MACxC,EAwBF,OACEhK,EAAAA,EAAAA,MAAA,QAAM6J,SATcjD,IACpBA,EAAEe,iBAdqB4C,MACvB,MAAMC,GAAmBhH,EAAAA,EAAAA,GAAa,CACpCwG,KAAM7F,EAAS6F,KACfrK,YAAawE,EAASxE,aACrB,CACDqK,KAAM,CAACtG,EAAAA,GAAgBa,SAAS,8BAChC5E,YAAa,CAAC+D,EAAAA,GAAgBa,SAAS,8BAIzC,OADA6F,EAAUI,GACsC,IAAzCtJ,OAAOuJ,KAAKD,GAAkBtK,MAAY,EAM7CqK,IACFV,EAAS1F,EACX,EAI8BrE,UAAU,YAAWG,SAAA,EACjDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,SAAO0K,QAAQ,OAAO5K,UAAU,0CAAyCG,SAAA,CAAC,kBAC1DE,EAAAA,EAAAA,KAAA,QAAML,UAAU,eAAcG,SAAC,UAE/CE,EAAAA,EAAAA,KAAA,SACE/B,KAAK,OACLK,GAAG,OACHuL,KAAK,OACL1H,MAAO6B,EAAS6F,KAChBW,SAAUN,EACVvK,UAAW,8GACT6D,EAAOqG,KAAO,iBAAmB,MAGpCrG,EAAOqG,OAAQ7J,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4BAA2BG,SAAE0D,EAAOqG,WAGnEhK,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,SAAO0K,QAAQ,cAAc5K,UAAU,0CAAyCG,SAAA,CAAC,gBACnEE,EAAAA,EAAAA,KAAA,QAAML,UAAU,eAAcG,SAAC,UAE7CE,EAAAA,EAAAA,KAAA,YACE1B,GAAG,cACHuL,KAAK,cACLY,KAAM,EACNtI,MAAO6B,EAASxE,YAChBgL,SAAUN,EACVvK,UAAW,8GACT6D,EAAOhE,YAAc,iBAAmB,MAG3CgE,EAAOhE,cAAeQ,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4BAA2BG,SAAE0D,EAAOhE,kBAG1EK,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAOuK,QAAQ,SAAS5K,UAAU,0CAAyCG,SAAC,YAG5ED,EAAAA,EAAAA,MAAA,UACEvB,GAAG,SACHuL,KAAK,SACL1H,MAAO6B,EAAS8F,OAChBU,SAAUN,EACVvK,UAAU,4GAA2GG,SAAA,EAErHE,EAAAA,EAAAA,KAAA,UAAQmC,MAAM,SAAQrC,SAAC,YACvBE,EAAAA,EAAAA,KAAA,UAAQmC,MAAM,WAAUrC,SAAC,oBAI7BD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBG,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAAF,UACED,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBG,SAAA,EAClCE,EAAAA,EAAAA,KAAA,SACE/B,KAAK,WACL4L,KAAK,uBACLa,QAAS1G,EAAS+F,qBAClBS,SAAW/D,GAAMmD,GAAYlL,IAAI,IAAUA,EAAMqL,qBAAsBtD,EAAE0D,OAAOO,YAChF/K,UAAU,6DAEZK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BG,SAAC,kCAGjDE,EAAAA,EAAAA,KAAA,OAAAF,UACED,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBG,SAAA,EAClCE,EAAAA,EAAAA,KAAA,SACE/B,KAAK,WACL4L,KAAK,uBACLa,QAAS1G,EAASgG,qBAClBQ,SAAW/D,GAAMmD,GAAYlL,IAAI,IAAUA,EAAMsL,qBAAsBvD,EAAE0D,OAAOO,YAChF/K,UAAU,6DAEZK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BG,SAAC,wCAMrDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BG,SAAA,EACzCE,EAAAA,EAAAA,KAAC2K,EAAAA,EAAM,CACL1M,KAAK,SACL2M,QAAQ,UACRjD,QAASgC,EACTkB,SAAUjO,EAAUkD,SACrB,YAGDE,EAAAA,EAAAA,KAAC2K,EAAAA,EAAM,CACL1M,KAAK,SACL4K,QAASjM,EAAUkD,SACpB,sBAIE,E,2CCrJJ,MA0EP,EA1E6B,CAI3BgL,cAAelN,UACb,IACE,MAAMmN,QAAiBC,EAAAA,EAAUC,IAAgB,cAAe,CAAElN,WAClE,OAAOmN,EAAAA,GAAmBC,QAAQJ,EAAU,aAC9C,CAAE,MAAOjO,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,GAMFuO,gBAAiBzN,UACf,IACE,MAAMmN,QAAiBC,EAAAA,EAAUC,IAAc,eAAe3M,KAC9D,OAAO4M,EAAAA,GAAmB7M,QAAQ0M,EAAU,WAAYzM,EAC1D,CAAE,MAAOxB,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,GAMFwO,eAAgB1N,UACd,IACE,MAAMmN,QAAiBC,EAAAA,EAAUO,KAAe,cAAeC,GAC/D,OAAON,EAAAA,GAAmBzM,OAAOsM,EAAU,WAC7C,CAAE,MAAOjO,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,GAMF2O,eAAgB7N,MAAOU,EAAYkN,KACjC,IACE,MAAMT,QAAiBC,EAAAA,EAAUU,IAAc,eAAepN,IAAMkN,GACpE,OAAON,EAAAA,GAAmBrM,OAAOkM,EAAU,WAAYzM,EACzD,CAAE,MAAOxB,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,GAMF6O,eAAgB/N,UACd,IACE,MAAMmN,QAAiBC,EAAAA,EAAU/L,OAAO,eAAeX,KACvD,OAAO4M,EAAAA,GAAmBjM,OAAO8L,EAAU,WAAYzM,EACzD,CAAE,MAAOxB,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,GAMF8O,iBAAkBhO,UAChB,IACE,MAAMmN,QAAiBC,EAAAA,EAAUC,IAAgB,cAAe,CAAElN,OAAQ,CAAE8N,cAC5E,OAAOX,EAAAA,GAAmBC,QAAQJ,EAAU,iBAAiB,EAC/D,CAAE,MAAOjO,GACP,MAAMsO,EAAAA,EAAAA,IAAetO,EACvB,ICrCJ,EAjC6B,WAAuC,IAAtCN,EAAO6H,UAAAtE,OAAA,QAAAqC,IAAAiC,UAAA,GAAAA,UAAA,GAAG,CAAElF,cAAc,GACtD,MAAM2M,GAAWxP,EAAAA,EAAAA,GAAc,CAC7BwB,OAAQiO,EAAcjB,cACtBzM,QAAS0N,EAAcV,gBACvB5M,OAAQsN,EAAcT,eACtBzM,OAAQkN,EAAcN,eACtBxM,OAAQ8M,EAAcJ,gBACrB,CACDrO,WAAY,aACZ6B,aAAc3C,EAAQ2C,eAMlB6M,GAAuBrO,EAAAA,EAAAA,cAAY,IAG/BmO,EAASrP,SAAwBqC,KAAIwK,IAAQ,IAChDA,EACH2C,cAAe3C,EAAS2C,eAAiB,QAE1C,CAACH,EAASrP,WAEb,MAAO,IACFqP,EACHtD,WAAYsD,EAASrP,SACrByP,gBAAiBJ,EAASpO,cAC1B2N,gBAAiBS,EAAS1N,cAC1B4N,uBAEJ,E", "sources": ["hooks/useEntityData.ts", "components/layout/PageHeader.tsx", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "utils/validation.ts", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "features/categories/components/CategoryList.tsx", "features/categories/components/AddCategoryForm.tsx", "features/categories/api/categoriesApi.ts", "features/categories/hooks/useCategories.ts"], "sourcesContent": ["import { useState, useCallback, useEffect, useRef } from 'react';\r\nimport useNotification from './useNotification';\r\n\r\nexport interface EntityApi<T, IdType = string> {\r\n  getAll: (params?: any) => Promise<T[]>;\r\n  getById: (id: IdType) => Promise<T>;\r\n  create: (data: any) => Promise<T>;\r\n  update: (id: IdType, data: any) => Promise<T>;\r\n  delete: (id: IdType) => Promise<void>;\r\n}\r\n\r\nexport interface UseEntityDataOptions {\r\n  entityName: string;\r\n  initialFetch?: boolean;\r\n}\r\n\r\nexport const useEntityData = <T, IdType = string>(\r\n  apiService: EntityApi<T, IdType>,\r\n  options: UseEntityDataOptions\r\n) => {\r\n  const [entities, setEntities] = useState<T[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use refs to store current values and avoid stale closures\r\n  const apiServiceRef = useRef(apiService);\r\n  const showNotificationRef = useRef(showNotification);\r\n  const entityNameRef = useRef(options.entityName);\r\n  const hasInitialFetched = useRef(false);\r\n\r\n  // Update refs when values change\r\n  useEffect(() => {\r\n    apiServiceRef.current = apiService;\r\n    showNotificationRef.current = showNotification;\r\n    entityNameRef.current = options.entityName;\r\n  });\r\n\r\n  const fetchEntities = useCallback(async (params?: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await apiServiceRef.current.getAll(params);\r\n      setEntities(data);\r\n      return data;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const getEntityById = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const entity = await apiServiceRef.current.getById(id);\r\n      return entity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const createEntity = useCallback(async (data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newEntity = await apiServiceRef.current.create(data);\r\n      setEntities(prev => [...prev, newEntity]);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} created successfully`\r\n      });\r\n      return newEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to create ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateEntity = useCallback(async (id: IdType, data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedEntity = await apiServiceRef.current.update(id, data);\r\n      setEntities(prev => prev.map(entity =>\r\n        (entity as any).id === id ? updatedEntity : entity\r\n      ));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} updated successfully`\r\n      });\r\n      return updatedEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to update ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteEntity = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      await apiServiceRef.current.delete(id);\r\n      setEntities(prev => prev.filter(entity => (entity as any).id !== id));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} deleted successfully`\r\n      });\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to delete ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Initial fetch effect - runs only once\r\n  useEffect(() => {\r\n    if (options.initialFetch !== false && !hasInitialFetched.current) {\r\n      console.log(`[useEntityData] Starting initial fetch for ${options.entityName}`);\r\n      hasInitialFetched.current = true;\r\n\r\n      const initialFetch = async () => {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        try {\r\n          console.log(`[useEntityData] Calling API for ${options.entityName}`);\r\n          const data = await apiService.getAll();\r\n          console.log(`[useEntityData] Received data for ${options.entityName}:`, data);\r\n          setEntities(data);\r\n        } catch (err) {\r\n          const error = err as Error;\r\n          console.error(`[useEntityData] Error fetching ${options.entityName}:`, error);\r\n          setError(error);\r\n          showNotificationRef.current({\r\n            type: 'error',\r\n            title: 'Error',\r\n            message: `Failed to fetch ${options.entityName}`\r\n          });\r\n        } finally {\r\n          console.log(`[useEntityData] Finished fetch for ${options.entityName}`);\r\n          setIsLoading(false);\r\n        }\r\n      };\r\n\r\n      initialFetch();\r\n    }\r\n  }, [\r\n    apiService,\r\n    options.entityName,\r\n    options.initialFetch\r\n  ]); // Empty dependency array - runs only once on mount\r\n\r\n  return {\r\n    entities,\r\n    isLoading,\r\n    error,\r\n    fetchEntities,\r\n    getEntityById,\r\n    createEntity,\r\n    updateEntity,\r\n    deleteEntity,\r\n    setEntities // Expose setEntities for custom state updates\r\n  };\r\n};\r\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "/**\r\n * Category List Component\r\n * \r\n * This component displays a list of categories in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport type{ Category } from '../types/index';\r\nimport { \r\n  CheckCircleIcon, \r\n  XCircleIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  EyeIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface CategoryListProps {\r\n  categories: Category[];\r\n  onCategoryClick: (category: Category) => void;\r\n  onViewCategory?: (category: Category) => void;\r\n  onEditCategory?: (category: Category) => void;\r\n  onDeleteCategory?: (category: Category) => void;\r\n  title?: string;\r\n  loading?: boolean;\r\n}\r\n\r\nconst CategoryList: React.FC<CategoryListProps> = ({\r\n  categories,\r\n  onCategoryClick,\r\n  onViewCategory,\r\n  onEditCategory,\r\n  onDeleteCategory,\r\n  title = 'Categories',\r\n  loading = false\r\n}) => {\r\n  const columns = [\r\n    { \r\n      key: 'id', \r\n      label: 'ID', \r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"text-xs text-gray-500\">{value}</span>\r\n      )\r\n    },\r\n    { \r\n      key: 'name', \r\n      label: 'Name', \r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"font-medium text-gray-900\">{value}</span>\r\n      )\r\n    },\r\n    { key: 'description', label: 'Description', sortable: true },\r\n    { \r\n      key: 'productCount', \r\n      label: 'Products', \r\n      sortable: true,\r\n      render: (value: number) => (\r\n        <span className=\"font-medium\">{value}</span>\r\n      )\r\n    },\r\n    { \r\n      key: 'status', \r\n      label: 'Status', \r\n      sortable: true,\r\n      render: (value: string) => {\r\n        return (\r\n          <div className=\"flex items-center\">\r\n            {value === 'active' ? (\r\n              <CheckCircleIcon className=\"w-4 h-4 text-green-500 mr-1\" />\r\n            ) : (\r\n              <XCircleIcon className=\"w-4 h-4 text-red-500 mr-1\" />\r\n            )}\r\n            <span>{value.charAt(0).toUpperCase() + value.slice(1)}</span>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n    { key: 'createdAt', label: 'Created At', sortable: true },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: any, category: Category) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          {onViewCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onViewCategory(category);\r\n              }}\r\n            >\r\n              <EyeIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n          {onEditCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onEditCategory(category);\r\n              }}\r\n            >\r\n              <PencilIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n          {onDeleteCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onDeleteCategory(category);\r\n              }}\r\n            >\r\n              <TrashIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      columns={columns}\r\n      data={categories}\r\n      onRowClick={onCategoryClick}\r\n      title={title}\r\n      pagination={true}\r\n      loading={loading}\r\n    />\r\n  );\r\n};\r\n\r\nexport default CategoryList;\r\n", "/**\r\n * Add Category Form Component\r\n * \r\n * This component provides a form for adding new categories.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport type { CategoryFormData } from '../types/index';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\n\r\ninterface AddCategoryFormProps {\r\n  onSubmit: (categoryData: CategoryFormData) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddCategoryForm: React.FC<AddCategoryFormProps> = ({\r\n  onSubmit,\r\n  onCancel,\r\n  isLoading = false\r\n}) => {\r\n  const [formData, setFormData] = useState<CategoryFormData>({\r\n    name: '',\r\n    description: '',\r\n    status: 'active',\r\n    visibleInSupplierApp: true,\r\n    visibleInCustomerApp: true\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n    \r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const validateFormData = () => {\r\n    const validationErrors = validateForm({\r\n      name: formData.name,\r\n      description: formData.description\r\n    }, {\r\n      name: [validationRules.required('Category name is required')],\r\n      description: [validationRules.required('Description is required')]\r\n    });\r\n\r\n    setErrors(validationErrors);\r\n    return Object.keys(validationErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (validateFormData()) {\r\n      onSubmit(formData);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n            Category Name <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"name\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleChange}\r\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n              errors.name ? 'border-red-300' : ''\r\n            }`}\r\n          />\r\n          {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\r\n            Description <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            rows={3}\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n              errors.description ? 'border-red-300' : ''\r\n            }`}\r\n          />\r\n          {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\r\n            Status\r\n          </label>\r\n          <select\r\n            id=\"status\"\r\n            name=\"status\"\r\n            value={formData.status}\r\n            onChange={handleChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\r\n          >\r\n            <option value=\"active\">Active</option>\r\n            <option value=\"inactive\">Inactive</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                name=\"visibleInSupplierApp\"\r\n                checked={formData.visibleInSupplierApp}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInSupplierApp: e.target.checked }))}\r\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n              />\r\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Supplier App</span>\r\n            </label>\r\n          </div>\r\n          <div>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                name=\"visibleInCustomerApp\"\r\n                checked={formData.visibleInCustomerApp}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInCustomerApp: e.target.checked }))}\r\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n              />\r\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Customer App</span>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button \r\n          type=\"button\" \r\n          variant=\"outline\" \r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button \r\n          type=\"submit\" \r\n          loading={isLoading}\r\n        >\r\n          Add Category\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddCategoryForm;\r\n", "/**\r\n * Categories API Service\r\n * \r\n * This file provides methods for interacting with the categories API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { Category, CategoryFormData } from '../types';\r\n\r\nexport const categoriesApi = {\r\n  /**\r\n   * Get all categories\r\n   */\r\n  getCategories: async (params?: Record<string, any>): Promise<Category[]> => {\r\n    try {\r\n      const response = await apiClient.get<Category[]>('/categories', { params });\r\n      return responseValidators.getList(response, 'categories');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a category by ID\r\n   */\r\n  getCategoryById: async (id: string): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.get<Category>(`/categories/${id}`);\r\n      return responseValidators.getById(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new category\r\n   */\r\n  createCategory: async (categoryData: CategoryFormData): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.post<Category>('/categories', categoryData);\r\n      return responseValidators.create(response, 'category');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a category\r\n   */\r\n  updateCategory: async (id: string, categoryData: Partial<CategoryFormData>): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.put<Category>(`/categories/${id}`, categoryData);\r\n      return responseValidators.update(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a category\r\n   */\r\n  deleteCategory: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(`/categories/${id}`);\r\n      return responseValidators.delete(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get subcategories for a parent category\r\n   */\r\n  getSubcategories: async (parentId: string): Promise<Category[]> => {\r\n    try {\r\n      const response = await apiClient.get<Category[]>('/categories', { params: { parentId } });\r\n      return responseValidators.getList(response, 'subcategories', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default categoriesApi;\r\n", "/**\r\n * Categories Hook\r\n * \r\n * This hook provides methods and state for working with categories.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\nimport { useEntityData } from '../../../hooks/useEntityData';\r\nimport categoriesApi from '../api/categoriesApi';\r\nimport type { Category } from '../types';\r\n\r\nexport const useCategories = (options = { initialFetch: true }) => {\r\n  const baseHook = useEntityData({\r\n    getAll: categoriesApi.getCategories,\r\n    getById: categoriesApi.getCategoryById,\r\n    create: categoriesApi.createCategory,\r\n    update: categoriesApi.updateCategory,\r\n    delete: categoriesApi.deleteCategory\r\n  }, {\r\n    entityName: 'categories',\r\n    initialFetch: options.initialFetch\r\n  });\r\n  \r\n  // Note: useNotification is available through useEntityData if needed\r\n  \r\n  // Category-specific methods\r\n  const getCategoryHierarchy = useCallback(() => {\r\n    // In the new hierarchy, all categories are top-level\r\n    // Subcategories are now embedded within categories\r\n    return (baseHook.entities as Category[]).map(category => ({\r\n      ...category,\r\n      subcategories: category.subcategories || []\r\n    }));\r\n  }, [baseHook.entities]);\r\n  \r\n  return {\r\n    ...baseHook,\r\n    categories: baseHook.entities as Category[],\r\n    fetchCategories: baseHook.fetchEntities,\r\n    getCategoryById: baseHook.getEntityById,\r\n    getCategoryHierarchy\r\n  };\r\n};\r\n\r\nexport default useCategories;\r\n\r\n\r\n\r\n"], "names": ["useEntityData", "apiService", "options", "entities", "setEntities", "useState", "isLoading", "setIsLoading", "error", "setError", "showNotification", "useNotification", "apiServiceRef", "useRef", "showNotificationRef", "entityNameRef", "entityName", "hasInitialFetched", "useEffect", "current", "fetchEntities", "useCallback", "async", "data", "getAll", "params", "err", "type", "title", "message", "getEntityById", "getById", "id", "createEntity", "newEntity", "create", "prev", "updateEntity", "updatedEntity", "update", "map", "entity", "deleteEntity", "delete", "filter", "initialFetch", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "description", "actions", "breadcrumbs", "className", "testId", "_jsxs", "children", "length", "_jsx", "Link", "to", "HomeIcon", "item", "index", "ChevronRightIcon", "path", "label", "memo", "PencilIcon", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "value", "undefined", "trim", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "validateForm", "values", "validationRules", "errors", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "formData", "ruleArray", "rule", "validator", "required", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "XCircleIcon", "Modal", "isOpen", "onClose", "size", "footer", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "modalRef", "handleEscape", "e", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "onClick", "xs", "sm", "md", "lg", "xl", "full", "stopPropagation", "XMarkIcon", "createPortal", "PlusIcon", "TrashIcon", "EyeIcon", "categories", "onCategoryClick", "onViewCategory", "onEditCategory", "onDeleteCategory", "loading", "columns", "sortable", "render", "CheckCircleIcon", "char<PERSON>t", "toUpperCase", "slice", "_", "category", "DataTable", "onRowClick", "pagination", "onSubmit", "onCancel", "setFormData", "name", "status", "visibleInSupplierApp", "visibleInCustomerApp", "setErrors", "handleChange", "target", "validateFormData", "validationErrors", "keys", "htmlFor", "onChange", "rows", "checked", "<PERSON><PERSON>", "variant", "disabled", "getCategories", "response", "apiClient", "get", "responseValidators", "getList", "handleApiError", "getCategoryById", "createCategory", "post", "categoryData", "updateCategory", "put", "deleteCategory", "getSubcategories", "parentId", "baseHook", "categoriesApi", "getCategoryHierarchy", "subcategories", "fetchCategories"], "sourceRoot": ""}