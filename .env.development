# Development Environment Configuration
# This file is used when running: npm start

# API Configuration
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_USE_MOCK_API=false
REACT_APP_ENVIRONMENT=development

# Authentication Configuration
REACT_APP_AUTH_TOKEN_KEY=connectchain_auth_token
REACT_APP_USER_DATA_KEY=connectchain_user_data

# Feature Flags
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_LOGGING=true

# Development specific settings
REACT_APP_ENABLE_DEVTOOLS=true
