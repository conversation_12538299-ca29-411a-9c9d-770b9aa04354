{"version": 3, "file": "static/js/992.f3b49cff.chunk.js", "mappings": "yJAgBO,MAAMA,EAAgBA,CAC3BC,EACAC,KAEA,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,KACvCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEK,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAgBC,EAAAA,EAAAA,QAAOZ,GACvBa,GAAsBD,EAAAA,EAAAA,QAAOH,GAC7BK,GAAgBF,EAAAA,EAAAA,QAAOX,EAAQc,YAC/BC,GAAoBJ,EAAAA,EAAAA,SAAO,IAGjCK,EAAAA,EAAAA,YAAU,KACRN,EAAcO,QAAUlB,EACxBa,EAAoBK,QAAUT,EAC9BK,EAAcI,QAAUjB,EAAQc,UAAU,IAG5C,MAAMI,GAAgBC,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IACE,MAAMc,QAAaX,EAAcO,QAAQK,OAAOC,GAEhD,OADArB,EAAYmB,GACLA,CACT,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGuB,GAAgBT,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IAEE,aADqBG,EAAcO,QAAQY,QAAQC,EAErD,CAAE,MAAON,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG0B,GAAeZ,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,IACE,MAAMyB,QAAkBtB,EAAcO,QAAQgB,OAAOZ,GAOrD,OANAnB,GAAYgC,GAAQ,IAAIA,EAAMF,KAC9BpB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBe,CACT,CAAE,MAAOR,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG8B,GAAehB,EAAAA,EAAAA,cAAYC,MAAOU,EAAYT,KAClDhB,GAAa,GACbE,EAAS,MACT,IACE,MAAM6B,QAAsB1B,EAAcO,QAAQoB,OAAOP,EAAIT,GAS7D,OARAnB,GAAYgC,GAAQA,EAAKI,KAAIC,GAC1BA,EAAeT,KAAOA,EAAKM,EAAgBG,MAE9C3B,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBmB,CACT,CAAE,MAAOZ,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGmC,GAAerB,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,UACQG,EAAcO,QAAQwB,OAAOX,GACnC5B,GAAYgC,GAAQA,EAAKQ,QAAOH,GAAWA,EAAeT,KAAOA,MACjElB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,gCAE9B,CAAE,MAAOO,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAuCH,OApCAW,EAAAA,EAAAA,YAAU,KACR,IAA6B,IAAzBhB,EAAQ2C,eAA2B5B,EAAkBE,QAAS,CAChE2B,QAAQC,IAAI,8CAA8C7C,EAAQc,cAClEC,EAAkBE,SAAU,EAE5B,MAAM0B,EAAevB,UACnBf,GAAa,GACbE,EAAS,MACT,IACEqC,QAAQC,IAAI,mCAAmC7C,EAAQc,cACvD,MAAMO,QAAatB,EAAWuB,SAC9BsB,QAAQC,IAAI,qCAAqC7C,EAAQc,cAAeO,GACxEnB,EAAYmB,EACd,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EACdoB,QAAQtC,MAAM,kCAAkCN,EAAQc,cAAeR,GACvEC,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmB3B,EAAQc,cAExC,CAAC,QACC8B,QAAQC,IAAI,sCAAsC7C,EAAQc,cAC1DT,GAAa,EACf,GAGFsC,GACF,IACC,CACD5C,EACAC,EAAQc,WACRd,EAAQ2C,eAGH,CACL1C,WACAG,YACAE,QACAY,gBACAU,gBACAG,eACAI,eACAK,eACAtC,cACD,C,sDCrMH,MAWA,EAX8C4C,IAGvC,IAHwC,SAC7CC,EAAQ,UACRC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,MAAID,UAAW,kCAAkCA,IAAYD,SAC1DA,GACE,C,gDCdT,SAASG,EAASJ,EAIfK,GAAQ,IAJQ,MACjBzB,EAAK,QACL0B,KACGC,GACJP,EACC,OAAoBQ,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3B,EAAqB4B,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACH1B,GAAS,KAAmB4B,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCX,EAAAA,WAAiBJ,E,mECDlD,MAAMgB,EAA0BC,IAC9B,IAAKb,EAAAA,eAAqBa,GAAO,MAAO,2BAGxC,MAGMC,GAHYD,EAAKd,MAAML,WAAa,IAGbqB,MAAM,kBACnC,GAAID,EAAY,CAEd,MAAO,MADOA,EAAW,OAE3B,CAEA,MAAO,0BAA0B,EAkDnC,EA/C8CtB,IAKvC,IALwC,MAC7CpB,EAAK,KACLL,EAAI,KACJ8C,EAAI,YACJG,EAAeC,GAAUA,EAAMC,YAChC1B,EACC,OACEG,EAAAA,EAAAA,KAACwB,EAAAA,EAAI,CAAA1B,UACH2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,oBAAoBkB,EAAuBC,KAAQpB,SAEhEO,EAAAA,eAAqBa,GACpB,MACE,MAAMQ,EAAcR,EAEdC,GADoBO,EAAYtB,MAAML,WAAa,IACpBqB,MAAM,mBACrCO,EAAaR,EAAaA,EAAW,GAAK,gBAGhD,OAAOd,EAAAA,aAAmBqB,EAAa,CACrC3B,UAAW,WAAgB4B,KAE9B,EAVD,GAYAT,KAGJO,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,cAAaD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oCAAmCD,SAAErB,KAClDgD,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,sBAAqBD,SAAA,EAClCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAChDuB,EAAYjD,EAAKwD,cAEHC,IAAhBzD,EAAK0D,SACJL,EAAAA,EAAAA,MAAA,KAAG1B,UAAW,mDACZ3B,EAAK0D,QAAU,EAAI,iBAAmB,gBACrChC,SAAA,CACA1B,EAAK0D,QAAU,EAAI,IAAM,GAAI1D,EAAK0D,OAAOC,QAAQ,GAAG,iBAM1D,ECvCX,EArBsDlC,IAG/C,IAHgD,QACrDmC,EAAO,UACPjC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yCAAyCA,IAAYD,SAClEkC,EAAQ3C,KAAI,CAAC4C,EAAQC,KACpBlC,EAAAA,EAAAA,KAACmC,EAAU,CAET1D,MAAOwD,EAAOxD,MACdL,KAAM,CACJwD,MAA+B,kBAAjBK,EAAOX,MAAqBc,WAAWH,EAAOX,QAAU,EAAIW,EAAOX,MACjFQ,OAAQG,EAAOI,QAAU,GAE3BnB,KAAMe,EAAOf,MANRgB,MASL,C,gDCtCV,SAASI,EAAUzC,EAIhBK,GAAQ,IAJS,MAClBzB,EAAK,QACL0B,KACGC,GACJP,EACC,OAAoBQ,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3B,EAAqB4B,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACH1B,GAAS,KAAmB4B,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCX,EAAAA,WAAiBiC,E,uDCflD,MAaA,EAb8CzC,IAIvC,IAJwC,MAC7C0C,EAAK,MACLjB,EAAK,UACLvB,EAAY,IACbF,EACC,OACE4B,EAAAA,EAAAA,MAAA,OAAK1B,UAAW,wDAAwDA,IAAYD,SAAA,EAClFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEyC,KACnDvC,EAAAA,EAAAA,KAAA,MAAID,UAAU,mDAAkDD,SAAEwB,MAC9D,C,uDCDV,MA+FA,EA/F4CzB,IAarC,IAbsC,MAC3C0C,EAAK,KACLC,EAAI,KACJhE,EAAO,OAAM,MACb8C,EAAK,SACLmB,EAAQ,MACRpF,EAAK,SACLqF,GAAW,EAAK,YAChBC,EAAc,GAAE,QAChB5F,EAAU,GAAE,UACZgD,EAAY,GAAE,SACd6C,GAAW,EAAK,QAChBC,GAAU,GACXhD,EACC,MAAMiD,EAAe,sDACnBzF,EAAQ,yDAA2D,2DAqErE,OACEoE,EAAAA,EAAAA,MAAA,OAAK1B,UAAW,GAAGA,IAAYD,SAAA,EAC7B2B,EAAAA,EAAAA,MAAA,SAAOsB,QAASP,EAAMzC,UAAU,0CAAyCD,SAAA,CACtEyC,EAAM,IAAEG,IAAY1C,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,SArEtCkD,MAClB,OAAQxE,GACN,IAAK,WACH,OACEwB,EAAAA,EAAAA,KAAA,YACEnB,GAAI2D,EACJA,KAAMA,EACNlB,MAAOA,EACPmB,SAAUA,EACV1C,UAAW+C,EACXH,YAAaA,EACbC,SAAUA,IAIhB,IAAK,SACH,OACE5C,EAAAA,EAAAA,KAAA,UACEnB,GAAI2D,EACJA,KAAMA,EACNlB,MAAOA,EACPmB,SAAUA,EACV1C,UAAW+C,EACXF,SAAUA,GAAYC,EAAQ/C,SAE7B+C,GACC7C,EAAAA,EAAAA,KAAA,UAAQsB,MAAM,GAAExB,SAAC,eAEjB/C,EAAQsC,KAAI4D,IACVjD,EAAAA,EAAAA,KAAA,UAA2BsB,MAAO2B,EAAO3B,MAAMxB,SAC5CmD,EAAOV,OADGU,EAAO3B,WAQ9B,IAAK,WACH,OACEtB,EAAAA,EAAAA,KAAA,SACExB,KAAK,WACLK,GAAI2D,EACJA,KAAMA,EACNU,QAAS5B,EACTmB,SAAUA,EACV1C,UAAU,kEACV6C,SAAUA,IAIhB,QACE,OACE5C,EAAAA,EAAAA,KAAA,SACExB,KAAMA,EACNK,GAAI2D,EACJA,KAAMA,EACNlB,MAAOA,EACPmB,SAAUA,EACV1C,UAAW+C,EACXH,YAAaA,EACbC,SAAUA,IAGlB,EAQGI,GACA3F,IAAS2C,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEzC,MAChD,C,2CC5FH,MAAM8F,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAOpG,GACP,OAAO,CACT,GAGWsG,EAAcrC,GACX,OAAVA,QAA4BO,IAAVP,IACD,kBAAVA,EAA2BA,EAAMsC,OAAOC,OAAS,GACxDC,MAAMC,QAAQzC,IAAeA,EAAMuC,OAAS,GAYrCG,EAAa1C,GACjB,WAAW+B,KAAK/B,GAGZ2C,EAAa3C,GACjB,sBAAsB+B,KAAK/B,GAGvB4C,EAAkB5C,GACtB,iBAAiB+B,KAAK/B,GAGlB6C,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAASb,OAAS,OAGjB,QAAQR,KAAKqB,OAGb,QAAQrB,KAAKqB,OAGb,QAAQrB,KAAKqB,MAGb,sCAAsCrB,KAAKqB,MAwBrCG,EAAeA,CAC1BC,EACAC,KAEA,MAAMC,EAA2C,CAAC,EAUlD,OARA1E,OAAO2E,QAAQF,GAAiBG,SAAQrF,IAAyB,IAAvBsF,EAAWC,GAAMvF,EACzD,MAAMwF,EAAMF,EACN9H,EA1BmBiI,EAC3BC,EACAjE,EACA8D,EACAI,KAEA,MAAMC,EAAY3B,MAAMC,QAAQqB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMM,KAAQD,EACjB,IAAKC,EAAKC,UAAUrE,EAAOkE,GACzB,OAAOE,EAAKhH,QAIhB,MAAO,EAAE,EAYO4G,CAAcH,EAAWL,EAAOO,GAAMD,EAAON,GACvDzH,IACF2H,EAAOK,GAAOhI,EAChB,IAGK2H,CAAM,EAIFD,EAAkB,CAC7BrC,SAAU,WAA2C,MAAsB,CACzEiD,UAAWhC,EACXjF,QAFwBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAEDxC,MAAO,WAAuD,MAAsB,CAClFuC,UAAWxC,EACXzE,QAFqBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDrC,MAAO,WAAsD,MAAsB,CACjFoC,UAAWrC,EACX5E,QAFqBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAEDnC,IAAK,WAA6C,MAAsB,CACtEkC,UAAWnC,EACX9E,QAFmBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDC,UAAWA,CAACC,EAAapH,KAAgB,CACvCiH,UAAYrE,GA3GSuE,EAACvE,EAAewE,IAChCxE,EAAMuC,QAAUiC,EA0GSD,CAAUvE,EAAOwE,GAC/CpH,QAASA,GAAW,oBAAoBoH,iBAG1CC,UAAWA,CAACC,EAAatH,KAAgB,CACvCiH,UAAYrE,GA5GSyE,EAACzE,EAAe0E,IAChC1E,EAAMuC,QAAUmC,EA2GSD,CAAUzE,EAAO0E,GAC/CtH,QAASA,GAAW,wBAAwBsH,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EN,UAAW3B,EACXtF,QAFuBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAEDM,QAAS,WAAwD,MAAsB,CACrFP,UAAW1B,EACXvF,QAFuBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAEDO,aAAc,WAAwD,MAAsB,CAC1FR,UAAWzB,EACXxF,QAF4BkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDvB,KAAM,WAA8C,MAAsB,CACxEsB,UAAWxB,EACXzF,QAFoBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDlB,SAAU,WAA2H,MAAsB,CACzJiB,UAAWf,EACXlG,QAFwBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAEDQ,cAAe,WAA2C,MAAsB,CAC9ET,UAAWA,CAACrE,EAAekE,IAAmBf,EAAiBnD,EAAe,OAARkE,QAAQ,IAARA,OAAQ,EAARA,EAAUb,iBAChFjG,QAF6BkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAEDS,qBAAsB,WAA2C,MAAsB,CACrFV,UAAWA,CAACrE,EAAekE,IAAmBf,EAAiBnD,EAAe,OAARkE,QAAQ,IAARA,OAAQ,EAARA,EAAUd,UAChFhG,QAFoCkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDU,IAAK,WAA6C,MAAsB,CACtEX,UAAYrE,GAAkB,sBAAsB+B,KAAK/B,GACzD5C,QAFmBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDW,MAAO,WAA+C,MAAsB,CAC1EZ,UAAYrE,GAAkBA,EAAQ,GAAKA,GAAS,OACpD5C,QAFqBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDY,MAAO,WAAwD,MAAsB,CACnFb,UAAYrE,GAAkBmF,OAAOC,UAAUpF,IAAUA,GAAS,EAClE5C,QAFqBkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDe,aAAc,WAA6D,MAAsB,CAC/FhB,UAAYrE,GAAkBmF,OAAOC,UAAUpF,IAAUA,GAAS,EAClE5C,QAF4BkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDgB,iBAAkB,WAAuE,MAAsB,CAC7GjB,UAAWA,CAACgB,EAAsBnB,KAC3BA,IAAaA,EAASgB,OACpBG,GAAgBnB,EAASgB,MAElC9H,QALgCkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDiB,cAAe,WAAkD,MAAsB,CACrFlB,UAAYrE,GAAiBwC,MAAMC,QAAQzC,IAAUA,EAAMuC,OAAS,EACpEnF,QAF6BkH,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAEDkB,WAAY,eAACC,EAAgBnB,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxED,UAAYrE,KACLwC,MAAMC,QAAQzC,IACZA,EAAMuC,QAAUkD,EAEzBrI,SALkDkH,UAAA/B,OAAA,EAAA+B,UAAA,QAAA/D,IAK9B,WAAWkF,mBAChC,E,uDC/NH,MAqBA,EArBoDlH,IAK7C,IAL8C,MACnDpB,EAAK,YACLuI,EAAW,SACXlH,EAAQ,UACRC,EAAY,IACbF,EACC,OACE4B,EAAAA,EAAAA,MAAA,OAAK1B,UAAW,iDAAiDA,IAAYD,SAAA,EAC3E2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAErB,IAC5DuI,IACChH,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAEkH,QAGzDhH,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,SACtCA,MAEC,C,8KChBH,MAAMmH,EAAmB,CAK9BC,iBAAkB/I,UAChB,IACE,MAAMgJ,QAAiBC,EAAAA,EAAUC,IAAoB,mBACrD,OAAOC,EAAAA,GAAmBC,QAAQJ,EAAU,iBAC9C,CAAE,MAAO9J,GACP,MAAMmK,EAAAA,EAAAA,IAAenK,EACvB,GAQFoK,oBAAqBtJ,UACnB,IACE,MAAMgJ,QAAiBC,EAAAA,EAAUC,IAAkB,mBAAmBxI,KACtE,OAAOyI,EAAAA,GAAmB1I,QAAQuI,EAAU,gBAAiBtI,EAC/D,CAAE,MAAOxB,GACP,MAAMmK,EAAAA,EAAAA,IAAenK,EACvB,KAKS,iBACX6J,EAAgB,oBAChBO,GACER,E,aCxBJ,MAiOA,EAjOgDpH,IAAgD,IAA/C,SAAE6H,EAAQ,SAAEC,EAAQ,UAAExK,GAAY,GAAO0C,EACxF,MAAO2F,EAAUoC,IAAe1K,EAAAA,EAAAA,UAAS,CACvCsF,KAAM,GACNY,MAAO,GACP5E,KAAM,WACN+E,MAAO,GACPsE,QAAS,GACTC,aAAc,GACdpD,SAAU,GACVC,gBAAiB,GACjBoD,YAAY,EACZC,MAAO,QAGFhD,EAAQiD,IAAa/K,EAAAA,EAAAA,UAAiC,CAAC,IACvDgL,EAAeC,IAAoBjL,EAAAA,EAAAA,UAAyB,KAC5DkL,EAAsBC,IAA2BnL,EAAAA,EAAAA,WAAS,IAGjEa,EAAAA,EAAAA,YAAU,KACkBI,WACxBkK,GAAwB,GACxB,IACE,MAAMC,QAAcpB,IACpBiB,EAAiBG,EACnB,CAAE,MAAOjL,GACPsC,QAAQtC,MAAM,iCAAkCA,EAElD,CAAC,QACCgL,GAAwB,EAC1B,GAGFE,EAAmB,GAClB,IAEH,MAAMC,EAAgBC,IACpB,MAAM,KAAEjG,EAAI,MAAElB,EAAK,KAAE9C,GAASiK,EAAEC,OAEhC,GAAa,aAATlK,EAAqB,CACvB,MAAM0E,EAAWuF,EAAEC,OAA4BxF,QAC/C0E,GAAY3I,IAAI,IAAUA,EAAM,CAACuD,GAAOU,KAC1C,MACE0E,GAAY3I,IAAI,IAAUA,EAAM,CAACuD,GAAOlB,MAItC0D,EAAOxC,IACTyF,GAAUhJ,IAAI,IAAUA,EAAM,CAACuD,GAAO,MACxC,EAyCF,OACEf,EAAAA,EAAAA,MAAA,QAAMiG,SAdce,IAGpB,GAFAA,EAAEE,iBAjBqBC,MACvB,MAAMC,EAAsB,CAC1BrG,KAAM,CAACuC,EAAAA,GAAgBrC,SAAS,qBAChCU,MAAO,CAAC2B,EAAAA,GAAgBrC,SAAS,qBAAsBqC,EAAAA,GAAgB3B,SACvE5E,KAAM,CAACuG,EAAAA,GAAgBrC,SAAS,0BAChCgC,SAAU,CAACK,EAAAA,GAAgBrC,SAAS,wBAAyBqC,EAAAA,GAAgBL,YAC7EC,gBAAiB,CAACI,EAAAA,GAAgBrC,SAAS,gCAAiCqC,EAAAA,GAAgBqB,iBAC5FyB,QAAS,CAAC9C,EAAAA,GAAgBrC,SAAS,wBACnCoF,aAAc,CAAC/C,EAAAA,GAAgBrC,SAAS,+BAGpCoG,GAAYjE,EAAAA,EAAAA,GAAaW,EAAUqD,GAEzC,OADAZ,EAAUa,GAC+B,IAAlCxI,OAAOyI,KAAKD,GAAWjF,MAAY,EAMtC+E,GAAoB,CACtB,MAAMI,EAAa,IACdxD,EAEHwC,MAAOxC,EAASwC,OAElBN,EAASsB,EACX,GAI8BjJ,UAAU,YAAWD,SAAA,EACjD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,wCAAuCD,SAAA,EACpDE,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,YACNC,KAAK,OACLlB,MAAOkE,EAAShD,KAChBC,SAAU+F,EACVnL,MAAO2H,EAAOxC,KACdE,UAAQ,KAGV1C,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,QACNC,KAAK,QACLhE,KAAK,QACL8C,MAAOkE,EAASpC,MAChBX,SAAU+F,EACVnL,MAAO2H,EAAO5B,MACdV,UAAQ,KAGV1C,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,YACNC,KAAK,OACLhE,KAAK,SACL8C,MAAOkE,EAAShH,KAChBiE,SAAU+F,EACVnL,MAAO2H,EAAOxG,KACdkE,UAAQ,EACR3F,QAAS,CACP,CAAEuE,MAAO,WAAYiB,MAAO,YAC5B,CAAEjB,MAAO,WAAYiB,MAAO,gBAIhCvC,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,eACNC,KAAK,QACLlB,MAAOkE,EAASjC,MAChBd,SAAU+F,EACVnL,MAAO2H,EAAOzB,SAGhBvD,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,UACNC,KAAK,UACLhE,KAAK,WACL8C,MAAOkE,EAASqC,QAChBpF,SAAU+F,EACVnL,MAAO2H,EAAO6C,QACdnF,UAAQ,EACRC,YAAY,wBAGd3C,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,gBACNC,KAAK,eACLhE,KAAK,SACL8C,MAAOkE,EAASsC,aAChBrF,SAAU+F,EACVnL,MAAO2H,EAAO8C,aACdpF,UAAQ,EACRG,QAASuF,EACTrL,QAAS,CACP,CAAEuE,MAAO,GAAIiB,MAAO,2BACjB2F,EAAc7I,KAAIb,IAAI,CACvB8C,MAAO9C,EAAKK,GACZ0D,MAAO/D,EAAKgE,aAKlBxC,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,WACNC,KAAK,WACLhE,KAAK,WACL8C,MAAOkE,EAASd,SAChBjC,SAAU+F,EACVnL,MAAO2H,EAAON,SACdhC,UAAQ,KAGV1C,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,mBACNC,KAAK,kBACLhE,KAAK,WACL8C,MAAOkE,EAASb,gBAChBlC,SAAU+F,EACVnL,MAAO2H,EAAOL,gBACdjC,UAAQ,QAKZ1C,EAAAA,EAAAA,KAACkJ,EAAAA,EAAW,CACV3G,MAAM,kBACNC,KAAK,QACLlB,MAAOkE,EAASwC,MAChBvF,SAzIqB0G,IACzBvB,GAAY3I,IAAI,IAAUA,EAAM+I,MAAOmB,MAGnCnE,EAAOgD,OACTC,GAAUhJ,IAAI,IAAUA,EAAM+I,MAAO,MACvC,EAoII3K,MAAO2H,EAAOgD,YAASnG,EACvBuH,QAAS,QACTC,aAAc,CAAC,aAAc,YAAa,YAAa,iBAGzDrJ,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oBAAmBD,UAChCE,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CACR1G,MAAM,wBACNC,KAAK,aACLhE,KAAK,WACL8C,MAAOkE,EAASuC,WAChBtF,SAAU+F,EACVzI,UAAU,mCAId0B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAM,CACLC,QAAQ,UACRC,QAAS7B,EACT/E,SAAUzF,EAAU2C,SACrB,YAGDE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAM,CACL9K,KAAK,SACLqE,QAAS1F,EAAU2C,SACpB,kBAIE,E,cC/NX,MAmKA,EAnKkDD,IAI3C,IAJ4C,KACjD4J,EAAI,SACJ/B,EAAQ,UACRvK,GAAY,GACb0C,EACC,MAAO2F,EAAUoC,IAAe1K,EAAAA,EAAAA,UAA+B,CAC7DsF,KAAM,GACNY,MAAO,GACP5E,KAAM,cAGDwG,EAAQiD,IAAa/K,EAAAA,EAAAA,UAAiC,CAAC,IAG9Da,EAAAA,EAAAA,YAAU,KACJ0L,GACF7B,EAAY,CACVpF,KAAMiH,EAAKjH,KACXY,MAAOqG,EAAKrG,MACZ5E,KAAMiL,EAAKjL,MAEf,GACC,CAACiL,IAEJ,MAAMjB,EAAgBC,IACpB,MAAM,KAAEjG,EAAI,MAAElB,EAAK,KAAE9C,GAASiK,EAAEC,OAEhC,GAAa,aAATlK,EAAqB,CACvB,MAAM0E,EAAWuF,EAAEC,OAA4BxF,QAC/C0E,GAAY3I,IAAI,IAAUA,EAAM,CAACuD,GAAOU,KAC1C,MACE0E,GAAY3I,IAAI,IAAUA,EAAM,CAACuD,GAAOlB,MAItC0D,EAAOxC,IACTyF,GAAUhJ,IAAI,IAAUA,EAAM,CAACuD,GAAO,MACxC,EA6BF,OACExC,EAAAA,EAAAA,KAACwB,EAAAA,EAAI,CAAA1B,UACH2B,EAAAA,EAAAA,MAAA,QAAMiG,SAhBWvJ,UAGnB,GAFAsK,EAAEE,iBAbqBC,MACvB,MAAMC,EAAsB,CAC1BrG,KAAM,CAACuC,EAAAA,GAAgBrC,SAAS,qBAChCU,MAAO,CAAC2B,EAAAA,GAAgBrC,SAAS,qBAAsBqC,EAAAA,GAAgB3B,SACvE5E,KAAM,CAACuG,EAAAA,GAAgBrC,SAAS,2BAG5BoG,GAAYjE,EAAAA,EAAAA,GAAaW,EAAUqD,GAEzC,OADAZ,EAAUa,GAC+B,IAAlCxI,OAAOyI,KAAKD,GAAWjF,MAAY,EAMtC+E,GACF,UACQlB,EAASlC,EAEjB,CAAE,MAAOnI,GAEPsC,QAAQtC,MAAM,yBAA0BA,EAC1C,CACF,EAKgC0C,UAAU,gBAAeD,SAAA,EACrD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,wCAAuCD,SAAA,EACpD2B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACE2B,EAAAA,EAAAA,MAAA,SAAOsB,QAAQ,OAAOhD,UAAU,0CAAyCD,SAAA,CAAC,cAC9DE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAE3CE,EAAAA,EAAAA,KAAA,SACExB,KAAK,OACLK,GAAG,OACH2D,KAAK,OACLlB,MAAOkE,EAAShD,KAChBC,SAAU+F,EACVzI,UAAW,8GACTiF,EAAa,KAAI,iBAAmB,MAGvCA,EAAa,OAAKhF,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEkF,EAAa,WAG5EvD,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACE2B,EAAAA,EAAAA,MAAA,SAAOsB,QAAQ,QAAQhD,UAAU,0CAAyCD,SAAA,CAAC,UACnEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAEvCE,EAAAA,EAAAA,KAAA,SACExB,KAAK,QACLK,GAAG,QACH2D,KAAK,QACLlB,MAAOkE,EAASpC,MAChBX,SAAU+F,EACVzI,UAAW,8GACTiF,EAAc,MAAI,iBAAmB,MAGxCA,EAAc,QAAKhF,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEkF,EAAc,YAG9EvD,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACE2B,EAAAA,EAAAA,MAAA,SAAOsB,QAAQ,OAAOhD,UAAU,0CAAyCD,SAAA,CAAC,cAC9DE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAE3C2B,EAAAA,EAAAA,MAAA,UACE5C,GAAG,OACH2D,KAAK,OACLlB,MAAOkE,EAAShH,KAChBiE,SAAU+F,EACVzI,UAAW,8GACTiF,EAAa,KAAI,iBAAmB,IACnClF,SAAA,EAEHE,EAAAA,EAAAA,KAAA,UAAQsB,MAAM,WAAUxB,SAAC,cACzBE,EAAAA,EAAAA,KAAA,UAAQsB,MAAM,WAAUxB,SAAC,gBAE1BkF,EAAa,OAAKhF,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEkF,EAAa,WAG5EvD,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAO+C,QAAQ,WAAWhD,UAAU,0CAAyCD,SAAC,kBAG9EE,EAAAA,EAAAA,KAAA,SACExB,KAAK,WACLK,GAAG,WACH2D,KAAK,WACLlB,MAAOkE,EAASd,UAAY,GAC5BjC,SAAU+F,EACV7F,YAAY,uCACZ5C,UAAW,8GACTiF,EAAiB,SAAI,iBAAmB,MAG3CA,EAAiB,WAAKhF,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEkF,EAAiB,kBAItFvD,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAM,CACLC,QAAQ,UACR/K,KAAK,SACLgL,QAASA,IAAME,OAAOC,QAAQC,OAC9BhH,SAAUzF,EAAU2C,SACrB,YAGDE,EAAAA,EAAAA,KAACsJ,EAAAA,EAAM,CACL9K,KAAK,SACLqE,QAAS1F,EAAU2C,SACpB,wBAKA,E,yEChKX,MA8EA,EA9EoDD,IAO7C,IAP8C,OACnDgK,EAAM,MACNpL,EAAQ,SAAQ,YAChBuI,EAAc,gBAAe,YAC7B8C,EAAW,aACXC,EAAe,kBAAiB,UAChChK,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAACgK,EAAAA,EAAa,CACZvL,MAAOA,EACPuI,YAAaA,EACbjH,UAAWA,EAAUD,SAEF,IAAlB+J,EAAOhG,QACN7D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8CAA6CD,SACzDiK,KAGH/J,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC9B2B,EAAAA,EAAAA,MAAA,SAAO1B,UAAU,sCAAqCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,UAC3B2B,EAAAA,EAAAA,MAAA,MAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIiK,MAAM,MAAMlK,UAAU,iFAAgFD,SAAC,cAG3GE,EAAAA,EAAAA,KAAA,MAAIiK,MAAM,MAAMlK,UAAU,iFAAgFD,SAAC,UAG3GE,EAAAA,EAAAA,KAAA,MAAIiK,MAAM,MAAMlK,UAAU,iFAAgFD,SAAC,YAG3GE,EAAAA,EAAAA,KAAA,MAAIiK,MAAM,MAAMlK,UAAU,iFAAgFD,SAAC,YAG3GE,EAAAA,EAAAA,KAAA,MAAIiK,MAAM,MAAMlK,UAAU,kFAAiFD,SAAC,kBAKhHE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,oCAAmCD,SACjD+J,EAAOxK,KAAK6K,IACXzI,EAAAA,EAAAA,MAAA,MAAmB1B,UAAU,mBAAkBD,SAAA,EAC7CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,+DAA8DD,SACzEoK,EAAMrL,MAETmB,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,UAC9DqK,EAAAA,EAAAA,IAAWD,EAAME,cAEpBpK,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,UAC9DuK,EAAAA,EAAAA,IAAeH,EAAMI,gBAExBtK,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCE,EAAAA,EAAAA,KAACuK,EAAAA,EAAW,CAACC,OAAQN,EAAMM,OAAQhM,KAAK,aAE1CwB,EAAAA,EAAAA,KAAA,MAAID,UAAU,6DAA4DD,SACvEgK,IACC9J,EAAAA,EAAAA,KAACsJ,EAAAA,EAAM,CACLC,QAAQ,OACRkB,KAAK,KACLjB,QAASA,IAAMM,EAAYI,GAC3BhJ,MAAMlB,EAAAA,EAAAA,KAAC0K,EAAAA,EAAO,CAAC3K,UAAU,uBACzBA,UAAU,mDAAkDD,SAC7D,aArBEoK,EAAMrL,cAgCX,E,aCrEpB,MAiCA,EAjCgDgB,IAAgC,IAA/B,KAAE4J,EAAI,WAAEkB,EAAa,IAAI9K,EACxE,MAAM+K,GAAWC,EAAAA,EAAAA,MAMjB,OACEpJ,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACgK,EAAAA,EAAa,CACZvL,MAAM,mBACNuI,YAAY,mCAAkClH,UAE9C2B,EAAAA,EAAAA,MAACqJ,EAAAA,EAAU,CAAAhL,SAAA,EACTE,EAAAA,EAAAA,KAAC+K,EAAAA,EAAU,CAACxI,MAAM,YAAYjB,MAAOmI,EAAKjH,QAC1CxC,EAAAA,EAAAA,KAAC+K,EAAAA,EAAU,CAACxI,MAAM,gBAAgBjB,MAAOmI,EAAKrG,SAC9CpD,EAAAA,EAAAA,KAAC+K,EAAAA,EAAU,CAACxI,MAAM,YAAYjB,MAAOmI,EAAKjL,QAC1CwB,EAAAA,EAAAA,KAAC+K,EAAAA,EAAU,CAACxI,MAAM,SAASjB,OAAOtB,EAAAA,EAAAA,KAACuK,EAAAA,EAAW,CAACC,OAAQf,EAAKe,OAAQhM,KAAK,YACzEwB,EAAAA,EAAAA,KAAC+K,EAAAA,EAAU,CAACxI,MAAM,aAAajB,MAAOmI,EAAKuB,kBAI/ChL,EAAAA,EAAAA,KAACiL,EAAa,CACZpB,OAAQc,EACRlM,MAAM,cACNuI,YAAY,6BACZ8C,YAvBmBI,IACvBU,EAASM,EAAAA,EAAOC,qBAAqBjB,EAAMrL,IAAI,EAuB3CkL,aAAa,8CAEX,E,kCChCV,MAsFA,EAtF0DlK,IAAe,IAAd,KAAE4J,GAAM5J,EACjE,OACE4B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,UAChD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACoL,EAAAA,EAAM,IACA3B,EAAK4B,QAAU,CAAEC,IAAK7B,EAAK4B,QAChCE,IAAK9B,EAAKjH,KACVA,KAAMiH,EAAKjH,KACXiI,KAAK,QAEPhJ,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAE2J,EAAKjH,QACxDf,EAAAA,EAAAA,MAAA,KAAG1B,UAAU,wBAAuBD,SAAA,CAAC,OAAK2J,EAAK5K,OAC/CmB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAAA,QAAMD,UAAW,4EACC,WAAhB0J,EAAKe,OACD,8BACA,2BACH1K,SACA2J,EAAKe,OAAOgB,OAAO,GAAGC,cAAgBhC,EAAKe,OAAOkB,MAAM,gBAOnEjK,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,sEAAqED,SAAA,EAClF2B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,yBACvD2B,EAAAA,EAAAA,MAAA,MAAI1B,UAAU,YAAWD,SAAA,EACvB2B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE2J,EAAKrG,WAE7CqG,EAAKlG,QACJ9B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE2J,EAAKlG,YAGhD9B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,gBACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE2J,EAAKuB,qBAKjDvB,EAAK5B,UACJpG,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,aACvDE,EAAAA,EAAAA,KAAA,WAASD,UAAU,mCAAkCD,SAClD2J,EAAK5B,gBAMb4B,EAAK3B,eACJrG,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,gCAA+BD,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,mBACvDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oGAAmGD,SAChH2J,EAAK3B,mBAKZrG,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,gCAA+BD,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,oBACvD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8BAA6BD,SAAA,CACzB,WAAhB2J,EAAKe,QACJxK,EAAAA,EAAAA,KAAC2L,EAAAA,EAAe,CAAC5L,UAAU,4BAE3BC,EAAAA,EAAAA,KAAC4L,EAAAA,EAAW,CAAC7L,UAAU,0BAEzBC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpB,WAAhB2J,EAAKe,OACF,gBAAgBf,EAAKuB,YACrB,8BAIN,E,4CCxEV,MA0GA,EA1G0CnL,IAQnC,IARoC,MACzCgM,EAAK,WACLC,EACAC,WAAYC,EAAW,aACvBC,EACAC,YAAaC,EAAY,MACzB1N,EAAQ,QAAO,QACfoE,GAAU,GACXhD,EACC,MAAM+K,GAAWC,EAAAA,EAAAA,MAQXuB,EAA0B,CAC9B,CACE/G,IAAK,OACL9C,MAAO,OACP8J,UAAU,EACVC,OAAQA,CAACC,EAAQ9C,KACfhI,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,oBAAmBD,SAAA,CAC/B2J,EAAK4B,QACJrL,EAAAA,EAAAA,KAAA,OACEsL,IAAK7B,EAAK4B,OACVE,IAAK9B,EAAKjH,KACVzC,UAAU,4CAGZC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mFAAkFD,SAC9F2J,EAAKjH,KAAKgJ,OAAO,MAGtB/J,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAE2J,EAAKjH,QACjDf,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,wBAAuBD,SAAA,CAAC,OAAK2J,EAAK5K,aAKzD,CACEwG,IAAK,QACL9C,MAAO,QACP8J,UAAU,EACVC,OAAShL,IACPG,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACwM,EAAAA,EAAY,CAACzM,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,QAAAF,SAAOwB,QAIb,CAAE+D,IAAK,OAAQ9C,MAAO,OAAQ8J,UAAU,GACxC,CAAEhH,IAAK,SAAU9C,MAAO,SAAU8J,UAAU,GAC5C,CAAEhH,IAAK,YAAa9C,MAAO,aAAc8J,UAAU,GACnD,CACEhH,IAAK,UACL9C,MAAO,UACP+J,OAAQA,CAACG,EAAGhD,KACVhI,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,UACED,UAAU,sEACVyJ,QAAUf,IACRA,EAAEiE,kBACFZ,EAAWrC,EAAK,EAChB3J,UAEFE,EAAAA,EAAAA,KAAC0K,EAAAA,EAAO,CAAC3K,UAAU,eAErBC,EAAAA,EAAAA,KAAA,UACED,UAAU,uEACVyJ,QAAUf,IACRA,EAAEiE,kBACF9B,EAASM,EAAAA,EAAOyB,iBAAiBlD,EAAK5K,IAAI,EAC1CiB,UAEFE,EAAAA,EAAAA,KAACsC,EAAAA,EAAU,CAACvC,UAAU,eAExBC,EAAAA,EAAAA,KAAA,UACED,UAAU,sEACVyJ,QAAUf,IACRA,EAAEiE,kBACFT,EAAaxC,EAAK,EAClB3J,UAEFE,EAAAA,EAAAA,KAAC4M,EAAAA,EAAS,CAAC7M,UAAU,mBAO/B,OACEC,EAAAA,EAAAA,KAAC6M,EAAAA,EAAc,CACbzO,KAAMyN,EACNO,QAASA,EACTU,WArFoBrD,IACtBmB,EAASM,EAAAA,EAAOyB,iBAAiBlD,EAAK5K,IAAI,EAqFxCJ,MAAOA,EACPsO,YAAY,EACZlK,QAASA,EACTkH,aAAa,kBACb,E,gEC7GN,MAwNA,EAxNoDlK,IAG7C,IAADmN,EAAAC,EAAAC,EAAA,IAFJC,OAAQC,EAAO,SACfC,GACDxN,EAEC,MAAMyN,EAAe,CACnBC,aAAqB,OAARF,QAAQ,IAARA,OAAQ,EAARA,EAAUE,cAAe,EACtCC,YAAoB,OAARH,QAAQ,IAARA,OAAQ,EAARA,EAAUG,aAAc,EACpCC,mBAA2B,OAARJ,QAAQ,IAARA,OAAQ,EAARA,EAAUI,oBAAqB,EAClDC,gBAAwB,OAARL,QAAQ,IAARA,OAAQ,EAARA,EAAUK,iBAAkB,EAC5CC,cAAsB,OAARN,QAAQ,IAARA,OAAQ,EAARA,EAAUM,eAAgB,IAMpC3L,EAAoB,CACxB,CACEvD,MAAO,eACP6C,MAAOgM,EAAaC,YACpBrM,MAAMlB,EAAAA,EAAAA,KAAC4N,EAAAA,EAAgB,CAAC7N,UAAU,2BAEpC,CACEtB,MAAO,cACP6C,OAAO+I,EAAAA,EAAAA,IAAeiD,EAAaE,YACnCtM,MAAMlB,EAAAA,EAAAA,KAAC6N,EAAAA,EAAkB,CAAC9N,UAAU,4BAEtC,CACEtB,MAAO,gBACP6C,OAAO+I,EAAAA,EAAAA,IAAeiD,EAAaG,mBACnCvM,MAAMlB,EAAAA,EAAAA,KAAC8N,EAAAA,EAAS,CAAC/N,UAAU,8BAKzBgO,EAAsB3J,IAC1B,IAGE,OAFa,IAAIE,KAAKF,GAEV4J,mBAAmB,QAAS,CACtCC,MAAO,QACPC,IAAK,UACLC,KAAM,WAEV,CAAE,MAAO9Q,GAEP,OAAO+G,CACT,GAkDIgK,EA9C2BC,MAC/B,GAAyC,IAArCf,EAAaK,aAAa9J,OAC5B,MAAO,CACLyK,OAAQ,CAAC,WACTC,SAAU,CAAC,CACThM,MAAO,iBACPnE,KAAM,CAAC,GACPoQ,YAAa,UACbC,gBAAiB,0BACjBhO,MAAM,EACNiO,QAAS,MAMf,MAAMC,EAAerB,EAAaK,aAC/BlO,QAAOmP,GAAQA,GAAQA,EAAKvK,MAA+B,kBAAhBuK,EAAKC,SAChDC,MAAK,CAACC,EAAGC,IAAM,IAAI1K,KAAKyK,EAAE1K,MAAMG,UAAY,IAAIF,KAAK0K,EAAE3K,MAAMG,YAEhE,IAAIyK,EAAqB,EACzB,MAAMC,EAAYP,EAAatP,KAAI6K,IACjC+E,GAAsB/E,EAAM2E,OACrB,CACLtM,MAAOwL,EAAmB7D,EAAM7F,MAChC/C,MAAO2N,MAIX,MAAO,CACLX,OAAQY,EAAU7P,KAAIuP,GAAQA,EAAKrM,QACnCgM,SAAU,CAAC,CACThM,MAAO,sBACPnE,KAAM8Q,EAAU7P,KAAIuP,GAAQA,EAAKtN,QACjCkN,YAAa,UACbC,gBAAiB,0BACjBhO,MAAM,EACNiO,QAAS,GACTS,qBAAsB,UACtBC,iBAAkB,UAClBC,iBAAkB,EAClBC,YAAa,IAEhB,EAGuBjB,GAE1B,OACE5M,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACuP,EAAAA,EAAc,CAACvN,QAASA,KAEzBP,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,wCAAuCD,SAAA,EAEpD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,iDAAgDD,SAAA,EAC7D2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAC,oBACpDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SACjCwN,EAAaK,aAAa9J,OAAS,EAChC,gCACA,kCAKPyJ,EAAaK,aAAa9J,OAAS,GAClC7D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAACwP,EAAAA,GAAI,CACHpR,KAAMgQ,EACNrR,QAAS,IACJ0S,EAAAA,GACHC,QAAS,IACJD,EAAAA,GAAwBC,QAC3BjR,MAAO,CACLkR,SAAS,GAEXC,OAAQ,CACND,SAAS,IAGbE,OAAQ,IACHJ,EAAAA,GAAwBI,OAC3BC,EAAG,IACgC,QAAjC9C,EAAGyC,EAAAA,GAAwBI,cAAM,IAAA7C,OAAA,EAA9BA,EAAgC8C,EACnCC,MAAO,IAC4B,QAAjC9C,EAAGwC,EAAAA,GAAwBI,cAAM,IAAA5C,GAAG,QAAHC,EAA9BD,EAAgC6C,SAAC,IAAA5C,OAAH,EAA9BA,EAAmC6C,MACtCC,SAAU,SAAS1O,GACjB,OAAO+I,EAAAA,EAAAA,IAAe/I,EACxB,WAQZtB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,cAAaD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8BAA6BD,SAAC,YAC7CE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oCAAmCD,SAAC,sBACjDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,0EASlD2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,iDAAgDD,SAAA,EAC7D2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAC,qBACpDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SAAC,8CAGvCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,SACvBwN,EAAaK,aAAa9J,OAAS,GAClCpC,EAAAA,EAAAA,MAAAwO,EAAAA,SAAA,CAAAnQ,SAAA,EACE2B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,uBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpC,MACC,MAAMoQ,EAAY5C,EAAaK,aAAaL,EAAaK,aAAa9J,OAAS,GAC/E,OAAgB,OAATqM,QAAS,IAATA,GAAAA,EAAW7L,KAAO0J,EAAmBmC,EAAU7L,MAAQ,KAC/D,EAHA,SAML5C,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,iBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpC,MACC,MAAMqQ,EAAa7C,EAAaK,aAAa,GAC7C,OAAiB,OAAVwC,QAAU,IAAVA,GAAAA,EAAY9L,KAAO0J,EAAmBoC,EAAW9L,MAAQ,KACjE,EAHA,SAML5C,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,mBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,UACpCuK,EAAAA,EAAAA,IAAe+F,KAAKpK,OAAOsH,EAAaK,aAAatO,KAAI6K,GAASA,EAAM2E,iBAG7EpN,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,oBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,UACpCuK,EAAAA,EAAAA,IAAe+F,KAAKtK,OAAOwH,EAAaK,aAAatO,KAAI6K,GAASA,EAAM2E,oBAK/EpN,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8BAA6BD,SAAC,kBAC7CE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAC,mBACzCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,2DAQhD,E,aCpOH,MAAMuQ,EAWJ,CACLC,KAAM,SACNC,QAAU1R,GAAe,UAAUA,IACnC2R,OAAS3R,GAAe,UAAUA,WAClC4R,IAAM5R,GAAe,UAAUA,QAC/B6R,MAAQ7R,GAAe,UAAUA,UACjC8R,aAAc,uBCJLC,EAA8BC,IACzC,MAAMC,EAAuB,CAC3BC,KAAMF,EAAarO,KACnBwO,MAAOH,EAAazN,MACpB6N,mBAAoBJ,EAAaI,oBAAsB,WAgBzD,YAb8BpP,IAA1BgP,EAAanM,WACfoM,EAAOpM,SAAWmM,EAAanM,eAEN7C,IAAvBgP,EAAatN,QACfuN,EAAOI,YAAcL,EAAatN,YAEP1B,IAAzBgP,EAAahJ,UACfiJ,EAAOK,QAAUN,EAAahJ,cAEEhG,IAA9BgP,EAAa/I,eACfgJ,EAAOM,aAAeP,EAAa/I,cAG9BgJ,CAAM,EAMFO,EAAkCC,IAC7C,MAAMR,EAAe,CACnBjS,GAAIyS,EAAYzS,GAChB2D,KAAM8O,EAAY9O,KAClBY,MAAOkO,EAAYlO,MACnB5E,KAAM8S,EAAY9S,KAClBgM,OAAQ8G,EAAY9G,OACpByG,mBAAoBK,EAAYL,oBAAsB,WAmBxD,YAhB2BpP,IAAvByP,EAAYjG,SACdyF,EAAOzF,OAASiG,EAAYjG,aAEJxJ,IAAtByP,EAAY/N,QACduN,EAAOvN,MAAQ+N,EAAY/N,YAED1B,IAAxByP,EAAYzJ,UACdiJ,EAAOjJ,QAAUyJ,EAAYzJ,cAEEhG,IAA7ByP,EAAYxJ,eACdgJ,EAAOhJ,aAAewJ,EAAYxJ,mBAENjG,IAA1ByP,EAAYtG,YACd8F,EAAO9F,UAAYsG,EAAYtG,WAG1B8F,CAAM,EAsBFS,EAA8BpK,IACzC,IAAKA,EACH,MAAM,IAAIqK,MAAM,2BAGlB,GAAwB,kBAAbrK,EACT,MAAM,IAAIqK,MAAM,2BAGlB,KAAM,YAAarK,GACjB,MAAM,IAAIqK,MAAM,kCAGlB,IAAyB,IAArBrK,EAASsK,QACX,MAAM,IAAID,MAAMrK,EAASzI,SAAW,kBAGtC,KAAM,SAAUyI,GACd,MAAM,IAAIqK,MAAM,+BAGlB,OAAOrK,CAAQ,EA2BJuK,EAAyBC,IACpC,MAAMC,EAAoBL,EAAqCI,GAEzDE,EAAkBR,EAA+BO,EAAkBxT,MAEzE,MAAO,CACLqT,QAASG,EAAkBH,QAC3B/S,QAASkT,EAAkBlT,QAC3BN,KAAMyT,EACP,EAMUC,EAAuBC,GACL,kBAAlBA,EACFA,EAGQ,OAAbA,QAAa,IAAbA,GAAAA,EAAerT,QACVqT,EAAcrT,QAGN,OAAbqT,QAAa,IAAbA,GAAAA,EAAe1U,MACV0U,EAAc1U,MAGhB,+BC5II2U,EAAW,CAMtBC,SAAU9T,UACR,IACE,MAAM+T,EAAgB5T,EDwCkBA,KAC5C,MAAM4T,EAAqC,CAAC,EAS5C,YAPoBrQ,IAAhBvD,EAAO6T,OAAoBD,EAAcC,KAAO7T,EAAO6T,WACtCtQ,IAAjBvD,EAAO8T,QAAqBF,EAAcE,MAAQ9T,EAAO8T,YACvCvQ,IAAlBvD,EAAO+T,SAAsBH,EAAcG,OAAS/T,EAAO+T,aACzCxQ,IAAlBvD,EAAOkM,SAAsB0H,EAAc1H,OAASlM,EAAOkM,aAC3C3I,IAAhBvD,EAAOwQ,OAAoBoD,EAAcpD,KAAOxQ,EAAOwQ,WACtCjN,IAAjBvD,EAAO4L,QAAqBgI,EAAchI,MAAQ5L,EAAO4L,OAEtDgI,CAAa,EClDeI,CAA8BhU,GAAU,CAAC,EAClE6I,QAAiBC,EAAAA,EAAUC,IAAIgJ,EAAgBC,KAAM,CAAEhS,OAAQ4T,IAGrE,GAAI/K,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAG3B,IAAK8J,EAAS/I,KACZ,MAAM,IAAIoT,MAAM,6BAIlB,MDsEoCG,KACxC,MAAMC,EAAoBL,EAAuCI,GAE3DY,EAAmBX,EAAkBxT,KAAKiB,IAAIgS,GAE9CP,EAAqC,CACzCW,QAASG,EAAkBH,QAC3B/S,QAASkT,EAAkBlT,QAC3BN,KAAMmU,GAOR,YAJqC1Q,IAAjC+P,EAAkB7E,aACpB+D,EAAO/D,WAAa6E,EAAkB7E,YAGjC+D,CAAM,ECrFF0B,CAA0BrL,EAAS/I,KAC5C,CAAE,MAAOf,GAAa,IAADoV,EAEnB,GAAkB,QAAlBA,EAAIpV,EAAM8J,gBAAQ,IAAAsL,GAAdA,EAAgBrU,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GAQFsV,YAAaxU,UACX,IACE,MAAMgJ,QAAiBC,EAAAA,EAAUC,IAAIgJ,EAAgBE,QAAQ1R,IAG7D,GAAIsI,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAG3B,IAAK8J,EAAS/I,KACZ,MAAM,IAAIoT,MAAM,6BAKlB,OAD4BE,EAAsBvK,EAAS/I,MAChCA,IAC7B,CAAE,MAAOf,GAAa,IAADuV,EAEnB,GAAkB,QAAlBA,EAAIvV,EAAM8J,gBAAQ,IAAAyL,GAAdA,EAAgBxU,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GAQFwV,WAAY1U,UACV,IAEE,MAAM2U,EAAclC,EAA2BvD,GACzClG,QAAiBC,EAAAA,EAAU2L,KAAK1C,EAAgBC,KAAMwC,GAG5D,GAAI3L,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAG3B,IAAK8J,EAAS/I,KACZ,MAAM,IAAIoT,MAAM,6BAKlB,OAD4BE,EAAsBvK,EAAS/I,MAChCA,IAC7B,CAAE,MAAOf,GAAa,IAAD2V,EAEnB,GAAkB,QAAlBA,EAAI3V,EAAM8J,gBAAQ,IAAA6L,GAAdA,EAAgB5U,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GASF4V,WAAY9U,MAAOU,EAAYwO,KAC7B,IAEE,MAAMyF,EAAclC,EAA2BvD,GACzClG,QAAiBC,EAAAA,EAAU8L,IAAI7C,EAAgBE,QAAQ1R,GAAKiU,GAGlE,GAAI3L,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAG3B,IAAK8J,EAAS/I,KACZ,MAAM,IAAIoT,MAAM,6BAKlB,OAD4BE,EAAsBvK,EAAS/I,MAChCA,IAC7B,CAAE,MAAOf,GAAa,IAAD8V,EAEnB,GAAkB,QAAlBA,EAAI9V,EAAM8J,gBAAQ,IAAAgM,GAAdA,EAAgB/U,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GAQF+V,WAAYjV,UACV,IACE,MAAMgJ,QAAiBC,EAAAA,EAAU5H,OAAO6Q,EAAgBE,QAAQ1R,IAGhE,GAAIsI,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAI3B,GAAI8J,EAAS/I,MAAiC,kBAAlB+I,EAAS/I,MAAqB,YAAa+I,EAAS/I,OAAS+I,EAAS/I,KAAKqT,QAAS,CAC9G,MAAMiB,EAAe,YAAavL,EAAS/I,KAAO+I,EAAS/I,KAAKM,QAAU,0BAC1E,MAAM,IAAI8S,MAAMkB,EAClB,CACF,CAAE,MAAOrV,GAAa,IAADgW,EAEnB,GAAkB,QAAlBA,EAAIhW,EAAM8J,gBAAQ,IAAAkM,GAAdA,EAAgBjV,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GASFiW,iBAAkBnV,MAAOU,EAAY2L,KACnC,IACE,MAAM+I,EAA+B,CAAE/I,UACjCrD,QAAiBC,EAAAA,EAAU8L,IAAI7C,EAAgBG,OAAO3R,GAAK0U,GAGjE,GAAIpM,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAI3B,GAAI8J,EAAS/I,MAAiC,kBAAlB+I,EAAS/I,MAAqB,YAAa+I,EAAS/I,OAAS+I,EAAS/I,KAAKqT,QAAS,CAC9G,MAAMiB,EAAe,YAAavL,EAAS/I,KAAO+I,EAAS/I,KAAKM,QAAU,uBAC1E,MAAM,IAAI8S,MAAMkB,EAClB,CACF,CAAE,MAAOrV,GAAa,IAADmW,EAEnB,GAAkB,QAAlBA,EAAInW,EAAM8J,gBAAQ,IAAAqM,GAAdA,EAAgBpV,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,GASFoW,YAAatV,MAAOuV,EAAepV,KACjC,IACE,MAAMqV,EAAgC,IAAKrV,EAAQ+T,OAAQqB,GAC3D,aAAa1B,EAASC,SAAS0B,EACjC,CAAE,MAAOtW,GACP,MAAMmK,EAAAA,EAAAA,IAAenK,EACvB,GASFuW,eAAgBzV,MAAO0V,EAAgCvV,KACrD,IAEE,MAAMwV,EAA8B,IAAKxV,GAGzC,aAAa0T,EAASC,SAAS6B,EACjC,CAAE,MAAOzW,GACP,MAAMmK,EAAAA,EAAAA,IAAenK,EACvB,GAQF0W,gBAAiB5V,UACf,IACE,MAAMqH,EAAW,IAAIwO,SACrBxO,EAASyO,OAAO,QAAS9K,GAEzB,MAAMhC,QAAiBC,EAAAA,EAAU2L,KAAK1C,EAAgBM,aAAcnL,EAAU,CAC5E0O,QAAS,CACP,eAAgB,yBAKpB,GAAI/M,EAAS9J,MACX,MAAM,IAAImU,MAAMrK,EAAS9J,OAG3B,IAAK8J,EAAS/I,KACZ,MAAM,IAAIoT,MAAM,6BAKlB,OAD0BD,EAA6CpK,EAAS/I,MACvDA,IAC3B,CAAE,MAAOf,GAAa,IAAD8W,EAEnB,GAAkB,QAAlBA,EAAI9W,EAAM8J,gBAAQ,IAAAgN,GAAdA,EAAgB/V,KAAM,CACxB,MAAMsU,EAAeZ,EAAoBzU,EAAM8J,SAAS/I,MACxD,MAAM,IAAIoT,MAAMkB,EAClB,CACA,MAAMlL,EAAAA,EAAAA,IAAenK,EACvB,KAKS,SACX4U,EAAQ,YACRU,EAAW,WACXE,GAAU,WACVI,GAAU,WACVG,GAAU,iBACVE,GAAgB,YAChBG,GAAW,eACXG,GAAc,gBACdG,IACE/B,EAEJ,K,eClSO,MAyIP,GAzIwB,WAAuC,IAAtCjV,EAAO6I,UAAA/B,OAAA,QAAAhC,IAAA+D,UAAA,GAAAA,UAAA,GAAG,CAAElG,cAAc,IAEjD3B,EAAAA,EAAAA,YAAU,KAERqJ,EAAAA,EAAUgN,YAAY,GACrB,IAIH,MAAMC,EAAa,CACjBhW,OAAQF,gBACiB6T,GAAAA,YACP5T,KAElBQ,QAASoT,GAAAA,YACThT,OAAQgT,GAAAA,WACR5S,OAAQ4S,GAAAA,WACRxS,OAAQwS,GAAAA,YAGJsC,GAAWzX,EAAAA,EAAAA,GAAoBwX,EAAY,CAC/CxW,WAAY,QACZ6B,aAAc3C,EAAQ2C,gBAGlB,iBAAEnC,IAAqBC,EAAAA,GAAAA,KAGvBG,GAAsBD,EAAAA,EAAAA,QAAOH,IAGnCQ,EAAAA,EAAAA,YAAU,KACRJ,EAAoBK,QAAUT,CAAgB,IAIhD,MAAM+V,GAAmBpV,EAAAA,EAAAA,cAAYC,MAAOU,EAAY2L,KACtD,UACQwH,GAAAA,iBAA0BnT,EAAI2L,GAGpC,MACM+J,EADkBD,EAAStX,SACOqC,KAAIoK,GAC1CA,EAAK5K,KAAOA,EAAK,IAAK4K,EAAMe,UAAWf,IAIzC6K,EAASrX,YAAYsX,GAErB5W,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,QAAmB,WAAX8L,EAAsB,YAAc,yBAEzD,CAAE,MAAOnN,GAMP,MALAM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,aAAwB,WAAX8L,EAAsB,WAAa,eAErDnN,CACR,IACC,CAACiX,IAGErB,GAAa/U,EAAAA,EAAAA,cAAYC,MAAOU,EAAYwO,KAChD,IACE,MAAMmH,QAAoBxC,GAAAA,WAAoBnT,EAAIwO,GAI5CkH,EADkBD,EAAStX,SACOqC,KAAIoK,GAC1CA,EAAK5K,KAAOA,EAAK2V,EAAc/K,IAYjC,OARA6K,EAASrX,YAAYsX,GAErB5W,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,8BAGJ8V,CACT,CAAE,MAAOnX,GAMP,MALAM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,0BAELrB,CACR,IACC,CAACiX,IAGEb,GAAcvV,EAAAA,EAAAA,cAAYC,MAAOuV,EAAepV,KACpD,IACE,aAAa0T,GAAAA,YAAqB0B,EAAOpV,EAC3C,CAAE,MAAOjB,GAMP,MALAM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,2BAELrB,CACR,IACC,IAGGoX,GAAyBvW,EAAAA,EAAAA,cAAYC,UACzC,IACE,aAAa6T,GAAAA,SAAkB1T,EACjC,CAAE,MAAOjB,GAMP,MALAM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,0BAELrB,CACR,IACC,IAEH,MAAO,IACFiX,EACHzI,MAAOyI,EAAStX,SAChB0X,WAAYJ,EAASrW,cACrB0U,YAAa2B,EAAS3V,cACtBG,aAAcwV,EAASxV,aACvBS,aAAc+U,EAAS/U,aACvB+T,mBACAL,aACAQ,cACAgB,yBAEJ,E,8FC9HA,MA6JA,EA7JgD5U,IAWzC,IAX0C,MAC/C0C,EAAK,KACLC,EAAI,MACJlB,EAAK,SACLmB,EAAQ,MACRpF,EAAK,SACLqF,GAAW,EAAK,SAChBE,GAAW,EAAK,QAChBwG,EAAU,QAAe,aACzBC,EAAe,CAAC,aAAc,YAAa,YAAa,cAAa,UACrEtJ,EAAY,IACbF,EACC,MAAO8U,EAAYC,IAAiB1X,EAAAA,EAAAA,WAAS,IACtC2X,EAASC,IAAc5X,EAAAA,EAAAA,UAAwB,MAChD6X,GAAerX,EAAAA,EAAAA,QAAyB,MAG9C2C,EAAAA,WAAgB,KACd,GAAIiB,aAAiB0T,KAAM,CACzB,MAAMvR,EAAMC,IAAIuR,gBAAgB3T,GAEhC,OADAwT,EAAWrR,GACJ,IAAMC,IAAIwR,gBAAgBzR,EACnC,CAAO,MAAqB,kBAAVnC,GAAsBA,OACtCwT,EAAWxT,QAGXwT,EAAW,KAEb,GACC,CAACxT,IAEJ,MAAM6T,GAAmBjX,EAAAA,EAAAA,cAAaiL,IACpC,MAAMiM,GAAaC,EAAAA,EAAAA,IAAalM,EAAM,CACpCC,UACAC,iBAGE+L,EAAWE,MACb7S,EAAS0G,GAGTxJ,QAAQtC,MAAM,0BAA2B+X,EAAW/X,MACtD,GACC,CAAC+L,EAASC,EAAc5G,IA8C3B,OACEhB,EAAAA,EAAAA,MAAA,OAAK1B,UAAWA,EAAUD,SAAA,EACxB2B,EAAAA,EAAAA,MAAA,SAAO1B,UAAU,+CAA8CD,SAAA,CAC5DyC,EAAM,IAAEG,IAAY1C,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAGtD2B,EAAAA,EAAAA,MAAA,OACE1B,UAAW,sHAEP4U,EAAa,yCAA2C,gCACxDtX,EAAQ,iBAAmB,iBAC3BuF,EAAW,gCAAkC,oDAEjD2S,WAlDkB9M,IACtBA,EAAEE,iBACG/F,GACHgS,GAAc,EAChB,EA+CIY,YA5CmB/M,IACvBA,EAAEE,iBACFiM,GAAc,EAAM,EA2ChBa,OAxCchN,IAAwB,IAADiN,EAIzC,GAHAjN,EAAEE,iBACFiM,GAAc,GAEVhS,EAAU,OAEd,MAAMuG,EAA2B,QAAvBuM,EAAGjN,EAAEkN,aAAaC,aAAK,IAAAF,OAAA,EAApBA,EAAuB,GAChCvM,GACFgM,EAAiBhM,EACnB,EAgCIK,QAtBcqM,MACbjT,GAAYmS,EAAa/W,SAC5B+W,EAAa/W,QAAQ8X,OACvB,EAmByBhW,SAAA,EAErBE,EAAAA,EAAAA,KAAA,SACEa,IAAKkU,EACLvW,KAAK,OACLgE,KAAMA,EACNuT,OAAQ1M,EAAa2M,KAAK,KAC1BvT,SAnEuBgG,IAA4C,IAADwN,EACxE,MAAM9M,EAAqB,QAAjB8M,EAAGxN,EAAEC,OAAOkN,aAAK,IAAAK,OAAA,EAAdA,EAAiB,GAC1B9M,GACFgM,EAAiBhM,EACnB,EAgEMpJ,UAAU,SACV6C,SAAUA,IAGXiS,GACCpT,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,WAAUD,SAAA,EACvBE,EAAAA,EAAAA,KAAA,OACEsL,IAAKuJ,EACLtJ,IAAI,UACJxL,UAAU,+CAEV6C,IACA5C,EAAAA,EAAAA,KAAA,UACExB,KAAK,SACLgL,QAAUf,IACRA,EAAEiE,kBAnDhBjK,EAAS,MACLsS,EAAa/W,UACf+W,EAAa/W,QAAQsD,MAAQ,GAkDH,EAEhBvB,UAAU,qGAAoGD,UAE9GE,EAAAA,EAAAA,KAACkW,EAAAA,EAAS,CAACnW,UAAU,kBAK3B0B,EAAAA,EAAAA,MAAA,OAAA3B,SAAA,EACEE,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAACF,UAAU,qCACrB0B,EAAAA,EAAAA,MAAA,OAAK1B,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SACjC6U,EAAa,kBAAoB,sCAEpClT,EAAAA,EAAAA,MAAA,KAAG1B,UAAU,6BAA4BD,SAAA,CAAC,uBACnBsQ,KAAK+F,MAAM/M,EAAU,KAAO,MAAM,iBAOhE/L,IAAS2C,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEzC,MAChD,C,mECzJV+Y,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAIK,MAAMvH,EAAgD,CAC3DwH,YAAY,EACZC,qBAAqB,EACrBxH,QAAS,CACPjR,MAAO,CACLkR,SAAS,EACTwH,MAAO,UACPC,KAAM,CACJ3M,KAAM,GACN4M,OAAQ,SAGZzH,OAAQ,CACND,SAAS,EACTrB,OAAQ,CACN6I,MAAO,YAGXG,QAAS,CACP7I,gBAAiB,UACjB8I,WAAY,UACZC,UAAW,UACXC,QAAS,GACTC,eAAe,IAGnB7H,OAAQ,CACN8H,EAAG,CACDC,KAAM,CACJjI,SAAS,GAEXI,MAAO,CACLoH,MAAO,YAGXrH,EAAG,CACD+H,aAAa,EACbD,KAAM,CACJjI,SAAS,EACTwH,MAAO,WAETpH,MAAO,CACLoH,MAAO,cAMFW,EAA8C,CACzDb,YAAY,EACZC,qBAAqB,EACrBxH,QAAS,CACPjR,MAAO,CACLkR,SAAS,EACTwH,MAAO,UACPC,KAAM,CACJ3M,KAAM,GACN4M,OAAQ,SAGZzH,OAAQ,CACNmI,SAAU,SACVzJ,OAAQ,CACN6I,MAAO,UACPC,KAAM,CACJ3M,KAAM,MAIZ6M,QAAS,CACP7I,gBAAiB,UACjB8I,WAAY,UACZC,UAAW,UACXC,QAAS,MAsDFO,EAAgBC,IACvBA,GACFA,EAAcC,SAChB,C,gDCvKF,SAAS1L,EAAY3M,EAIlBK,GAAQ,IAJW,MACpBzB,EAAK,QACL0B,KACGC,GACJP,EACC,OAAoBQ,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3B,EAAqB4B,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACH1B,GAAS,KAAmB4B,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCX,EAAAA,WAAiBmM,E,gDCvBlD,SAASI,EAAS/M,EAIfK,GAAQ,IAJQ,MACjBzB,EAAK,QACL0B,KACGC,GACJP,EACC,OAAoBQ,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3B,EAAqB4B,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACH1B,GAAS,KAAmB4B,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCX,EAAAA,WAAiBuM,E", "sources": ["hooks/useEntityData.ts", "components/common/DetailList.tsx", "../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/MetricCard.tsx", "components/common/MetricsSection.tsx", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "components/common/DetailItem.tsx", "components/common/FormField.tsx", "utils/validation.ts", "components/common/DetailSection.tsx", "features/users/api/businessTypesApi.ts", "features/users/components/AddUserForm.tsx", "features/users/components/EditUserForm.tsx", "components/common/OrdersSection.tsx", "features/users/components/UserDetails.tsx", "features/users/components/UserDetailsModal.tsx", "features/users/components/UserList.tsx", "features/users/components/UserAnalytics.tsx", "constants/endpoints.ts", "features/users/utils/apiTransformers.ts", "features/users/api/usersApi.ts", "features/users/hooks/useUsers.ts", "components/common/ImageUpload.tsx", "utils/chartConfig.ts", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import { useState, useCallback, useEffect, useRef } from 'react';\r\nimport useNotification from './useNotification';\r\n\r\nexport interface EntityApi<T, IdType = string> {\r\n  getAll: (params?: any) => Promise<T[]>;\r\n  getById: (id: IdType) => Promise<T>;\r\n  create: (data: any) => Promise<T>;\r\n  update: (id: IdType, data: any) => Promise<T>;\r\n  delete: (id: IdType) => Promise<void>;\r\n}\r\n\r\nexport interface UseEntityDataOptions {\r\n  entityName: string;\r\n  initialFetch?: boolean;\r\n}\r\n\r\nexport const useEntityData = <T, IdType = string>(\r\n  apiService: EntityApi<T, IdType>,\r\n  options: UseEntityDataOptions\r\n) => {\r\n  const [entities, setEntities] = useState<T[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use refs to store current values and avoid stale closures\r\n  const apiServiceRef = useRef(apiService);\r\n  const showNotificationRef = useRef(showNotification);\r\n  const entityNameRef = useRef(options.entityName);\r\n  const hasInitialFetched = useRef(false);\r\n\r\n  // Update refs when values change\r\n  useEffect(() => {\r\n    apiServiceRef.current = apiService;\r\n    showNotificationRef.current = showNotification;\r\n    entityNameRef.current = options.entityName;\r\n  });\r\n\r\n  const fetchEntities = useCallback(async (params?: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await apiServiceRef.current.getAll(params);\r\n      setEntities(data);\r\n      return data;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const getEntityById = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const entity = await apiServiceRef.current.getById(id);\r\n      return entity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const createEntity = useCallback(async (data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newEntity = await apiServiceRef.current.create(data);\r\n      setEntities(prev => [...prev, newEntity]);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} created successfully`\r\n      });\r\n      return newEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to create ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateEntity = useCallback(async (id: IdType, data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedEntity = await apiServiceRef.current.update(id, data);\r\n      setEntities(prev => prev.map(entity =>\r\n        (entity as any).id === id ? updatedEntity : entity\r\n      ));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} updated successfully`\r\n      });\r\n      return updatedEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to update ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteEntity = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      await apiServiceRef.current.delete(id);\r\n      setEntities(prev => prev.filter(entity => (entity as any).id !== id));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} deleted successfully`\r\n      });\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to delete ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Initial fetch effect - runs only once\r\n  useEffect(() => {\r\n    if (options.initialFetch !== false && !hasInitialFetched.current) {\r\n      console.log(`[useEntityData] Starting initial fetch for ${options.entityName}`);\r\n      hasInitialFetched.current = true;\r\n\r\n      const initialFetch = async () => {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        try {\r\n          console.log(`[useEntityData] Calling API for ${options.entityName}`);\r\n          const data = await apiService.getAll();\r\n          console.log(`[useEntityData] Received data for ${options.entityName}:`, data);\r\n          setEntities(data);\r\n        } catch (err) {\r\n          const error = err as Error;\r\n          console.error(`[useEntityData] Error fetching ${options.entityName}:`, error);\r\n          setError(error);\r\n          showNotificationRef.current({\r\n            type: 'error',\r\n            title: 'Error',\r\n            message: `Failed to fetch ${options.entityName}`\r\n          });\r\n        } finally {\r\n          console.log(`[useEntityData] Finished fetch for ${options.entityName}`);\r\n          setIsLoading(false);\r\n        }\r\n      };\r\n\r\n      initialFetch();\r\n    }\r\n  }, [\r\n    apiService,\r\n    options.entityName,\r\n    options.initialFetch\r\n  ]); // Empty dependency array - runs only once on mount\r\n\r\n  return {\r\n    entities,\r\n    isLoading,\r\n    error,\r\n    fetchEntities,\r\n    getEntityById,\r\n    createEntity,\r\n    updateEntity,\r\n    deleteEntity,\r\n    setEntities // Expose setEntities for custom state updates\r\n  };\r\n};\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailListProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailList: React.FC<DetailListProps> = ({\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <dl className={`sm:divide-y sm:divide-gray-200 ${className}`}>\r\n      {children}\r\n    </dl>\r\n  );\r\n};\r\n\r\nexport default DetailList;", "import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "/**\n * Metric Card Component\n *\n * A simple metric card component for displaying key metrics with icons.\n * This is a replacement for the analytics MetricCard to avoid dependencies.\n */\n\nimport React from 'react';\nimport Card from './Card';\n\ninterface MetricData {\n  total: number;\n  growth?: number;\n}\n\ninterface MetricCardProps {\n  title: string;\n  data: MetricData;\n  icon?: React.ReactNode;\n  formatValue?: (value: number) => string;\n}\n\n// Helper function to get appropriate background class for icon\nconst getIconBackgroundClass = (icon: React.ReactNode): string => {\n  if (!React.isValidElement(icon)) return 'bg-primary bg-opacity-10';\n\n  // Get the className from the icon props\n  const className = icon.props.className || '';\n  \n  // Extract color from className (e.g., \"text-blue-500\" -> \"blue\")\n  const colorMatch = className.match(/text-([a-z]+)-/);\n  if (colorMatch) {\n    const color = colorMatch[1];\n    return `bg-${color}-50`;\n  }\n  \n  return 'bg-primary bg-opacity-10';\n};\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  data,\n  icon,\n  formatValue = (value) => value.toString()\n}) => {\n  return (\n    <Card>\n      <div className=\"flex items-center\">\n        <div className={`p-3 rounded-full ${getIconBackgroundClass(icon)}`}>\n          {/* Ensure consistent icon styling while preserving color */}\n          {React.isValidElement(icon) ? (\n            (() => {\n              const iconElement = icon as React.ReactElement;\n              const existingClassName = iconElement.props.className || '';\n              const colorMatch = existingClassName.match(/text-[a-z0-9-]+/);\n              const colorClass = colorMatch ? colorMatch[0] : 'text-gray-600';\n              const sizeClass = 'h-6 w-6';\n              \n              return React.cloneElement(iconElement, {\n                className: `${sizeClass} ${colorClass}`\n              });\n            })()\n          ) : (\n            icon\n          )}\n        </div>\n        <div className=\"ml-4 flex-1\">\n          <p className=\"text-sm font-medium text-gray-500\">{title}</p>\n          <div className=\"flex items-baseline\">\n            <p className=\"text-2xl font-semibold text-gray-900\">\n              {formatValue(data.total)}\n            </p>\n            {data.growth !== undefined && (\n              <p className={`ml-2 flex items-baseline text-sm font-semibold ${\n                data.growth >= 0 ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {data.growth >= 0 ? '+' : ''}{data.growth.toFixed(1)}%\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default MetricCard;\n", "/**\n * Metrics Section Component\n *\n * A simple metrics section component for displaying multiple metrics.\n * This is a replacement for the analytics MetricsSection to avoid dependencies.\n */\n\nimport React from 'react';\nimport MetricCard from './MetricCard';\n\nexport interface Metric {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon?: React.ReactNode;\n}\n\ninterface MetricsSectionProps {\n  metrics: Metric[];\n  className?: string;\n}\n\nconst MetricsSection: React.FC<MetricsSectionProps> = ({\n  metrics,\n  className = ''\n}) => {\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      {metrics.map((metric, index) => (\n        <MetricCard\n          key={index}\n          title={metric.title}\n          data={{ \n            total: typeof metric.value === 'string' ? parseFloat(metric.value) || 0 : metric.value, \n            growth: metric.change || 0 \n          }}\n          icon={metric.icon}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default MetricsSection;\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailItemProps {\r\n  label: string;\r\n  value: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailItem: React.FC<DetailItemProps> = ({\r\n  label,\r\n  value,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${className}`}>\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{value}</dd>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailItem;", "import React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  name: string;\r\n  type?: string;\r\n  value: any;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  className?: string;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  name,\r\n  type = 'text',\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  placeholder = '',\r\n  options = [],\r\n  className = '',\r\n  disabled = false,\r\n  loading = false\r\n}) => {\r\n  const inputClasses = `mt-1 block w-full rounded-md shadow-sm sm:text-sm ${\r\n    error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary focus:ring-primary'\r\n  }`;\r\n  \r\n  const renderField = () => {\r\n    switch (type) {\r\n      case 'textarea':\r\n        return (\r\n          <textarea\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      case 'select':\r\n        return (\r\n          <select\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            disabled={disabled || loading}\r\n          >\r\n            {loading ? (\r\n              <option value=\"\">Loading...</option>\r\n            ) : (\r\n              options.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </select>\r\n        );\r\n      \r\n      case 'checkbox':\r\n        return (\r\n          <input\r\n            type=\"checkbox\"\r\n            id={name}\r\n            name={name}\r\n            checked={value}\r\n            onChange={onChange}\r\n            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      default:\r\n        return (\r\n          <input\r\n            type={type}\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div className={`${className}`}>\r\n      <label htmlFor={name} className=\"block text-sm font-medium text-gray-700\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      {renderField()}\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailSection: React.FC<DetailSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-white shadow overflow-hidden sm:rounded-lg ${className}`}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"border-t border-gray-200\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailSection;", "/**\r\n * Business Types API Service\r\n * \r\n * This file provides methods for interacting with business types API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { BusinessType } from '../types';\r\n\r\nexport const businessTypesApi = {\r\n  /**\r\n   * Get all business types\r\n   * @returns Promise resolving to an array of business types\r\n   */\r\n  getBusinessTypes: async (): Promise<BusinessType[]> => {\r\n    try {\r\n      const response = await apiClient.get<BusinessType[]>('/business-types');\r\n      return responseValidators.getList(response, 'business types');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a business type by ID\r\n   * @param id - The business type ID\r\n   * @returns Promise resolving to a business type\r\n   */\r\n  getBusinessTypeById: async (id: string): Promise<BusinessType> => {\r\n    try {\r\n      const response = await apiClient.get<BusinessType>(`/business-types/${id}`);\r\n      return responseValidators.getById(response, 'business type', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\n// Export individual methods for more flexible importing\r\nexport const { \r\n  getBusinessTypes, \r\n  getBusinessTypeById \r\n} = businessTypesApi;\r\n\r\nexport default businessTypesApi;\r\n", "/**\r\n * Add User Form Component\r\n *\r\n * This component provides a form for adding new users.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport FormField from '../../../components/common/FormField';\r\nimport ImageUpload from '../../../components/common/ImageUpload';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport { getBusinessTypes } from '../api/businessTypesApi';\r\nimport type { UserFormDataFrontend, BusinessType } from '../types';\r\n\r\ninterface AddUserFormProps {\r\n  onSubmit: (userData: UserFormDataFrontend & { confirmPassword: string; sendInvite: boolean }) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddUserForm: React.FC<AddUserFormProps> = ({ onSubmit, onCancel, isLoading = false }) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    type: 'customer' as const,\r\n    phone: '',\r\n    address: '',\r\n    businessType: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    sendInvite: true,\r\n    image: null as File | null\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);\r\n  const [loadingBusinessTypes, setLoadingBusinessTypes] = useState(false);\r\n\r\n  // Load business types on component mount\r\n  useEffect(() => {\r\n    const loadBusinessTypes = async () => {\r\n      setLoadingBusinessTypes(true);\r\n      try {\r\n        const types = await getBusinessTypes();\r\n        setBusinessTypes(types);\r\n      } catch (error) {\r\n        console.error('Failed to load business types:', error);\r\n        // You might want to show a notification here\r\n      } finally {\r\n        setLoadingBusinessTypes(false);\r\n      }\r\n    };\r\n\r\n    loadBusinessTypes();\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    const { name, value, type } = e.target;\r\n\r\n    if (type === 'checkbox') {\r\n      const checked = (e.target as HTMLInputElement).checked;\r\n      setFormData(prev => ({ ...prev, [name]: checked }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (file: File | null) => {\r\n    setFormData(prev => ({ ...prev, image: file }));\r\n\r\n    // Clear error when image is changed\r\n    if (errors.image) {\r\n      setErrors(prev => ({ ...prev, image: '' }));\r\n    }\r\n  };\r\n\r\n  const validateUserForm = () => {\r\n    const formValidationRules = {\r\n      name: [validationRules.required('Name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      type: [validationRules.required('User type is required')],\r\n      password: [validationRules.required('Password is required'), validationRules.password()],\r\n      confirmPassword: [validationRules.required('Confirm password is required'), validationRules.passwordMatch()],\r\n      address: [validationRules.required('Address is required')],\r\n      businessType: [validationRules.required('Business type is required')],\r\n    };\r\n\r\n    const newErrors = validateForm(formData, formValidationRules);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateUserForm()) {\r\n      const submitData = {\r\n        ...formData,\r\n        // Include image field, even if null\r\n        image: formData.image\r\n      };\r\n      onSubmit(submitData);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n        <FormField\r\n          label=\"Full Name\"\r\n          name=\"name\"\r\n          value={formData.name}\r\n          onChange={handleChange}\r\n          error={errors.name}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Email\"\r\n          name=\"email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          error={errors.email}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"User Type\"\r\n          name=\"type\"\r\n          type=\"select\"\r\n          value={formData.type}\r\n          onChange={handleChange}\r\n          error={errors.type}\r\n          required\r\n          options={[\r\n            { value: 'customer', label: 'Customer' },\r\n            { value: 'supplier', label: 'Supplier' }\r\n          ]}\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Phone Number\"\r\n          name=\"phone\"\r\n          value={formData.phone}\r\n          onChange={handleChange}\r\n          error={errors.phone}\r\n        />\r\n\r\n        <FormField\r\n          label=\"Address\"\r\n          name=\"address\"\r\n          type=\"textarea\"\r\n          value={formData.address}\r\n          onChange={handleChange}\r\n          error={errors.address}\r\n          required\r\n          placeholder=\"Enter full address\"\r\n        />\r\n\r\n        <FormField\r\n          label=\"Business Type\"\r\n          name=\"businessType\"\r\n          type=\"select\"\r\n          value={formData.businessType}\r\n          onChange={handleChange}\r\n          error={errors.businessType}\r\n          required\r\n          loading={loadingBusinessTypes}\r\n          options={[\r\n            { value: '', label: 'Select Business Type' },\r\n            ...businessTypes.map(type => ({\r\n              value: type.id,\r\n              label: type.name\r\n            }))\r\n          ]}\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Password\"\r\n          name=\"password\"\r\n          type=\"password\"\r\n          value={formData.password}\r\n          onChange={handleChange}\r\n          error={errors.password}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Confirm Password\"\r\n          name=\"confirmPassword\"\r\n          type=\"password\"\r\n          value={formData.confirmPassword}\r\n          onChange={handleChange}\r\n          error={errors.confirmPassword}\r\n          required\r\n        />\r\n      </div>\r\n\r\n      {/* Image Upload Field */}\r\n      <ImageUpload\r\n        label=\"Profile Picture\"\r\n        name=\"image\"\r\n        value={formData.image}\r\n        onChange={handleImageChange}\r\n        error={errors.image || undefined}\r\n        maxSize={5 * 1024 * 1024} // 5MB\r\n        allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\r\n      />\r\n\r\n      <div className=\"flex items-center\">\r\n        <FormField\r\n          label=\"Send invitation email\"\r\n          name=\"sendInvite\"\r\n          type=\"checkbox\"\r\n          value={formData.sendInvite}\r\n          onChange={handleChange}\r\n          className=\"flex items-center space-x-2\"\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          type=\"submit\"\r\n          loading={isLoading}\r\n        >\r\n          Add User\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddUserForm;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Edit User Form Component\r\n *\r\n * This component provides a form for editing existing users.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport Card from '../../../components/common/Card';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport type { User, UserFormDataFrontend } from '../types/index';\r\n\r\ninterface EditUserFormProps {\r\n  user: User;\r\n  onSubmit: (userData: UserFormDataFrontend) => Promise<void>;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst EditUserForm: React.FC<EditUserFormProps> = ({ \r\n  user, \r\n  onSubmit, \r\n  isLoading = false \r\n}) => {\r\n  const [formData, setFormData] = useState<UserFormDataFrontend>({\r\n    name: '',\r\n    email: '',\r\n    type: 'customer',\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  // Initialize form with user data\r\n  useEffect(() => {\r\n    if (user) {\r\n      setFormData({\r\n        name: user.name,\r\n        email: user.email,\r\n        type: user.type,\r\n      });\r\n    }\r\n  }, [user]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n    const { name, value, type } = e.target;\r\n\r\n    if (type === 'checkbox') {\r\n      const checked = (e.target as HTMLInputElement).checked;\r\n      setFormData(prev => ({ ...prev, [name]: checked }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const validateUserForm = () => {\r\n    const formValidationRules = {\r\n      name: [validationRules.required('Name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      type: [validationRules.required('User type is required')]\r\n    };\r\n    \r\n    const newErrors = validateForm(formData, formValidationRules);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateUserForm()) {\r\n      try {\r\n        await onSubmit(formData);\r\n        // Success notification is handled in the parent component\r\n      } catch (error) {\r\n        // Error handling is done in the parent component\r\n        console.error('Form submission error:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\r\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n          <div>\r\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n              Full Name <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              value={formData.name}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['name'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['name'] && <p className=\"mt-1 text-sm text-red-600\">{errors['name']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n              Email <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              id=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['email'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['email'] && <p className=\"mt-1 text-sm text-red-600\">{errors['email']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700\">\r\n              User Type <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <select\r\n              id=\"type\"\r\n              name=\"type\"\r\n              value={formData.type}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['type'] ? 'border-red-300' : ''\r\n              }`}\r\n            >\r\n              <option value=\"customer\">Customer</option>\r\n              <option value=\"supplier\">Supplier</option>\r\n            </select>\r\n            {errors['type'] && <p className=\"mt-1 text-sm text-red-600\">{errors['type']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n              New Password\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              id=\"password\"\r\n              name=\"password\"\r\n              value={formData.password || ''}\r\n              onChange={handleChange}\r\n              placeholder=\"Leave blank to keep current password\"\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['password'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['password'] && <p className=\"mt-1 text-sm text-red-600\">{errors['password']}</p>}\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex justify-end space-x-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            type=\"button\"\r\n            onClick={() => window.history.back()}\r\n            disabled={isLoading}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            type=\"submit\"\r\n            loading={isLoading}\r\n          >\r\n            Save Changes\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EditUserForm;\r\n", "import React from 'react';\r\nimport DetailSection from './DetailSection';\r\nimport type { Order } from '../../features/orders/types';\r\nimport { formatCurrency, formatDate } from '../../utils/formatters';\r\nimport StatusBadge from './StatusBadge';\r\nimport Button from './Button';\r\nimport { EyeIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface OrdersSectionProps {\r\n  orders: Order[];\r\n  title?: string;\r\n  description?: string;\r\n  onViewOrder?: (order: Order) => void;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nconst OrdersSection: React.FC<OrdersSectionProps> = ({\r\n  orders,\r\n  title = 'Orders',\r\n  description = 'Recent orders',\r\n  onViewOrder,\r\n  emptyMessage = 'No orders found',\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <DetailSection\r\n      title={title}\r\n      description={description}\r\n      className={className}\r\n    >\r\n      {orders.length === 0 ? (\r\n        <div className=\"px-4 py-5 text-center text-sm text-gray-500\">\r\n          {emptyMessage}\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Order ID\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Date\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Amount\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {orders.map((order) => (\r\n                <tr key={order.id} className=\"hover:bg-gray-50\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-primary\">\r\n                    {order.id}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {formatDate(order.orderDate)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {formatCurrency(order.totalAmount)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <StatusBadge status={order.status} type=\"order\" />\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    {onViewOrder && (\r\n                      <Button\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        onClick={() => onViewOrder(order)}\r\n                        icon={<EyeIcon className=\"w-4 h-4 text-black\" />}\r\n                        className=\"text-black hover:text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        View\r\n                      </Button>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n    </DetailSection>\r\n  );\r\n};\r\n\r\nexport default OrdersSection;", "/**\r\n * User Details Component\r\n *\r\n * This component displays detailed information about a user.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport DetailList from '../../../components/common/DetailList';\r\nimport DetailItem from '../../../components/common/DetailItem';\r\nimport OrdersSection from '../../../components/common/OrdersSection';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport type{ User } from '../types';\r\nimport type { Order } from '../../orders/types';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface UserDetailsProps {\r\n  user: User;\r\n  userOrders: Order[];\r\n}\r\n\r\nconst UserDetails: React.FC<UserDetailsProps> = ({ user, userOrders = [] }) => {\r\n  const navigate = useNavigate();\r\n\r\n  const handleViewOrder = (order: Order) => {\r\n    navigate(ROUTES.getOrderDetailsRoute(order.id));\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <DetailSection\r\n        title=\"User Information\"\r\n        description=\"Personal details and application\"\r\n      >\r\n        <DetailList>\r\n          <DetailItem label=\"Full name\" value={user.name} />\r\n          <DetailItem label=\"Email address\" value={user.email} />\r\n          <DetailItem label=\"User type\" value={user.type} />\r\n          <DetailItem label=\"Status\" value={<StatusBadge status={user.status} type=\"user\" />} />\r\n          <DetailItem label=\"Last login\" value={user.lastLogin} />\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      <OrdersSection\r\n        orders={userOrders}\r\n        title=\"User Orders\"\r\n        description=\"Orders placed by this user\"\r\n        onViewOrder={handleViewOrder}\r\n        emptyMessage=\"This user has not placed any orders yet\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDetails;", "/**\r\n * User Details Modal Component\r\n *\r\n * This component displays detailed information about a user in a modal format.\r\n * It's specifically designed for the modal popup and matches the Supplier Details modal design.\r\n */\r\n\r\nimport React from 'react';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ User } from '../types';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface UserDetailsModalProps {\r\n  user: User;\r\n}\r\n\r\nconst UserDetailsModal: React.FC<UserDetailsModalProps> = ({ user }) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Avatar\r\n            {...(user.avatar && { src: user.avatar })}\r\n            alt={user.name}\r\n            name={user.name}\r\n            size=\"xl\"\r\n          />\r\n          <div>\r\n            <h3 className=\"text-lg font-medium text-gray-900\">{user.name}</h3>\r\n            <p className=\"text-sm text-gray-500\">ID: {user.id}</p>\r\n            <div className=\"mt-1\">\r\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                user.status === 'active'\r\n                  ? 'bg-green-100 text-green-800'\r\n                  : 'bg-red-100 text-red-800'\r\n              }`}>\r\n                {user.status.charAt(0).toUpperCase() + user.status.slice(1)}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4\">\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Contact Information</h4>\r\n          <dl className=\"space-y-3\">\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Email</dt>\r\n              <dd className=\"text-sm text-gray-900\">{user.email}</dd>\r\n            </div>\r\n            {user.phone && (\r\n              <div>\r\n                <dt className=\"text-xs text-gray-500\">Phone</dt>\r\n                <dd className=\"text-sm text-gray-900\">{user.phone}</dd>\r\n              </div>\r\n            )}\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Last Login</dt>\r\n              <dd className=\"text-sm text-gray-900\">{user.lastLogin}</dd>\r\n            </div>\r\n          </dl>\r\n        </div>\r\n\r\n        {user.address && (\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Address</h4>\r\n            <address className=\"not-italic text-sm text-gray-900\">\r\n              {user.address}\r\n            </address>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {user.businessType && (\r\n        <div className=\"border-t border-gray-200 pt-4\">\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Business Type</h4>\r\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n            {user.businessType}\r\n          </span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Account Status</h4>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {user.status === 'active' ? (\r\n            <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />\r\n          ) : (\r\n            <XCircleIcon className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n          <span className=\"text-sm text-gray-700\">\r\n            {user.status === 'active'\r\n              ? `Active since ${user.lastLogin}`\r\n              : 'Account is banned'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDetailsModal;\r\n", "/**\r\n * User List Component\r\n *\r\n * This component displays a list of users in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport BaseEntityList from '../../../components/common/EntityList/BaseEntityList';\r\nimport type { Column } from '../../../components/common/DataTable';\r\nimport type { User } from '../types';\r\nimport {\r\n  EnvelopeIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  EyeIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface UserListProps {\r\n  users: User[];\r\n  onViewUser: (user: User) => void;\r\n  onEditUser: (user: User) => void;\r\n  onDeleteUser: (user: User) => void;\r\n  onUserClick: (user: User) => void;\r\n  title?: string;\r\n  loading?: boolean;\r\n}\r\n\r\nconst UserList: React.FC<UserListProps> = ({\r\n  users,\r\n  onViewUser,\r\n  onEditUser: _onEditUser,\r\n  onDeleteUser,\r\n  onUserClick: _onUserClick, // Keep for interface compatibility but use navigation instead\r\n  title = 'Users',\r\n  loading = false\r\n}) => {\r\n  const navigate = useNavigate();\r\n\r\n  // Handle row click to navigate to user edit page (which serves as details page)\r\n  const handleRowClick = (user: User) => {\r\n    navigate(ROUTES.getUserEditRoute(user.id));\r\n  };\r\n  \r\n  // Define user-specific columns\r\n  const columns: Column<User>[] = [\r\n    {\r\n      key: 'name',\r\n      label: 'Name',\r\n      sortable: true,\r\n      render: (_value, user) => (\r\n        <div className=\"flex items-center\">\r\n          {user.avatar ? (\r\n            <img\r\n              src={user.avatar}\r\n              alt={user.name}\r\n              className=\"w-8 h-8 rounded-full mr-3 object-cover\"\r\n            />\r\n          ) : (\r\n            <div className=\"w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3\">\r\n              {user.name.charAt(0)}\r\n            </div>\r\n          )}\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{user.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {user.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'email',\r\n      label: 'Email',\r\n      sortable: true,\r\n      render: (value) => (\r\n        <div className=\"flex items-center\">\r\n          <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    { key: 'type', label: 'Type', sortable: true },\r\n    { key: 'status', label: 'Status', sortable: true },\r\n    { key: 'lastLogin', label: 'Last Login', sortable: true },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_, user) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onViewUser(user);\r\n            }}\r\n          >\r\n            <EyeIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              navigate(ROUTES.getUserEditRoute(user.id));\r\n            }}\r\n          >\r\n            <PencilIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDeleteUser(user);\r\n            }}\r\n          >\r\n            <TrashIcon className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <BaseEntityList<User>\r\n      data={users}\r\n      columns={columns}\r\n      onRowClick={handleRowClick}\r\n      title={title}\r\n      pagination={true}\r\n      loading={loading}\r\n      emptyMessage=\"No users found\"\r\n    />\r\n  );\r\n};\r\n\r\nexport default UserList;\r\n\r\n\r\n\r\n", "import React from 'react';\r\nimport { CurrencyDollarIcon, ShoppingCartIcon, ClockIcon } from '@heroicons/react/24/outline';\r\nimport { Line } from 'react-chartjs-2';\r\nimport MetricsSection from '../../../components/common/MetricsSection';\r\nimport type { Metric } from '../../../components/common/MetricsSection';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\nimport { defaultLineChartOptions } from '../../../utils/chartConfig';\r\n\r\ninterface UserAnalyticsProps {\r\n  userId: string;\r\n  userData: {\r\n    totalOrders: number;\r\n    totalSpent: number;\r\n    averageOrderValue: number;\r\n    orderFrequency: number;\r\n    orderHistory: {\r\n      date: string;\r\n      amount: number;\r\n    }[];\r\n  };\r\n}\r\n\r\nconst UserAnalytics: React.FC<UserAnalyticsProps> = ({\r\n  userId: _userId,\r\n  userData\r\n}) => {\r\n  // Validate userData to prevent runtime errors\r\n  const safeUserData = {\r\n    totalOrders: userData?.totalOrders || 0,\r\n    totalSpent: userData?.totalSpent || 0,\r\n    averageOrderValue: userData?.averageOrderValue || 0,\r\n    orderFrequency: userData?.orderFrequency || 0,\r\n    orderHistory: userData?.orderHistory || []\r\n  };\r\n\r\n\r\n\r\n  // Prepare metrics with safe data and new analytics\r\n  const metrics: Metric[] = [\r\n    {\r\n      title: 'Total Orders',\r\n      value: safeUserData.totalOrders,\r\n      icon: <ShoppingCartIcon className=\"w-6 h-6 text-blue-500\" />\r\n    },\r\n    {\r\n      title: 'Total Spent',\r\n      value: formatCurrency(safeUserData.totalSpent),\r\n      icon: <CurrencyDollarIcon className=\"w-6 h-6 text-green-500\" />\r\n    },\r\n    {\r\n      title: 'Average Order',\r\n      value: formatCurrency(safeUserData.averageOrderValue),\r\n      icon: <ClockIcon className=\"w-6 h-6 text-purple-500\" />\r\n    },\r\n  ];\r\n  \r\n  // Helper function to format date for chart labels\r\n  const formatDateForChart = (dateString: string): string => {\r\n    try {\r\n      const date = new Date(dateString);\r\n      // Format as MM/DD or MM/DD/YY for better readability\r\n      return date.toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: '2-digit'\r\n      });\r\n    } catch (error) {\r\n      // Fallback to original string if date parsing fails\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  // Prepare spending trend data for Line chart\r\n  const prepareSpendingTrendData = () => {\r\n    if (safeUserData.orderHistory.length === 0) {\r\n      return {\r\n        labels: ['No Data'],\r\n        datasets: [{\r\n          label: 'Spending Trend',\r\n          data: [0],\r\n          borderColor: '#F28B22',\r\n          backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n          fill: true,\r\n          tension: 0.4\r\n        }]\r\n      };\r\n    }\r\n\r\n    // Sort orders by date and calculate cumulative spending\r\n    const sortedOrders = safeUserData.orderHistory\r\n      .filter(item => item && item.date && typeof item.amount === 'number')\r\n      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());\r\n\r\n    let cumulativeSpending = 0;\r\n    const trendData = sortedOrders.map(order => {\r\n      cumulativeSpending += order.amount;\r\n      return {\r\n        label: formatDateForChart(order.date),\r\n        value: cumulativeSpending\r\n      };\r\n    });\r\n\r\n    return {\r\n      labels: trendData.map(item => item.label),\r\n      datasets: [{\r\n        label: 'Cumulative Spending',\r\n        data: trendData.map(item => item.value),\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n        fill: true,\r\n        tension: 0.4,\r\n        pointBackgroundColor: '#F28B22',\r\n        pointBorderColor: '#ffffff',\r\n        pointBorderWidth: 2,\r\n        pointRadius: 4\r\n      }]\r\n    };\r\n  };\r\n\r\n  const spendingTrendData = prepareSpendingTrendData();\r\n  \r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <MetricsSection metrics={metrics} />\r\n      \r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Spending Trend Chart */}\r\n        <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\r\n          <div className=\"mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">Spending Trend</h3>\r\n            <p className=\"text-sm text-gray-500\">\r\n              {safeUserData.orderHistory.length > 0\r\n                ? \"Cumulative spending over time\"\r\n                : \"No spending data available\"\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {safeUserData.orderHistory.length > 0 ? (\r\n            <div className=\"h-80\">\r\n              <Line\r\n                data={spendingTrendData}\r\n                options={{\r\n                  ...defaultLineChartOptions,\r\n                  plugins: {\r\n                    ...defaultLineChartOptions.plugins,\r\n                    title: {\r\n                      display: false\r\n                    },\r\n                    legend: {\r\n                      display: false\r\n                    }\r\n                  },\r\n                  scales: {\r\n                    ...defaultLineChartOptions.scales,\r\n                    y: {\r\n                      ...defaultLineChartOptions.scales?.y,\r\n                      ticks: {\r\n                        ...defaultLineChartOptions.scales?.y?.ticks,\r\n                        callback: function(value) {\r\n                          return formatCurrency(value as number);\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n          ) : (\r\n            <div className=\"h-80 flex items-center justify-center\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-gray-400 text-4xl mb-4\">�</div>\r\n                <p className=\"text-gray-500 text-lg font-medium\">No Spending Data</p>\r\n                <p className=\"text-gray-400 text-sm mt-2\">\r\n                  Spending trends will appear here once the user places orders\r\n                </p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Order Frequency Analysis */}\r\n        <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\r\n          <div className=\"mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">Order Frequency</h3>\r\n            <p className=\"text-sm text-gray-500\">Order patterns and frequency analysis</p>\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {safeUserData.orderHistory.length > 0 ? (\r\n              <>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Most Recent Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {(() => {\r\n                      const lastOrder = safeUserData.orderHistory[safeUserData.orderHistory.length - 1];\r\n                      return lastOrder?.date ? formatDateForChart(lastOrder.date) : 'N/A';\r\n                    })()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">First Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {(() => {\r\n                      const firstOrder = safeUserData.orderHistory[0];\r\n                      return firstOrder?.date ? formatDateForChart(firstOrder.date) : 'N/A';\r\n                    })()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Largest Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {formatCurrency(Math.max(...safeUserData.orderHistory.map(order => order.amount)))}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Smallest Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {formatCurrency(Math.min(...safeUserData.orderHistory.map(order => order.amount)))}\r\n                  </span>\r\n                </div>\r\n              </>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <div className=\"text-gray-400 text-3xl mb-3\">📊</div>\r\n                <p className=\"text-gray-500 font-medium\">No Order Data</p>\r\n                <p className=\"text-gray-400 text-sm mt-1\">\r\n                  Order frequency analysis will appear here\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserAnalytics;", "/**\r\n * API Endpoints\r\n * \r\n * This file contains constants for all API endpoints.\r\n */\r\n\r\nexport const ENDPOINTS = {\r\n  // Auth endpoints\r\n  AUTH: {\r\n    LOGIN: '/auth/login',\r\n    LOGOUT: '/auth/logout',\r\n    CURRENT_USER: '/auth/me',\r\n    FORGOT_PASSWORD: '/auth/forgot-password',\r\n    RESET_PASSWORD: '/auth/reset-password',\r\n  },\r\n  \r\n  // User endpoints\r\n  USERS: {\r\n    BASE: '/users',\r\n    DETAILS: (id: string) => `/users/${id}`,\r\n    STATUS: (id: string) => `/users/${id}/status`,\r\n    BAN: (id: string) => `/users/${id}/ban`,\r\n    UNBAN: (id: string) => `/users/${id}/unban`,\r\n    UPLOAD_IMAGE: '/users/upload-image',\r\n  },\r\n  \r\n  // Supplier endpoints\r\n  SUPPLIERS: {\r\n    BASE: '/suppliers',\r\n    DETAILS: (id: string) => `/suppliers/${id}`,\r\n    VERIFY: (id: string) => `/suppliers/${id}/verify`,\r\n    REJECT: (id: string) => `/suppliers/${id}/reject`,\r\n    PRODUCTS: (id: string) => `/suppliers/${id}/products`,\r\n  },\r\n  \r\n  // Category endpoints\r\n  CATEGORIES: {\r\n    BASE: '/categories',\r\n    DETAILS: (id: string) => `/categories/${id}`,\r\n    PRODUCTS: (id: string) => `/categories/${id}/products`,\r\n  },\r\n\r\n  // Business Types endpoints\r\n  BUSINESS_TYPES: {\r\n    BASE: '/business-types',\r\n  },\r\n  \r\n  // Order endpoints\r\n  ORDERS: {\r\n    BASE: '/orders',\r\n    DETAILS: (id: string) => `/orders/${id}`,\r\n    APPROVE: (id: string) => `/orders/${id}/approve`,\r\n    REJECT: (id: string) => `/orders/${id}/reject`,\r\n    COMPLETE: (id: string) => `/orders/${id}/complete`,\r\n  },\r\n  \r\n  // Dashboard endpoints\r\n  DASHBOARD: {\r\n    STATS: '/dashboard/stats',\r\n    SALES: '/dashboard/sales',\r\n    USER_GROWTH: '/dashboard/user-growth',\r\n    CATEGORY_DISTRIBUTION: '/dashboard/category-distribution',\r\n    RECENT_ORDERS: '/dashboard/recent-orders',\r\n    SALES_CHART: '/dashboard/sales-chart',\r\n  },\r\n};\r\n", "/**\n * User API Data Transformers\n * \n * This file provides utilities to transform data between frontend and backend formats\n * for the users API, handling field name differences and data structure variations.\n */\n\nimport type { \n  User, \n  BackendUser, \n  UserFormData, \n  UserFormDataFrontend,\n  ApiResponseWrapper,\n  UserQueryParams\n} from '../types';\n\n/**\n * Transform frontend user form data to backend format\n */\nexport const transformUserFormToBackend = (frontendData: UserFormDataFrontend): UserFormData => {\n  const result: UserFormData = {\n    Name: frontendData.name,\n    Email: frontendData.email,\n    verificationStatus: frontendData.verificationStatus || 'pending'\n  };\n\n  if (frontendData.password !== undefined) {\n    result.password = frontendData.password;\n  }\n  if (frontendData.phone !== undefined) {\n    result.PhoneNumber = frontendData.phone;\n  }\n  if (frontendData.address !== undefined) {\n    result.Address = frontendData.address;\n  }\n  if (frontendData.businessType !== undefined) {\n    result.BusinessType = frontendData.businessType;\n  }\n\n  return result;\n};\n\n/**\n * Transform backend user data to frontend format\n */\nexport const transformBackendUserToFrontend = (backendUser: BackendUser): User => {\n  const result: User = {\n    id: backendUser.id,\n    name: backendUser.name,\n    email: backendUser.email,\n    type: backendUser.type,\n    status: backendUser.status,\n    verificationStatus: backendUser.verificationStatus || 'pending'\n  };\n\n  if (backendUser.avatar !== undefined) {\n    result.avatar = backendUser.avatar;\n  }\n  if (backendUser.phone !== undefined) {\n    result.phone = backendUser.phone;\n  }\n  if (backendUser.address !== undefined) {\n    result.address = backendUser.address;\n  }\n  if (backendUser.businessType !== undefined) {\n    result.businessType = backendUser.businessType;\n  }\n  if (backendUser.lastLogin !== undefined) {\n    result.lastLogin = backendUser.lastLogin;\n  }\n\n  return result;\n};\n\n/**\n * Transform frontend query parameters to backend format\n */\nexport const transformQueryParamsToBackend = (params: UserQueryParams): Record<string, any> => {\n  const backendParams: Record<string, any> = {};\n  \n  if (params.page !== undefined) backendParams.page = params.page;\n  if (params.limit !== undefined) backendParams.limit = params.limit;\n  if (params.search !== undefined) backendParams.search = params.search;\n  if (params.status !== undefined) backendParams.status = params.status;\n  if (params.sort !== undefined) backendParams.sort = params.sort;\n  if (params.order !== undefined) backendParams.order = params.order;\n  \n  return backendParams;\n};\n\n/**\n * Validate backend API response structure\n */\nexport const validateBackendResponse = <T>(response: any): ApiResponseWrapper<T> => {\n  if (!response) {\n    throw new Error('Empty response received');\n  }\n\n  if (typeof response !== 'object') {\n    throw new Error('Invalid response format');\n  }\n\n  if (!('success' in response)) {\n    throw new Error('Response missing success field');\n  }\n\n  if (response.success === false) {\n    throw new Error(response.message || 'Request failed');\n  }\n\n  if (!('data' in response)) {\n    throw new Error('Response missing data field');\n  }\n\n  return response as ApiResponseWrapper<T>;\n};\n\n/**\n * Transform backend user list response to frontend format\n */\nexport const transformUserListResponse = (backendResponse: any): ApiResponseWrapper<User[]> => {\n  const validatedResponse = validateBackendResponse<BackendUser[]>(backendResponse);\n\n  const transformedUsers = validatedResponse.data.map(transformBackendUserToFrontend);\n\n  const result: ApiResponseWrapper<User[]> = {\n    success: validatedResponse.success,\n    message: validatedResponse.message,\n    data: transformedUsers\n  };\n\n  if (validatedResponse.pagination !== undefined) {\n    result.pagination = validatedResponse.pagination;\n  }\n\n  return result;\n};\n\n/**\n * Transform backend single user response to frontend format\n */\nexport const transformUserResponse = (backendResponse: any): ApiResponseWrapper<User> => {\n  const validatedResponse = validateBackendResponse<BackendUser>(backendResponse);\n  \n  const transformedUser = transformBackendUserToFrontend(validatedResponse.data);\n  \n  return {\n    success: validatedResponse.success,\n    message: validatedResponse.message,\n    data: transformedUser\n  };\n};\n\n/**\n * Extract error message from backend response\n */\nexport const extractErrorMessage = (errorResponse: any): string => {\n  if (typeof errorResponse === 'string') {\n    return errorResponse;\n  }\n  \n  if (errorResponse?.message) {\n    return errorResponse.message;\n  }\n  \n  if (errorResponse?.error) {\n    return errorResponse.error;\n  }\n  \n  return 'An unexpected error occurred';\n};\n", "/**\r\n * Users API Service\r\n *\r\n * This file provides methods for interacting with the users API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { ENDPOINTS } from '../../../constants/endpoints';\r\nimport type {\r\n  User,\r\n  UserFormDataFrontend,\r\n  UserQueryParams,\r\n  UserStatusUpdate,\r\n  ImageUploadResponse,\r\n  ApiResponseWrapper\r\n} from '../types';\r\nimport {\r\n  transformUserFormToBackend,\r\n  transformUserListResponse,\r\n  transformUserResponse,\r\n  transformQueryParamsToBackend,\r\n  validateBackendResponse,\r\n  extractErrorMessage\r\n} from '../utils/apiTransformers';\r\n\r\n/**\r\n * Users API service with methods for managing user data\r\n */\r\nexport const usersApi = {\r\n  /**\r\n   * Get all users with optional filtering and pagination\r\n   * @param params - Optional query parameters for filtering users\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  getUsers: async (params?: UserQueryParams): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      const backendParams = params ? transformQueryParamsToBackend(params) : {};\r\n      const response = await apiClient.get(ENDPOINTS.USERS.BASE, { params: backendParams });\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      return transformUserListResponse(response.data);\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a user by their ID\r\n   * @param id - The user's unique identifier\r\n   * @returns Promise resolving to a single user\r\n   */\r\n  getUserById: async (id: string): Promise<User> => {\r\n    try {\r\n      const response = await apiClient.get(ENDPOINTS.USERS.DETAILS(id));\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new user\r\n   * @param userData - The user data to create\r\n   * @returns Promise resolving to the created user\r\n   */\r\n  createUser: async (userData: UserFormDataFrontend): Promise<User> => {\r\n    try {\r\n      // Transform frontend data to backend format\r\n      const backendData = transformUserFormToBackend(userData);\r\n      const response = await apiClient.post(ENDPOINTS.USERS.BASE, backendData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing user\r\n   * @param id - The user's unique identifier\r\n   * @param userData - The user data to update\r\n   * @returns Promise resolving to the updated user\r\n   */\r\n  updateUser: async (id: string, userData: UserFormDataFrontend): Promise<User> => {\r\n    try {\r\n      // Transform frontend data to backend format\r\n      const backendData = transformUserFormToBackend(userData);\r\n      const response = await apiClient.put(ENDPOINTS.USERS.DETAILS(id), backendData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a user\r\n   * @param id - The user's unique identifier\r\n   * @returns Promise resolving to a success indicator\r\n   */\r\n  deleteUser: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(ENDPOINTS.USERS.DETAILS(id));\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // For delete operations, we just need to check success\r\n      if (response.data && typeof response.data === 'object' && 'success' in response.data && !response.data.success) {\r\n        const errorMessage = 'message' in response.data ? response.data.message : 'Delete operation failed';\r\n        throw new Error(errorMessage as string);\r\n      }\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n  \r\n  /**\r\n   * Update a user's status\r\n   * @param id - The user's unique identifier\r\n   * @param status - The new status to set\r\n   * @returns Promise resolving to void\r\n   */\r\n  updateUserStatus: async (id: string, status: 'active' | 'banned'): Promise<void> => {\r\n    try {\r\n      const statusData: UserStatusUpdate = { status };\r\n      const response = await apiClient.put(ENDPOINTS.USERS.STATUS(id), statusData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // For status update operations, we just need to check success\r\n      if (response.data && typeof response.data === 'object' && 'success' in response.data && !response.data.success) {\r\n        const errorMessage = 'message' in response.data ? response.data.message : 'Status update failed';\r\n        throw new Error(errorMessage as string);\r\n      }\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Search for users by name or email\r\n   * @param query - The search query string\r\n   * @param params - Additional query parameters\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  searchUsers: async (query: string, params?: Omit<UserQueryParams, 'search'>): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      const searchParams: UserQueryParams = { ...params, search: query };\r\n      return await usersApi.getUsers(searchParams);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get users filtered by type\r\n   * @param type - The user type to filter by\r\n   * @param params - Additional query parameters\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  getUsersByType: async (_type: 'customer' | 'supplier', params?: Omit<UserQueryParams, 'type'>): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      // Note: Removed 'admin' type as it's not part of the regular user interface\r\n      const typeParams: UserQueryParams = { ...params };\r\n      // Add type filtering logic here if the backend supports it\r\n      // For now, we'll get all users and filter on the frontend if needed\r\n      return await usersApi.getUsers(typeParams);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload user image\r\n   * @param file - The image file to upload\r\n   * @returns Promise resolving to the uploaded image URL\r\n   */\r\n  uploadUserImage: async (file: File): Promise<ImageUploadResponse> => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n\r\n      const response = await apiClient.post(ENDPOINTS.USERS.UPLOAD_IMAGE, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Validate backend response\r\n      const validatedResponse = validateBackendResponse<ImageUploadResponse>(response.data);\r\n      return validatedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\n// Export individual methods for more flexible importing\r\nexport const {\r\n  getUsers,\r\n  getUserById,\r\n  createUser,\r\n  updateUser,\r\n  deleteUser,\r\n  updateUserStatus,\r\n  searchUsers,\r\n  getUsersByType,\r\n  uploadUserImage\r\n} = usersApi;\r\n\r\nexport default usersApi;\r\n\r\n\r\n", "// src/features/users/hooks/useUsers.ts\r\n/**\r\n * Users Hook\r\n * \r\n * This hook provides methods and state for working with users.\r\n */\r\n\r\nimport { useCallback, useRef, useEffect } from 'react';\r\nimport { useEntityData } from '../../../hooks/useEntityData';\r\nimport usersApi from '../api/usersApi';\r\nimport type { User, UserFormDataFrontend, UserQueryParams } from '../types';\r\nimport useNotification from '../../../hooks/useNotification';\r\nimport apiClient from '../../../api';\r\n\r\nexport const useUsers = (options = { initialFetch: true }) => {\r\n  // Clear any cached users data on hook initialization\r\n  useEffect(() => {\r\n    // Clear cache for users endpoint\r\n    apiClient.clearCache();\r\n  }, []);\r\n\r\n  // Create an adapter that maps usersApi methods to what useEntityData expects\r\n  // Note: We need to adapt the new paginated response format\r\n  const apiAdapter = {\r\n    getAll: async () => {\r\n      const response = await usersApi.getUsers();\r\n      return response.data; // Extract just the data array for useEntityData\r\n    },\r\n    getById: usersApi.getUserById,\r\n    create: usersApi.createUser,\r\n    update: usersApi.updateUser,\r\n    delete: usersApi.deleteUser\r\n  };\r\n\r\n  const baseHook = useEntityData<User>(apiAdapter, {\r\n    entityName: 'users',\r\n    initialFetch: options.initialFetch\r\n  });\r\n\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues\r\n  const showNotificationRef = useRef(showNotification);\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n  \r\n  // User-specific methods\r\n  const updateUserStatus = useCallback(async (id: string, status: 'active' | 'banned') => {\r\n    try {\r\n      await usersApi.updateUserStatus(id, status);\r\n\r\n      // Update the local state immediately using setEntities\r\n      const currentEntities = baseHook.entities as User[];\r\n      const updatedEntities = currentEntities.map(user =>\r\n        user.id === id ? { ...user, status } : user\r\n      );\r\n\r\n      // Use the exposed setEntities function to update the state\r\n      baseHook.setEntities(updatedEntities);\r\n\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `User ${status === 'active' ? 'activated' : 'banned'} successfully`\r\n      });\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to ${status === 'active' ? 'activate' : 'ban'} user`\r\n      });\r\n      throw error;\r\n    }\r\n  }, [baseHook]);\r\n  \r\n  // Add updateUser method\r\n  const updateUser = useCallback(async (id: string, userData: UserFormDataFrontend) => {\r\n    try {\r\n      const updatedUser = await usersApi.updateUser(id, userData);\r\n\r\n      // Update the local state using setEntities\r\n      const currentEntities = baseHook.entities as User[];\r\n      const updatedEntities = currentEntities.map(user =>\r\n        user.id === id ? updatedUser : user\r\n      );\r\n\r\n      // Use the exposed setEntities function to update the state\r\n      baseHook.setEntities(updatedEntities);\r\n\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'User updated successfully'\r\n      });\r\n\r\n      return updatedUser;\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update user'\r\n      });\r\n      throw error;\r\n    }\r\n  }, [baseHook]);\r\n\r\n  // Add search method with pagination support\r\n  const searchUsers = useCallback(async (query: string, params?: Omit<UserQueryParams, 'search'>) => {\r\n    try {\r\n      return await usersApi.searchUsers(query, params);\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to search users'\r\n      });\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Add method to get users with pagination\r\n  const getUsersWithPagination = useCallback(async (params?: UserQueryParams) => {\r\n    try {\r\n      return await usersApi.getUsers(params);\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch users'\r\n      });\r\n      throw error;\r\n    }\r\n  }, []);\r\n  \r\n  return {\r\n    ...baseHook,\r\n    users: baseHook.entities as User[], // Rename for clarity\r\n    fetchUsers: baseHook.fetchEntities, // Rename for clarity\r\n    getUserById: baseHook.getEntityById, // Rename for clarity\r\n    createEntity: baseHook.createEntity, // Expose create method\r\n    deleteEntity: baseHook.deleteEntity, // Expose delete method\r\n    updateUserStatus, // Updated method name\r\n    updateUser, // Add the new method to the return object\r\n    searchUsers, // Add search method\r\n    getUsersWithPagination // Add pagination method\r\n  };\r\n};\r\n\r\nexport default useUsers;", "/**\r\n * Image Upload Component\r\n * \r\n * A reusable component for uploading and previewing images with drag and drop support.\r\n */\r\n\r\nimport React, { useState, useRef, useCallback } from 'react';\r\nimport { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { validateFile } from '../../utils/errorHandling';\r\n\r\ninterface ImageUploadProps {\r\n  label: string;\r\n  name: string;\r\n  value?: File | string | null;\r\n  onChange: (file: File | null) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  maxSize?: number; // in bytes\r\n  allowedTypes?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst ImageUpload: React.FC<ImageUploadProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  disabled = false,\r\n  maxSize = 5 * 1024 * 1024, // 5MB default\r\n  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n  className = ''\r\n}) => {\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const [preview, setPreview] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Generate preview URL when value changes\r\n  React.useEffect(() => {\r\n    if (value instanceof File) {\r\n      const url = URL.createObjectURL(value);\r\n      setPreview(url);\r\n      return () => URL.revokeObjectURL(url);\r\n    } else if (typeof value === 'string' && value) {\r\n      setPreview(value);\r\n      return;\r\n    } else {\r\n      setPreview(null);\r\n      return;\r\n    }\r\n  }, [value]);\r\n\r\n  const handleFileSelect = useCallback((file: File) => {\r\n    const validation = validateFile(file, {\r\n      maxSize,\r\n      allowedTypes\r\n    });\r\n\r\n    if (validation.valid) {\r\n      onChange(file);\r\n    } else {\r\n      // Handle validation error - you might want to show this error\r\n      console.error('File validation failed:', validation.error);\r\n    }\r\n  }, [maxSize, allowedTypes, onChange]);\r\n\r\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    if (!disabled) {\r\n      setIsDragOver(true);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    \r\n    if (disabled) return;\r\n\r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleClick = () => {\r\n    if (!disabled && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        className={`\r\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\r\n          ${isDragOver ? 'border-primary bg-primary bg-opacity-5' : 'border-gray-300'}\r\n          ${error ? 'border-red-300' : ''}\r\n          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-gray-50'}\r\n        `}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={handleClick}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          name={name}\r\n          accept={allowedTypes.join(',')}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n          disabled={disabled}\r\n        />\r\n\r\n        {preview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={preview}\r\n              alt=\"Preview\"\r\n              className=\"mx-auto h-32 w-32 object-cover rounded-lg\"\r\n            />\r\n            {!disabled && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  handleRemove();\r\n                }}\r\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\"\r\n              >\r\n                <XMarkIcon className=\"h-4 w-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <div className=\"mt-4\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isDragOver ? 'Drop image here' : 'Click to upload or drag and drop'}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                PNG, JPG, GIF up to {Math.round(maxSize / 1024 / 1024)}MB\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageUpload;\r\n", "/**\r\n * Chart.js Configuration\r\n *\r\n * This file configures Chart.js and registers all necessary components.\r\n */\r\n\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler,\r\n  RadialLinearScale,\r\n  type ChartOptions\r\n} from 'chart.js';\r\n\r\n// Register ChartJS components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement, // Required for Pie and Doughnut charts\r\n  RadialLinearScale,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n);\r\n\r\n// Default chart options\r\nexport const defaultLineChartOptions: ChartOptions<'line'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultPieChartOptions: ChartOptions<'pie'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      position: 'bottom',\r\n      labels: {\r\n        color: '#000000',\r\n        font: {\r\n          size: 12\r\n        }\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultBarChartOptions: ChartOptions<'bar'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Helper function to destroy chart instances\r\nexport const destroyChart = (chartInstance: ChartJS | null) => {\r\n  if (chartInstance) {\r\n    chartInstance.destroy();\r\n  }\r\n};\r\n\r\nexport default ChartJS;\r\n", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": ["useEntityData", "apiService", "options", "entities", "setEntities", "useState", "isLoading", "setIsLoading", "error", "setError", "showNotification", "useNotification", "apiServiceRef", "useRef", "showNotificationRef", "entityNameRef", "entityName", "hasInitialFetched", "useEffect", "current", "fetchEntities", "useCallback", "async", "data", "getAll", "params", "err", "type", "title", "message", "getEntityById", "getById", "id", "createEntity", "newEntity", "create", "prev", "updateEntity", "updatedEntity", "update", "map", "entity", "deleteEntity", "delete", "filter", "initialFetch", "console", "log", "_ref", "children", "className", "_jsx", "PhotoIcon", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "getIconBackgroundClass", "icon", "colorMatch", "match", "formatValue", "value", "toString", "Card", "_jsxs", "iconElement", "colorClass", "total", "undefined", "growth", "toFixed", "metrics", "metric", "index", "MetricCard", "parseFloat", "change", "PencilIcon", "label", "name", "onChange", "required", "placeholder", "disabled", "loading", "inputClasses", "htmlFor", "renderField", "option", "checked", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "trim", "length", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "validateForm", "values", "validationRules", "errors", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "formData", "ruleArray", "rule", "validator", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "description", "businessTypesApi", "getBusinessTypes", "response", "apiClient", "get", "responseValidators", "getList", "handleApiError", "getBusinessTypeById", "onSubmit", "onCancel", "setFormData", "address", "businessType", "sendInvite", "image", "setErrors", "businessTypes", "setBusinessTypes", "loadingBusinessTypes", "setLoadingBusinessTypes", "types", "loadBusinessTypes", "handleChange", "e", "target", "preventDefault", "validateUserForm", "formValidationRules", "newErrors", "keys", "submitData", "FormField", "ImageUpload", "file", "maxSize", "allowedTypes", "<PERSON><PERSON>", "variant", "onClick", "user", "window", "history", "back", "orders", "onViewOrder", "emptyMessage", "DetailSection", "scope", "order", "formatDate", "orderDate", "formatCurrency", "totalAmount", "StatusBadge", "status", "size", "EyeIcon", "userOrders", "navigate", "useNavigate", "DetailList", "DetailItem", "lastLogin", "OrdersSection", "ROUTES", "getOrderDetailsRoute", "Avatar", "avatar", "src", "alt", "char<PERSON>t", "toUpperCase", "slice", "CheckCircleIcon", "XCircleIcon", "users", "onViewUser", "onEditUser", "_onEditUser", "onDeleteUser", "onUserClick", "_onUserClick", "columns", "sortable", "render", "_value", "EnvelopeIcon", "_", "stopPropagation", "getUserEditRoute", "TrashIcon", "BaseEntityList", "onRowClick", "pagination", "_defaultLineChartOpti", "_defaultLineChartOpti2", "_defaultLineChartOpti3", "userId", "_userId", "userData", "safeUserData", "totalOrders", "totalSpent", "averageOrderValue", "orderFrequency", "orderHistory", "ShoppingCartIcon", "CurrencyDollarIcon", "ClockIcon", "formatDateForChart", "toLocaleDateString", "month", "day", "year", "spendingTrendData", "prepareSpendingTrendData", "labels", "datasets", "borderColor", "backgroundColor", "tension", "sortedOrders", "item", "amount", "sort", "a", "b", "cumulativeSpending", "trendData", "pointBackgroundColor", "pointBorderColor", "pointBorderWidth", "pointRadius", "MetricsSection", "Line", "defaultLineChartOptions", "plugins", "display", "legend", "scales", "y", "ticks", "callback", "_Fragment", "lastOrder", "firstOrder", "Math", "ENDPOINTS", "BASE", "DETAILS", "STATUS", "BAN", "UNBAN", "UPLOAD_IMAGE", "transformUserFormToBackend", "frontendData", "result", "Name", "Email", "verificationStatus", "PhoneNumber", "Address", "BusinessType", "transformBackendUserToFrontend", "backendUser", "validateBackendResponse", "Error", "success", "transformUserResponse", "backendResponse", "validatedResponse", "transformedUser", "extractErrorMessage", "errorResponse", "usersApi", "getUsers", "backendParams", "page", "limit", "search", "transformQueryParamsToBackend", "transformedUsers", "transformUserListResponse", "_error$response", "errorMessage", "getUserById", "_error$response2", "createUser", "backendData", "post", "_error$response3", "updateUser", "put", "_error$response4", "deleteUser", "_error$response5", "updateUserStatus", "statusData", "_error$response6", "searchUsers", "query", "searchParams", "getUsersByType", "_type", "typeParams", "uploadUserImage", "FormData", "append", "headers", "_error$response7", "clearCache", "apiAdapter", "baseHook", "updatedEntities", "updatedUser", "getUsersWithPagination", "fetchUsers", "isDragOver", "setIsDragOver", "preview", "setPreview", "fileInputRef", "File", "createObjectURL", "revokeObjectURL", "handleFileSelect", "validation", "validateFile", "valid", "onDragOver", "onDragLeave", "onDrop", "_e$dataTransfer$files", "dataTransfer", "files", "handleClick", "click", "accept", "join", "_e$target$files", "XMarkIcon", "round", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "RadialLinearScale", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "responsive", "maintainAspectRatio", "color", "font", "weight", "tooltip", "titleColor", "bodyColor", "padding", "displayColors", "x", "grid", "beginAtZero", "defaultPieChartOptions", "position", "destroy<PERSON>hart", "chartInstance", "destroy"], "sourceRoot": ""}