"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[300],{3593:(e,s,t)=>{t.d(s,{A:()=>n});var r=t(5043),a=t(579);const l=e=>{let{title:s,subtitle:t,children:r,className:l="",bodyClassName:n="",headerClassName:i="",footerClassName:o="",icon:d,footer:c,onClick:m,hoverable:u=!1,noPadding:x=!1,bordered:g=!0,loading:p=!1,testId:h}=e;const y=`\n    bg-white rounded-xl ${g?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${l}\n  `,b=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${i}\n  `,f=`\n    ${x?"":"p-6"}\n    ${n}\n  `,v=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return p?(0,a.jsxs)("div",{className:y,"data-testid":h,children:[(s||t||d)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{className:"w-full",children:[s&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),t&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),d&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:f,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),c&&(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:y,onClick:m,"data-testid":h,children:[(s||t||d)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{children:["string"===typeof s?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:s}):s,"string"===typeof t?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:t}):t]}),d&&(0,a.jsx)("div",{className:"text-primary",children:d})]}),(0,a.jsx)("div",{className:f,children:r}),c&&(0,a.jsx)("div",{className:v,children:c})]})},n=(0,r.memo)(l)},3927:(e,s,t)=>{t.d(s,{A:()=>a});t(5043);var r=t(579);const a=e=>{let{size:s="md",className:t="",variant:a="spinner",color:l="#F28B22",useCurrentColor:n=!1}=e;const i={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=n?"currentColor":l;return"spinner"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${t}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${i[s].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${t}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[s].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${i[s].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${i[s].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${t}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[s].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${t}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[s].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,r.jsx)("div",{className:`${i[s].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},7422:(e,s,t)=>{t.d(s,{A:()=>x});var r=t(5043);function a(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const l=r.forwardRef(a);function n(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const i=r.forwardRef(n);var o=t(2517),d=t(3927),c=t(1308),m=t(579);function u(e){let{columns:s,data:t,onRowClick:a,title:n,description:u,loading:x=!1,pagination:g=!0,pageSize:p=c.PI.DEFAULT_PAGE_SIZE,selectable:h=!0,onSelectionChange:y,actions:b,emptyMessage:f="No results found",className:v="",headerClassName:j="",bodyClassName:N="",footerClassName:w="",rowClassName:k,initialSortKey:$,initialSortDirection:C="asc",testId:L}=e;const[S,A]=(0,r.useState)($?{key:$,direction:C}:null),[M,E]=(0,r.useState)(""),[I,z]=(0,r.useState)(1),[P,_]=(0,r.useState)([]),[B,O]=(0,r.useState)(null),R=(0,r.useMemo)((()=>S?[...t].sort(((e,s)=>{const t=e[S.key],r=s[S.key];return null==t&&null==r?0:null==t?"asc"===S.direction?-1:1:null==r?"asc"===S.direction?1:-1:"string"===typeof t&&"string"===typeof r?"asc"===S.direction?t.localeCompare(r):r.localeCompare(t):t<r?"asc"===S.direction?-1:1:t>r?"asc"===S.direction?1:-1:0})):t),[t,S]),W=(0,r.useMemo)((()=>M?R.filter((e=>Object.entries(e).some((e=>{let[s,t]=e;return null!==t&&void 0!==t&&("object"!==typeof t&&String(t).toLowerCase().includes(M.toLowerCase()))})))):R),[R,M]),F=Math.ceil(W.length/p),D=(0,r.useMemo)((()=>{const e=(I-1)*p;return W.slice(e,e+p)}),[W,I,p]),T=e=>{z(e)},Z=e=>{let s="bg-gray-100 text-gray-800";if("string"===typeof e){const t=e.toLowerCase();t.includes("active")||t.includes("approved")||t.includes("verified")||t.includes("completed")||t.includes("success")?s="bg-green-100 text-green-800":t.includes("pending")||t.includes("processing")?s="bg-yellow-100 text-yellow-800":t.includes("rejected")||t.includes("banned")||t.includes("failed")||t.includes("error")?s="bg-red-100 text-red-800":t.includes("inactive")&&(s="bg-gray-100 text-gray-800")}return(0,m.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s}`,children:e})};return(0,m.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${v}`,"data-testid":L,children:[(n||u)&&(0,m.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${j}`,children:["string"===typeof n?(0,m.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:n}):n,"string"===typeof u?(0,m.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:u}):u]}),(0,m.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,m.jsxs)("div",{className:"relative flex-1",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(l,{className:"h-5 w-5 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:M,onChange:e=>{E(e.target.value),z(1)},"data-testid":`${L}-search`})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[P.length>0&&(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)("span",{className:"text-sm text-gray-500",children:[P.length," selected"]}),(0,m.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{_([]),y&&y([])},"data-testid":`${L}-clear-selection`,children:"Clear"})]}),b]})]}),(0,m.jsx)("div",{className:`overflow-x-auto ${N}`,children:x?(0,m.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,m.jsx)(d.A,{size:"lg",variant:"spinner"})}):(0,m.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,m.jsx)("thead",{className:"bg-gray-50",children:(0,m.jsxs)("tr",{children:[h&&(0,m.jsx)("th",{className:"w-12 px-6 py-3",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const s=e.target.checked?Array.from({length:D.length},((e,s)=>s)):[];if(_(s),y){const e=s.map((e=>D[e])).filter((e=>void 0!==e));y(e)}},checked:P.length===D.length&&D.length>0,"data-testid":`${L}-select-all`})}),s.map((e=>(0,m.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let s="asc";S&&S.key===e&&"asc"===S.direction&&(s="desc"),A({key:e,direction:s})})(e.key),"data-testid":`${L}-column-${e.key}`,children:(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)("span",{children:e.label}),e.sortable&&(0,m.jsx)("span",{className:"transition-colors duration-200 "+((null===S||void 0===S?void 0:S.key)===e.key?"text-primary":"text-gray-400"),children:(null===S||void 0===S?void 0:S.key)===e.key&&"asc"===S.direction?(0,m.jsx)(i,{className:"h-4 w-4"}):(null===S||void 0===S?void 0:S.key)===e.key&&"desc"===S.direction?(0,m.jsx)(o.A,{className:"h-4 w-4"}):(0,m.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,m.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:D.length>0?D.map(((e,t)=>(0,m.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${P.includes(t)?"bg-primary bg-opacity-5":""}\n                    ${B===t?"bg-gray-50":""}\n                    ${k?k(e,t):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>O(t),onMouseLeave:()=>O(null),"data-testid":`${L}-row-${t}`,children:[h&&(0,m.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:P.includes(t),onChange:()=>{},onClick:e=>((e,s)=>{s.stopPropagation();const t=[...P];if(P.includes(e)){const s=t.indexOf(e);t.splice(s,1)}else t.push(e);if(_(t),y){const e=t.map((e=>D[e])).filter((e=>void 0!==e));y(e)}})(t,e),"data-testid":`${L}-row-${t}-checkbox`})}),s.map((s=>(0,m.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${s.align||"left"} ${s.className||""}`,"data-testid":`${L}-row-${t}-cell-${s.key}`,children:s.render?s.render(e[s.key],e):s.key.toLowerCase().includes("status")?Z(e[s.key]):e[s.key]},s.key)))]},t))):(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:s.length+(h?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${L}-empty-message`,children:f})})})]})}),g&&F>1&&(0,m.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${w}`,children:[(0,m.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(I-1)*p+1," to ",Math.min(I*p,W.length)," of ",W.length," entries"]}),(0,m.jsxs)("div",{className:"flex space-x-1",children:[(0,m.jsx)("button",{onClick:()=>T(Math.max(1,I-1)),disabled:1===I,className:"px-3 py-1 rounded-md text-sm "+(1===I?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,F)},((e,s)=>{let t;return t=F<=5||I<=3?s+1:I>=F-2?F-4+s:I-2+s,(0,m.jsx)("button",{onClick:()=>T(t),className:"px-3 py-1 rounded-md text-sm "+(I===t?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-${t}`,children:t},t)})),(0,m.jsx)("button",{onClick:()=>T(Math.min(F,I+1)),disabled:I===F,className:"px-3 py-1 rounded-md text-sm "+(I===F?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-next`,children:"Next"})]})]})]})}const x=(0,r.memo)(u)},7907:(e,s,t)=>{t.d(s,{A:()=>n});var r=t(5043),a=t(579);const l=e=>{let{children:s,variant:t="primary",size:r="md",className:l="",onClick:n,disabled:i=!1,type:o="button",icon:d,iconPosition:c="left",fullWidth:m=!1,loading:u=!1,rounded:x=!1,href:g,target:p,rel:h,title:y,ariaLabel:b,testId:f}=e;const v=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[t]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[r]}\n    ${i?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${x?"rounded-full":"rounded-lg"}\n    ${l}\n  `,j=(0,a.jsxs)(a.Fragment,{children:[u&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d&&"left"===c&&!u&&(0,a.jsx)("span",{className:"mr-2",children:d}),s,d&&"right"===c&&(0,a.jsx)("span",{className:"ml-2",children:d})]});return g?(0,a.jsx)("a",{href:g,className:v,target:p,rel:h||("_blank"===p?"noopener noreferrer":void 0),onClick:n,title:y,"aria-label":b,"data-testid":f,children:j}):(0,a.jsx)("button",{type:o,className:v,onClick:n,disabled:i||u,title:y,"aria-label":b,"data-testid":f,children:j})},n=(0,r.memo)(l)}}]);
//# sourceMappingURL=300.275b6140.chunk.js.map