"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[426,525],{245:(e,t,r)=>{r.d(t,{Ji:()=>u,F9:()=>i,h:()=>x});var s=r(5043),a=r(5445),n=r(4692),l=r(3893),c=r(579);const o=e=>{let{orders:t,onOrderClick:r,title:o="Orders",loading:i=!1}=e;const d=(0,s.useMemo)((()=>[{key:"id",label:"Order ID",sortable:!0,render:e=>(0,c.jsx)("span",{className:"font-medium text-black",children:e})},{key:"customerName",label:"Customer",sortable:!0},{key:"supplierName",label:"Supplier",sortable:!0},{key:"totalAmount",label:"Total Amount",sortable:!0,render:e=>(0,l.vv)(e)},{key:"status",label:"Status",sortable:!0,render:e=>(0,c.jsx)(n.A,{status:e||"pending",type:"order"})},{key:"orderDate",label:"Order Date",sortable:!0},{key:"deliveryDate",label:"Delivery Date",sortable:!0}]),[]);return(0,c.jsx)(a.A,{data:t,columns:d,onRowClick:r,title:o,pagination:!0,loading:i,emptyMessage:"No orders found"})},i=(0,s.memo)(o);var d=r(7907);const u=e=>{let{activeFilter:t,onFilterChange:r}=e;return(0,c.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,c.jsx)(d.A,{variant:"all"===t?"primary":"outline",size:"sm",onClick:()=>r("all"),children:"All Orders"}),(0,c.jsx)(d.A,{variant:"pending"===t?"primary":"outline",size:"sm",onClick:()=>r("pending"),children:"Pending"}),(0,c.jsx)(d.A,{variant:"approved"===t?"primary":"outline",size:"sm",onClick:()=>r("approved"),children:"Approved"}),(0,c.jsx)(d.A,{variant:"completed"===t?"primary":"outline",size:"sm",onClick:()=>r("completed"),children:"Completed"}),(0,c.jsx)(d.A,{variant:"rejected"===t?"primary":"outline",size:"sm",onClick:()=>r("rejected"),children:"Rejected"})]})};var m=r(1568),p=r(4703),y=r(8479);const h={getOrders:async e=>{try{const t=await m.A.get("/orders",{params:e});return y.lg.getList(t,"orders")}catch(t){throw(0,p.hS)(t)}},getOrderById:async e=>{try{const t=await m.A.get(`/orders/${e}`);return y.lg.getById(t,"order",e)}catch(t){throw(0,p.hS)(t)}},createOrder:async e=>{try{const t=await m.A.post("/orders",e);return y.lg.create(t,"order")}catch(t){throw(0,p.hS)(t)}},updateOrder:async(e,t)=>{try{const r=await m.A.put(`/orders/${e}`,t);return y.lg.update(r,"order",e)}catch(r){throw(0,p.hS)(r)}},deleteOrder:async e=>{try{const t=await m.A.delete(`/orders/${e}`);return y.lg.delete(t,"order",e)}catch(t){throw(0,p.hS)(t)}},updateOrderStatus:async(e,t)=>{try{const r=await m.A.put(`/orders/${e}/status`,{status:t});return y.lg.update(r,"order",e)}catch(r){throw(0,p.hS)(r)}},getOrdersByStatus:async e=>{try{const t=await m.A.get("/orders",{params:{status:e}});return y.lg.getList(t,"orders",!0)}catch(t){throw(0,p.hS)(t)}},getOrdersByCustomer:async e=>{try{const t=await m.A.get("/orders",{params:{customerId:e}});return y.lg.getList(t,"orders",!0)}catch(t){throw(0,p.hS)(t)}}};var g=r(9705);const x=()=>{const[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)(!1),[n,l]=(0,s.useState)(null),{showNotification:c}=(0,g.A)(),o=(0,s.useRef)(c);(0,s.useEffect)((()=>{o.current=c}));const i=(0,s.useCallback)((async()=>{a(!0),l(null);try{const e=await h.getOrders();t(e)}catch(e){l(e),o.current({type:"error",title:"Error",message:"Failed to fetch orders"})}finally{a(!1)}}),[]),d=(0,s.useCallback)((async e=>{a(!0),l(null);try{return await h.getOrderById(e)}catch(t){throw l(t),o.current({type:"error",title:"Error",message:`Failed to fetch order ${e}`}),t}finally{a(!1)}}),[]),u=(0,s.useCallback)((async(e,r)=>{a(!0),l(null);try{const s=await h.updateOrder(e,r);return t((t=>t.map((t=>t.id===e?s:t)))),o.current({type:"success",title:"Success",message:"Order updated successfully"}),s}catch(s){throw l(s),o.current({type:"error",title:"Error",message:"Failed to update order"}),s}finally{a(!1)}}),[]),m=(0,s.useCallback)((async e=>{a(!0),l(null);try{const r=await h.updateOrderStatus(e,"rejected");return t((t=>t.map((t=>t.id===e?r:t)))),o.current({type:"success",title:"Success",message:"Order cancelled successfully"}),r}catch(r){throw l(r),o.current({type:"error",title:"Error",message:"Failed to cancel order"}),r}finally{a(!1)}}),[]),p=(0,s.useCallback)((async e=>{a(!0),l(null);try{return await h.getOrdersByStatus(e)}catch(t){throw l(t),o.current({type:"error",title:"Error",message:`Failed to fetch orders with status ${e}`}),t}finally{a(!1)}}),[]),y=(0,s.useCallback)((async e=>{a(!0),l(null);try{return await h.getOrdersByCustomer(e)}catch(t){throw l(t),o.current({type:"error",title:"Error",message:`Failed to fetch orders for customer ${e}`}),t}finally{a(!1)}}),[]),x=(0,s.useCallback)((async e=>{a(!0),l(null);try{await h.deleteOrder(e),t((t=>t.filter((t=>t.id!==e)))),o.current({type:"success",title:"Success",message:"Order deleted successfully"})}catch(r){l(r);const e=r instanceof Error?r.message:"Failed to delete order";throw o.current({type:"error",title:"Error",message:e}),r}finally{a(!1)}}),[]);return(0,s.useEffect)((()=>{i()}),[i]),{orders:e,isLoading:r,error:n,fetchOrders:i,getOrderById:d,updateOrder:u,cancelOrder:m,deleteOrder:x,getOrdersByStatus:p,getOrdersByCustomer:y}};r(6906)},2806:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(5043),a=r(5475),n=r(5501),l=r(6365),c=r(579);const o=e=>{let{title:t,description:r,actions:s,breadcrumbs:o,className:i="",testId:d}=e;return(0,c.jsxs)("div",{className:`mb-6 ${i}`,"data-testid":d,children:[o&&o.length>0&&(0,c.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,c.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,c.jsx)("li",{children:(0,c.jsx)(a.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,c.jsx)(n.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,c.jsxs)("li",{className:"flex items-center",children:[(0,c.jsx)(l.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,c.jsx)(a.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,c.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,c.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),r&&"string"===typeof r?(0,c.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r}):r]}),s&&(0,c.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:s})]})]})},i=(0,s.memo)(o)},3893:(e,t,r)=>{r.d(t,{Yq:()=>s,v7:()=>n,vv:()=>a});const s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const r=new Date(e),s={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",s).format(r)}catch(r){return console.error("Error formatting date:",r),e}},a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(r,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(s){return console.error("Error formatting currency:",s),`${t} ${e.toFixed(2)}`}},n=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,t)).toFixed(2))} ${["Bytes","KB","MB","GB","TB"][t]}`}},4692:(e,t,r)=>{r.d(t,{A:()=>i});r(5043);var s=r(4538),a=r(7012),n=r(7098),l=r(5889),c=r(3867),o=r(579);const i=e=>{let{status:t,type:r="user",className:i=""}=e;if(!t)return(0,o.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${i}`,children:"Unknown"});const d=t.toLowerCase();let u="",m=null;"active"===d||"verified"===d||"completed"===d?(u="bg-green-100 text-green-800",m=(0,o.jsx)(s.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(u="bg-blue-100 text-blue-800",m=(0,o.jsx)(a.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(u="bg-red-100 text-red-800",m=(0,o.jsx)(n.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(u="bg-purple-100 text-purple-800",m=(0,o.jsx)(l.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(u="bg-yellow-100 text-yellow-800",m=(0,o.jsx)(c.A,{className:"w-4 h-4 mr-1"})):u="bg-gray-100 text-gray-800";const p=t?t.charAt(0).toUpperCase()+t.slice(1):"Unknown";return(0,o.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${u} ${i}`,children:[m,p]})}},5445:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(7422),a=r(579);const n=e=>{let{data:t,columns:r,onRowClick:n,title:l,pagination:c=!0,loading:o=!1,emptyMessage:i="No data available",className:d=""}=e;return(0,a.jsx)(s.A,{columns:r,data:t,onRowClick:n,title:l,pagination:c,loading:o,emptyMessage:i,className:d})}},6365:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const n=s.forwardRef(a)},6906:(e,t,r)=>{r.d(t,{getMockOrderById:()=>c,getMockOrders:()=>l,mapMockOrderToOrder:()=>n});var s=r(4624);const a=e=>({id:e.id,name:e.productName,quantity:e.quantity,unitPrice:e.unitPrice,description:`Product ID: ${e.productId}`,sku:e.productId}),n=e=>{const t=e=>e?new Date(e).toISOString():(new Date).toISOString();return{id:e.id,customerName:e.customerName,supplierName:e.supplierName,totalAmount:e.totalAmount,status:"cancelled"===e.status?"rejected":e.status,orderDate:t(e.orderDate),deliveryDate:t(e.deliveryDate),items:e.items?e.items.map(a):[],notes:e.notes,...e.shippingAddress&&{shippingAddress:{street:e.shippingAddress.street,city:e.shippingAddress.city,state:e.shippingAddress.state,postalCode:e.shippingAddress.zipCode,country:e.shippingAddress.country}}}},l=()=>s.W.map(n),c=e=>{const t=String(e),r=s.W.find((e=>e.orderNumber===t||e.id===t||String(e.id)===t));if(r)return n(r)}}}]);
//# sourceMappingURL=426.3059cd2d.chunk.js.map