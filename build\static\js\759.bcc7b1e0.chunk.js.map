{"version": 3, "file": "static/js/759.bcc7b1e0.chunk.js", "mappings": "+PAsBA,MAAMA,EAA2BA,KAC/B,MAAMC,GAAWC,EAAAA,EAAAA,OACX,WAAEC,EAAU,UAAEC,EAAS,gBAAEC,IAAoBC,EAAAA,EAAAA,OAC7C,iBAAEC,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAwBC,IAA6BC,EAAAA,EAAAA,WAAS,GAK/DC,GAAsBC,EAAAA,EAAAA,cAAaC,IACvCb,EAASc,EAAAA,EAAOC,wBAAwBF,EAASG,IAAI,GACpD,CAAChB,IAEEiB,GAAoBL,EAAAA,EAAAA,cAAYM,UACpC,IAEET,GAA0B,SACpBL,IACNE,EAAiB,CACfa,KAAM,UACNC,MAAO,UACPC,QAAS,+BAEb,CAAE,MAAOC,GACPhB,EAAiB,CACfa,KAAM,QACNC,MAAO,QACPC,QAAS,0BAEb,IACC,CAACjB,EAAiBE,IAErB,OACEiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CACTP,MAAM,aACNQ,YAAY,4BACZC,SACEH,EAAAA,EAAAA,KAACI,EAAAA,EAAM,CACLC,MAAML,EAAAA,EAAAA,KAACM,EAAAA,EAAQ,CAACR,UAAU,YAC1BS,QAASA,IAAMxB,GAA0B,GAAMgB,SAChD,oBAMLC,EAAAA,EAAAA,KAACQ,EAAAA,EAAI,CAAAT,UACHC,EAAAA,EAAAA,KAACS,EAAAA,GAAY,CACXjC,WAAYA,EACZkC,gBAAiBzB,EACjB0B,QAASlC,OAIbuB,EAAAA,EAAAA,KAACY,EAAAA,EAAK,CACJC,OAAQ/B,EACRgC,QAASA,IAAM/B,GAA0B,GACzCW,MAAM,eAAcK,UAEpBC,EAAAA,EAAAA,KAACe,EAAAA,GAAe,CACdC,SAAUzB,EACV0B,SAAUA,IAAMlC,GAA0B,SAG1C,EAIV,GAAemC,EAAAA,EAAAA,MAAK7C,E", "sources": ["pages/CategoriesPage.tsx"], "sourcesContent": ["/**\r\n * Categories Page\r\n *\r\n * This page displays and manages categories in the system.\r\n */\r\n\r\nimport React, { useState, useCallback, memo } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Card from '../components/common/Card';\r\nimport Button from '../components/common/Button';\r\nimport Modal from '../components/common/Modal';\r\nimport { PlusIcon } from '@heroicons/react/24/outline';\r\nimport {\r\n  CategoryList,\r\n  AddCategoryForm,\r\n  useCategories,\r\n  Category\r\n} from '../features/categories';\r\nimport { ROUTES } from '../constants/routes';\r\nimport useNotification from '../hooks/useNotification';\r\n\r\nconst CategoriesPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const { categories, isLoading, fetchCategories } = useCategories();\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);\r\n  \r\n\r\n\r\n  // Memoize event handlers to prevent unnecessary re-renders\r\n  const handleCategoryClick = useCallback((category: Category) => {\r\n    navigate(ROUTES.getCategoryDetailsRoute(category.id));\r\n  }, [navigate]);\r\n\r\n  const handleAddCategory = useCallback(async (_categoryData: any) => {\r\n    try {\r\n      // Implementation\r\n      setIsAddCategoryModalOpen(false);\r\n      await fetchCategories();\r\n      showNotification({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Category added successfully'\r\n      });\r\n    } catch (error) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to add category'\r\n      });\r\n    }\r\n  }, [fetchCategories, showNotification]);\r\n  \r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title=\"Categories\"\r\n        description=\"Manage product categories\"\r\n        actions={\r\n          <Button\r\n            icon={<PlusIcon className=\"h-5 w-5\" />}\r\n            onClick={() => setIsAddCategoryModalOpen(true)}\r\n          >\r\n            Add Category\r\n          </Button>\r\n        }\r\n      />\r\n      \r\n      <Card>\r\n        <CategoryList\r\n          categories={categories}\r\n          onCategoryClick={handleCategoryClick}\r\n          loading={isLoading}\r\n        />\r\n      </Card>\r\n      \r\n      <Modal\r\n        isOpen={isAddCategoryModalOpen}\r\n        onClose={() => setIsAddCategoryModalOpen(false)}\r\n        title=\"Add Category\"\r\n      >\r\n        <AddCategoryForm\r\n          onSubmit={handleAddCategory}\r\n          onCancel={() => setIsAddCategoryModalOpen(false)}\r\n        />\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(CategoriesPage);\r\n"], "names": ["CategoriesPage", "navigate", "useNavigate", "categories", "isLoading", "fetchCategories", "useCategories", "showNotification", "useNotification", "isAddCategoryModalOpen", "setIsAddCategoryModalOpen", "useState", "handleCategoryClick", "useCallback", "category", "ROUTES", "getCategoryDetailsRoute", "id", "handleAddCategory", "async", "type", "title", "message", "error", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "description", "actions", "<PERSON><PERSON>", "icon", "PlusIcon", "onClick", "Card", "CategoryList", "onCategoryClick", "loading", "Modal", "isOpen", "onClose", "AddCategoryForm", "onSubmit", "onCancel", "memo"], "sourceRoot": ""}