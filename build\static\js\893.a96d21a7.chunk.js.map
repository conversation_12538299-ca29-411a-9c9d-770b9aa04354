{"version": 3, "file": "static/js/893.a96d21a7.chunk.js", "mappings": "oKA+BO,MAuMP,EAvM+B,WAA2C,IAA1CA,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjE,MAAM,oBAAEG,GAAsB,EAAI,gBAAEC,GAAkB,EAAI,QAAEC,GAAYN,GAClE,iBAAEO,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAqB,CACvDC,UAAU,EACVC,MAAO,KACPC,UAAW,OAIPC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BN,EAAc,CACZE,UAAU,EACVC,MAAO,KACPC,UAAW,MACX,GACD,IAGGG,GAA0BD,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KACvD,MAAMC,GAAWC,EAAAA,EAAAA,IACfP,EACAT,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAkBN,OAfAO,EAAc,CACZE,UAAU,EACVC,MAAOM,EACPL,UAAW,SACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,GAGVC,CAAQ,GACd,CAACf,EAAqBC,EAAiBE,EAAkBD,IAGtDqB,GAAiCX,EAAAA,EAAAA,cAAY,CACjDY,EACAJ,EACAK,EACAX,KAEA,MAAMY,GAAkBC,EAAAA,EAAAA,IAAsBH,EAAOJ,EAASK,GAqB9D,OAnBAnB,EAAc,CACZE,UAAU,EACVC,MAAOiB,EACPhB,UAAW,gBACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,mBACPC,QAASM,EAAgBN,UAIzBlB,GACFA,EAAQwB,EAAiBZ,GAGpBY,CAAe,GACrB,CAAC1B,EAAqBG,EAAkBD,IAGrC0B,GAA2BhB,EAAAA,EAAAA,cAAY,CAC3CH,EACAoB,EACAf,MAEAgB,EAAAA,EAAAA,IACErB,EACAoB,EACA7B,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAGNO,EAAc,CACZE,UAAU,EACVC,QACAC,UAAW,UACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,EACjB,GACC,CAACd,EAAqBC,EAAiBE,EAAkBD,IAGtD6B,GAAqBnB,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KAClD,MAAMkB,EAAWvB,aAAiBY,MAAQZ,EAAQ,IAAIY,MAAMY,OAAOxB,IA2BnE,OAzBAH,EAAc,CACZE,UAAU,EACVC,MAAOuB,EACPtB,UAAW,aACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,QACPC,QAASY,EAASZ,UAIlBnB,IACFqB,EAAAA,EAAAA,IAAYU,EAAUlB,IAGxBoB,EAAAA,EAAAA,IAASF,EAAUlB,GAEfZ,GACFA,EAAQO,EAAOK,GAGVkB,CAAQ,GACd,CAAChC,EAAqBC,EAAiBE,EAAkBD,IAGtDiC,GAAoBvB,EAAAA,EAAAA,cAAYwB,MACpCC,EACAvB,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAI,EAAwBJ,EAAOK,GACxB,IACT,IACC,CAACH,EAAYE,IAGVyB,GAAwB1B,EAAAA,EAAAA,cAAYwB,MACxCC,EACAR,EACAf,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAmB,EAAyBnB,EAAOoB,EAAef,GACxC,IACT,IACC,CAACH,EAAYiB,IAEhB,MAAO,IAEFvB,EAGHW,eAAgBH,EAChBc,sBAAuBJ,EACvBO,gBAAiBF,EACjBG,qBACApB,aAGAwB,oBACAG,wBAGAC,WAAa9B,GACXA,GAA0B,kBAAVA,GAAsB,WAAYA,EACpD+B,kBAAoB/B,GAClBA,GAA0B,kBAAVA,GAAsB,UAAWA,EAEvD,C,uFC3MA,MAAMgC,EAAwCC,IAOvC,IAPwC,MAC7CvB,EAAK,YACLwB,EAAW,QACXC,EAAO,YACPC,EAAW,UACXC,EAAY,GAAE,OACdC,GACDL,EACC,OACEM,EAAAA,EAAAA,MAAA,OACEF,UAAW,QAAQA,IACnB,cAAaC,EAAOE,SAAA,CAGnBJ,GAAeA,EAAY/C,OAAS,IACnCoD,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,YAAY,aAAW,aAAYG,UAChDD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oDAAmDG,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAG,IACHN,UAAU,uCACV,aAAW,OAAMG,UAEjBC,EAAAA,EAAAA,KAACG,EAAAA,EAAQ,CAACP,UAAU,gBAIvBD,EAAYS,KAAI,CAACC,EAAMC,KACtBR,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,oBAAmBG,SAAA,EAC3CC,EAAAA,EAAAA,KAACO,EAAAA,EAAgB,CAACX,UAAU,+BAC3BS,EAAKG,MAAQF,EAAQX,EAAY/C,OAAS,GACzCoD,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAIG,EAAKG,KACTZ,UAAU,qBAAoBG,SAE7BM,EAAKI,SAGRT,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,4BAA2BG,SAAEM,EAAKI,UAV7CH,WAmBjBR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EG,SAAA,EAC1FD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,mCAAkCG,SAAE9B,IACjDwB,GAAsC,kBAAhBA,GACrBO,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,6BAA4BG,SAAEN,IAE3CA,KAIHC,IACCM,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,oCAAmCG,SAC/CL,SAIH,EAIV,GAAegB,EAAAA,EAAAA,MAAKnB,E,yDCjEpB,MAAMoB,EAA4BnB,IAgB3B,IAhB4B,MACjCvB,EAAK,SACL2C,EAAQ,SACRb,EAAQ,UACRH,EAAY,GAAE,cACdiB,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBC,EAAI,OACJC,EAAM,QACNC,EAAO,UACPC,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACfzB,GACDL,EAEC,MAAM+B,EAAc,6BACIF,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFD,EAAU,iBAAmB,WAC7BtB,QAIE4B,EAAgB,mFAElBV,QAIEW,EAAc,SAChBL,EAAY,GAAK,cACjBP,QAIEa,EAAgB,4DAElBX,QAIJ,OAAIO,GAEAxB,EAAAA,EAAAA,MAAA,OAAKF,UAAW2B,EAAa,cAAa1B,EAAOE,SAAA,EAC7C9B,GAAS2C,GAAYI,KACrBlB,EAAAA,EAAAA,MAAA,OAAKF,UAAW4B,EAAczB,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQG,SAAA,CACpB9B,IAAS+B,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,gDACxBgB,IAAYZ,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wDAE7BoB,IAAQhB,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,uDAI5BI,EAAAA,EAAAA,KAAA,OAAKJ,UAAW6B,EAAY1B,UAC1BC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,6CAGhBqB,IACCjB,EAAAA,EAAAA,KAAA,OAAKJ,UAAW8B,EAAc3B,UAC5BC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,sDAQvBE,EAAAA,EAAAA,MAAA,OACEF,UAAW2B,EACXL,QAASA,EACT,cAAarB,EAAOE,SAAA,EAElB9B,GAAS2C,GAAYI,KACrBlB,EAAAA,EAAAA,MAAA,OAAKF,UAAW4B,EAAczB,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,CACoB,kBAAV9B,GACN+B,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,qCAAoCG,SAAE9B,IAEpDA,EAEmB,kBAAb2C,GACNZ,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,6BAA4BG,SAAEa,IAE3CA,KAGHI,IAAQhB,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,eAAcG,SAAEiB,QAI5ChB,EAAAA,EAAAA,KAAA,OAAKJ,UAAW6B,EAAY1B,SAAEA,IAE7BkB,IACCjB,EAAAA,EAAAA,KAAA,OAAKJ,UAAW8B,EAAc3B,SAC3BkB,MAGD,EAIV,GAAeP,EAAAA,EAAAA,MAAKC,E,8EChHpB,MAAMgB,EAA2EnC,IAAA,IAAC,MAChFjC,EAAK,WACLqE,GACDpC,EAAA,OACCM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yGAAwGG,SAAA,EACrHC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,6BAA4BG,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,0CAAyCG,SAAC,0BACxDC,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,iDAAgDG,SAC1DxC,EAAMW,SAAW,kCAEpB8B,EAAAA,EAAAA,KAAA,UACEkB,QAASU,EACThC,UAAU,6EAA4EG,SACvF,gBAGG,EAMD,SAAS8B,EACdC,GAEC,IADDC,EAA2BpF,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAE/B,MACEqF,SAAUC,EAAoBN,EAAoB,QAClD3E,EAAO,gBACPD,GAAkB,EAAI,QACtBa,GACEmE,EAEEG,GAAmBC,EAAAA,EAAAA,aAAmB,CAACC,EAAOC,KAiBhDrC,EAAAA,EAAAA,KAACsC,EAAAA,EAAa,CACZN,UAAUhC,EAAAA,EAAAA,KAACiC,EAAiB,CAAC1E,MAAO,IAAIY,MAASyD,WAAYA,IAAMW,OAAOC,SAASC,WACnFzF,QAlBgB0F,CAACnF,EAAcoF,KAE7B5F,IACFqB,EAAAA,EAAAA,IAAYb,EAAOK,GAAWkE,EAAUc,aAAed,EAAUe,KAAM,CACrEC,eAAgBH,EAAUG,eAC1BC,eAAe,IAKf/F,GACFA,EAAQO,EAAOoF,EACjB,EAMuB5C,UAErBC,EAAAA,EAAAA,KAAC8B,EAAS,IAAMM,EAAeC,IAAKA,QAQ1C,OAFAH,EAAiBU,YAAc,qBAAqBd,EAAUc,aAAed,EAAUe,QAEhFX,CACT,CAKO,MAsBP,G,qDCrGO,MAAMc,EAAa,SAACC,GAA0E,IAAtDvG,EAAmCC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKsG,EAAY,MAAO,IAExB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GAGhBG,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACF7G,GAGL,OAAO,IAAI8G,KAAKC,eAAe,QAASL,GAAgBM,OAAOR,EACjE,CAAE,MAAO3F,GAEP,OADAoG,QAAQpG,MAAM,yBAA0BA,GACjC0F,CACT,CACF,EAkBaW,EAAiB,SAC5BC,GAGY,IAFZC,EAAgBnH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,MACnBoH,EAAcpH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAI6G,KAAKQ,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBT,OAAOG,EACZ,CAAE,MAAOtG,GAEP,OADAoG,QAAQpG,MAAM,6BAA8BA,GACrC,GAAGuG,KAAYD,EAAOO,QAAQ,IACvC,CACF,EAkDaC,EAAkBC,IAC7B,GAAc,IAAVA,EAAa,MAAO,UAExB,MAEMC,EAAIC,KAAKC,MAAMD,KAAKE,IAAIJ,GAASE,KAAKE,IAFlC,OAIV,MAAO,GAAGC,YAAYL,EAAQE,KAAKI,IAJzB,KAIgCL,IAAIH,QAAQ,OAHxC,CAAC,QAAS,KAAM,KAAM,KAAM,MAGyBG,IAAI,C,uDC3GzE,MAsGA,EAtGsD/E,IAM/C,IANgD,KACrDqF,EAAO,KAAI,UACXjF,EAAY,GAAE,QACdkF,EAAU,UAAS,MACnBC,EAAQ,UAAS,gBACjBC,GAAkB,GACnBxF,EACC,MAAMyF,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiBD,EAGxD,MAAgB,YAAZD,GAEAhF,EAAAA,EAAAA,MAAA,OACEF,UAAW,oCAAoCA,IAC/C8F,KAAK,SACL,aAAW,UAAS3F,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,wDAAwDqF,EAAQJ,GAAMM,UACjFlB,MAAO,CACL0B,eAAgBF,EAChBG,iBAAkBH,MAGtBzF,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,UAASG,SAAC,kBAMhB,SAAZ+E,GAEAhF,EAAAA,EAAAA,MAAA,OACEF,UAAW,0DAA0DA,IACrE8F,KAAK,SACL,aAAW,UAAS3F,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMO,wBAC5BnB,MAAO,CAAE4B,gBAAiBJ,MAE5BzF,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMO,wBAC5BnB,MAAO,CAAE4B,gBAAiBJ,MAE5BzF,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMO,wBAC5BnB,MAAO,CAAE4B,gBAAiBJ,MAE5BzF,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,UAASG,SAAC,kBAMhB,UAAZ+E,GAEAhF,EAAAA,EAAAA,MAAA,OACEF,UAAW,oCAAoCA,IAC/C8F,KAAK,SACL,aAAW,UAAS3F,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMQ,kCAC5BpB,MAAO,CAAE4B,gBAAiBJ,MAE5BzF,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,UAASG,SAAC,kBAMhB,WAAZ+E,GAEAhF,EAAAA,EAAAA,MAAA,OACEF,UAAW,oCAAoCA,IAC/C8F,KAAK,SACL,aAAW,UAAS3F,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMS,oCAC5BrB,MAAO,CAAEc,MAAOU,GAAe1F,UAE/BC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,GAAGqF,EAAQJ,GAAMQ,0CAC5BpB,MAAO,CAAE4B,gBAAiBJ,QAG9BzF,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,UAASG,SAAC,kBAKzB,IAAI,C,gDC9Gb,SAAS+F,EAAQtG,EAIduG,GAAQ,IAJO,MAChB9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4JAEP,CACA,MACA,EADiCX,EAAAA,WAAiBH,E,gDCvBlD,SAASe,EAAiBrH,EAIvBuG,GAAQ,IAJgB,MACzB9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,iHAEP,CACA,MACA,EADiCX,EAAAA,WAAiBY,E,gDCvBlD,SAAStG,EAAgBf,EAItBuG,GAAQ,IAJe,MACxB9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiB1F,E,mECDlDuG,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAIK,MAAMC,EAAgD,CAC3DC,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACP7J,MAAO,CACL8J,SAAS,EACThD,MAAO,UACPiD,KAAM,CACJnD,KAAM,GACNoD,OAAQ,SAGZC,OAAQ,CACNH,SAAS,EACTI,OAAQ,CACNpD,MAAO,YAGXqD,QAAS,CACPvC,gBAAiB,UACjBwC,WAAY,UACZC,UAAW,UACXC,QAAS,GACTC,eAAe,IAGnBC,OAAQ,CACNC,EAAG,CACDC,KAAM,CACJZ,SAAS,GAEXa,MAAO,CACL7D,MAAO,YAGX8D,EAAG,CACDC,aAAa,EACbH,KAAM,CACJZ,SAAS,EACThD,MAAO,WAET6D,MAAO,CACL7D,MAAO,cAMFgE,EAA8C,CACzDnB,YAAY,EACZC,qBAAqB,EACrBC,QAAS,CACP7J,MAAO,CACL8J,SAAS,EACThD,MAAO,UACPiD,KAAM,CACJnD,KAAM,GACNoD,OAAQ,SAGZC,OAAQ,CACNc,SAAU,SACVb,OAAQ,CACNpD,MAAO,UACPiD,KAAM,CACJnD,KAAM,MAIZuD,QAAS,CACPvC,gBAAiB,UACjBwC,WAAY,UACZC,UAAW,UACXC,QAAS,MAsDFU,EAAgBC,IACvBA,GACFA,EAAcC,SAChB,C,yDCvIF,MAAMC,EAAgC5J,IAmB/B,IAnBgC,SACrCO,EAAQ,QACR+E,EAAU,UAAS,KACnBD,EAAO,KAAI,UACXjF,EAAY,GAAE,QACdsB,EAAO,SACPmI,GAAW,EAAK,KAChBrL,EAAO,SAAQ,KACfgD,EAAI,aACJsI,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBjI,GAAU,EAAK,QACfkI,GAAU,EAAK,KACfC,EAAI,OACJC,EAAM,IACNC,EAAG,MACH1L,EAAK,UACL2L,EAAS,OACT/J,GACDL,EACC,MAwBMqK,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTC,KAAM,2EACNC,KAAM,kFAiBWtF,WAdC,CAClBuF,GAAI,oBACJnF,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJ8E,GAAI,qBAUUzF,WAPQwE,EAAW,gCAAkC,yBAClDE,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5C5J,QAGE2K,GACJzK,EAAAA,EAAAA,MAAA0K,EAAAA,SAAA,CAAAzK,SAAA,CACGuB,IACCxB,EAAAA,EAAAA,MAAA,OACEF,UAAU,+CACVwG,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMvG,SAAA,EAElBC,EAAAA,EAAAA,KAAA,UACEJ,UAAU,aACV6K,GAAG,KACHC,GAAG,KACHC,EAAE,KACFnE,OAAO,eACPD,YAAY,OAEdvG,EAAAA,EAAAA,KAAA,QACEJ,UAAU,aACVyG,KAAK,eACLO,EAAE,uHAKP5F,GAAyB,SAAjBsI,IAA4BhI,IACnCtB,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,OAAMG,SAAEiB,IAGzBjB,EAEAiB,GAAyB,UAAjBsI,IACPtJ,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,OAAMG,SAAEiB,OAM9B,OAAIyI,GAEAzJ,EAAAA,EAAAA,KAAA,KACEyJ,KAAMA,EACN7J,UAAWiK,EACXH,OAAQA,EACRC,IAAKA,IAAmB,WAAXD,EAAsB,2BAAwB7M,GAC3DqE,QAASA,EACTjD,MAAOA,EACP,aAAY2L,EACZ,cAAa/J,EAAOE,SAEnBwK,KAOLvK,EAAAA,EAAAA,KAAA,UACEhC,KAAMA,EACN4B,UAAWiK,EACX3I,QAASA,EACTmI,SAAUA,GAAY/H,EACtBrD,MAAOA,EACP,aAAY2L,EACZ,cAAa/J,EAAOE,SAEnBwK,GACM,EAIb,GAAe7J,EAAAA,EAAAA,MAAK0I,E,gDC9JpB,SAASwB,EAAYpL,EAIlBuG,GAAQ,IAJW,MACpB9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,sOAEP,CACA,MACA,EADiCX,EAAAA,WAAiB2E,E,6ECMlD,MAAMC,EAA8BrL,IAiB7B,IAjB8B,OACnCsL,EAAM,QACNC,EAAO,MACP9M,EAAK,SACL8B,EAAQ,KACR8E,EAAO,KAAI,OACX5D,EAAM,WACN+J,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfvL,EAAY,GAAE,cACdiB,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBqK,EAAoB,GAAE,OACtBvL,GACDL,EACC,MAAM6L,GAAWC,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAgBC,IAChBT,GAAwB,WAAVS,EAAEC,KAClBX,GACF,EASF,OANID,IACFa,SAASC,iBAAiB,UAAWJ,GAErCG,SAASE,KAAK5H,MAAM6H,SAAW,UAG1B,KACLH,SAASI,oBAAoB,UAAWP,GACxCG,SAASE,KAAK5H,MAAM6H,SAAW,MAAM,CACtC,GACA,CAAChB,EAAQC,EAASC,KAGrBO,EAAAA,EAAAA,YAAU,KACR,IAAKT,IAAWO,EAASW,QAAS,OAElC,MAAMC,EAAoBZ,EAASW,QAAQE,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBrP,OAAc,OAEpC,MAAMuP,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBrP,OAAS,GAE3DyP,EAAgBZ,IACN,QAAVA,EAAEC,MAEFD,EAAEa,SACAX,SAASY,gBAAkBJ,IAC7BC,EAAYI,QACZf,EAAEgB,kBAGAd,SAASY,gBAAkBH,IAC7BD,EAAaK,QACbf,EAAEgB,kBAEN,EAMF,OAHAd,SAASC,iBAAiB,UAAWS,GACrCF,EAAaK,QAEN,KACLb,SAASI,oBAAoB,UAAWM,EAAa,CACtD,GACA,CAACvB,KAECA,EAAQ,OAAO,KAGpB,MAUM4B,GACJ5M,EAAAA,EAAAA,MAAC6M,EAAAA,SAAQ,CAAA5M,SAAA,EAEPC,EAAAA,EAAAA,KAAA,OACEJ,UAAW,gEAAgEwL,IAC3ElK,QAAS+J,EAAuBF,OAAUlO,EAC1C,cAAa,GAAGgD,gBAIlBG,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,qCAAoCG,UACjDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAW,yBAAyBuL,EAAW,SAAW,yCAAyCpL,UACtGD,EAAAA,EAAAA,MAAA,OACEuC,IAAKgJ,EACLzL,UAAW,GAxBD,CAClByK,GAAI,WACJnF,GAAI,WACJK,GAAI,WACJC,GAAI,YACJ8E,GAAI,YACJsC,KAAM,mBAkB4B/H,2GAA8GjF,IACxIsB,QAAUuK,GAAMA,EAAEoB,kBAClB,cAAahN,EAAOE,SAAA,EAGpBD,EAAAA,EAAAA,MAAA,OAAKF,UAAW,wEAAwEkB,IAAkBf,SAAA,CACtF,kBAAV9B,GACN+B,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,sCAAqCG,SAAE9B,IAErDA,EAEDiN,IACClL,EAAAA,EAAAA,KAAA,UACEhC,KAAK,SACL4B,UAAU,wGACVsB,QAAS6J,EACT,aAAW,cACX,cAAa,GAAGlL,iBAAsBE,UAEtCC,EAAAA,EAAAA,KAAC8M,EAAAA,EAAS,CAAClN,UAAU,kBAM3BI,EAAAA,EAAAA,KAAA,OAAKJ,UAAW,aAAaiB,IAAgBd,SAC1CA,IAIFkB,IACCjB,EAAAA,EAAAA,KAAA,OAAKJ,UAAW,4EAA4EmB,IAAkBhB,SAC3GkB,cAUf,OAAO8L,EAAAA,EAAAA,cAAaL,EAAcf,SAASE,KAAK,EAGlD,GAAenL,EAAAA,EAAAA,MAAKmK,E,6IClLpB,SAASmC,EAAoBxN,EAI1BuG,GAAQ,IAJmB,MAC5B9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,mSAEP,CACA,MACA,EADiCX,EAAAA,WAAiB+G,GCvBlD,SAASC,EAAazN,EAInBuG,GAAQ,IAJY,MACrB9H,EAAK,QACL+H,KACG5D,GACJ5C,EACC,OAAoByG,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbnE,IAAK0D,EACL,kBAAmBC,GAClB5D,GAAQnE,EAAqBgI,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACH/H,GAAS,KAAmBgI,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4KAEP,CACA,MACA,EADiCX,EAAAA,WAAiBgH,G,+DCpBlD,MAAMC,EAA0BlM,IAC9B,IAAKiF,EAAAA,eAAqBjF,GAAO,MAAO,2BAGxC,MAAMpB,EAAYoB,EAAKoB,MAAMxC,WAAa,GAM1C,OAHA+D,QAAQe,IAAI,kBAAmB9E,GAG3BA,EAAUuN,SAAS,gBAAwB,+BAC3CvN,EAAUuN,SAAS,aAAqB,4BACxCvN,EAAUuN,SAAS,cAAsB,6BACzCvN,EAAUuN,SAAS,eAAuB,8BAC1CvN,EAAUuN,SAAS,YAAoB,2BACvCvN,EAAUuN,SAAS,eAAuB,8BAC1CvN,EAAUuN,SAAS,eAAuB,8BAC1CvN,EAAUuN,SAAS,aAAqB,4BACxCvN,EAAUuN,SAAS,aAAqB,4BAGrC,0BAA0B,EA6EnC,EA7D0C3N,IAQnC,IARoC,MACzCvB,EAAK,MACLmP,EAAK,KACLpM,EAAI,OACJqM,EAAM,UACNC,GAAY,EAAK,QACjBpM,EAAO,UACPC,GAAY,GACb3B,EACC,OACEQ,EAAAA,EAAAA,KAACW,EAAAA,EAAI,CACHf,UAAU,uDACLsB,GAAW,CAAEA,WAClBC,UAAWA,EAAUpB,UAErBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,oCAAmCG,SAAE9B,IAEjDqP,GACCtN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,qDAEfI,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,4CAA2CG,SAAEqN,IAG3DC,IAAWC,IACVxN,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBG,SAAA,EACrCD,EAAAA,EAAAA,MAAA,QACEF,UAAW,wBACTyN,EAAOE,WAAa,iBAAmB,gBACtCxN,SAAA,CAEFsN,EAAOE,WAAa,SAAM,SAAI,IAAEF,EAAOD,MAAM,QAEhDpN,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,6BAA4BG,SAAC,0BAKnDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAW,oBAAoBsN,EAAuBlM,KAAQjB,SAEhEkG,EAAAA,eAAqBjF,GACpB,MACE,MAAMwM,EAAcxM,EAEdyM,GADoBD,EAAYpL,MAAMxC,WAAa,IACpB8N,MAAM,mBACrCC,EAAaF,EAAaA,EAAW,GAAK,eAEhD,OAAOxH,EAAAA,aAAmBuH,EAAa,CACrC5N,UAAW,WAAW+N,KAEzB,EATD,GAWA3M,QAID,E,wBC7EX,MAAM4M,EAAwCpO,IAIvC,IAJwC,KAC7CqO,EAAI,UACJP,GAAY,EAAK,eACjBQ,GACDtO,EACC,MAAOuO,EAAcC,IAAmB3Q,EAAAA,EAAAA,UAA4C,QAC9E4Q,GAAW3C,EAAAA,EAAAA,QAAuB,OAGxCC,EAAAA,EAAAA,YAAU,IACD,MACLtC,EAAAA,EAAAA,IAAagF,EAASjC,QAAQ,GAE/B,IAGH,MAAMkC,GAAqBxQ,EAAAA,EAAAA,cAAayQ,IACtCH,EAAgBG,GACZL,GACFA,EAAeK,EACjB,GACC,CAACL,IAGEM,GAAYC,EAAAA,EAAAA,UAAQ,MACxBlG,OAAQ0F,EAAKzN,KAAIC,GAAQA,EAAK6C,OAC9BoL,SAAU,CACR,CACE7N,MAAO,QACPoN,KAAMA,EAAKzN,KAAIC,GAAQA,EAAKwD,SAC5B0K,YAAa,UACb1I,gBAAiB,0BACjB2I,QAAS,GACTnI,MAAM,EACNoI,qBAAsB,UACtBC,iBAAkB,OAClBC,iBAAkB,EAClBC,YAAa,EACbC,iBAAkB,OAGpB,CAAChB,IAGCnR,GAAU2R,EAAAA,EAAAA,UAAQ,SAAAS,EAAAC,EAAA,MAAO,IAC1BpH,EAAAA,GACHG,QAAS,IACJH,EAAAA,GAAwBG,QAC3BM,QAAS,KAC4B,QAA/B0G,EAAAnH,EAAAA,GAAwBG,eAAO,IAAAgH,OAAA,EAA/BA,EAAiC1G,UAAW,CAAC,EACjD4G,UAAW,CACTvO,MAAO,SAAS7C,GACd,MAAO,IAAIA,EAAQqR,OAAOpG,EAAEqG,kBAC9B,KAINzG,OAAQ,IACHd,EAAAA,GAAwBc,OAC3BI,EAAG,KACiC,QAA9BkG,EAAApH,EAAAA,GAAwBc,cAAM,IAAAsG,OAAA,EAA9BA,EAAgClG,IAAK,CAAC,EAC1CD,MAAO,CACLuG,SAAU,SAAS/B,GACjB,MAAO,IAAMA,EAAM8B,gBACrB,KAIP,GAAG,IAEJ,OACEpP,EAAAA,EAAAA,MAACa,EAAAA,EAAI,CAACf,UAAU,SAAQG,SAAA,EACtBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6EAA4EG,SAAA,EACzFC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,oCAAmCG,SAAC,oBAClDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BG,SAAA,EAC1CC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,QAAjBiJ,EAAyB,UAAY,UAC9C7M,QAASA,IAAMgN,EAAmB,OAAOnO,SAC1C,SAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,SAAjBiJ,EAA0B,UAAY,UAC/C7M,QAASA,IAAMgN,EAAmB,QAAQnO,SAC3C,UAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,UAAjBiJ,EAA2B,UAAY,UAChD7M,QAASA,IAAMgN,EAAmB,SAASnO,SAC5C,WAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,SAAjBiJ,EAA0B,UAAY,UAC/C7M,QAASA,IAAMgN,EAAmB,QAAQnO,SAC3C,eAMJuN,GACCtN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wCAAuCG,UACpDC,EAAAA,EAAAA,KAACoP,EAAAA,EAAc,CAACvK,KAAK,UAGvB7E,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,OAAMG,UACnBC,EAAAA,EAAAA,KAACqP,EAAAA,GAAI,CACHxB,KAAMO,EACN1R,QAASA,EACT2F,IAAMA,IACAA,IACF4L,EAASjC,QAAU3J,EACrB,QAKH,EAIX,GAAe3B,EAAAA,EAAAA,MAAKkN,GCzHd0B,EAAkD9P,IAMjD,IANkD,KACvDqO,EAAI,UACJP,GAAY,EAAK,eACjBQ,EAAc,SACdyB,GAAW,EAAI,MACftR,EAAQ,eACTuB,EACC,MAAOuO,EAAcC,IAAmB3Q,EAAAA,EAAAA,UAA4C,QAC9E4Q,GAAW3C,EAAAA,EAAAA,QAAuB,OAGxCC,EAAAA,EAAAA,YAAU,IACD,MACLtC,EAAAA,EAAAA,IAAagF,EAASjC,QAAQ,GAE/B,IAGH,MAAMkC,GAAqBxQ,EAAAA,EAAAA,cAAayQ,IACtCH,EAAgBG,GACZL,GACFA,EAAeK,EACjB,GACC,CAACL,IAGEM,GAAYC,EAAAA,EAAAA,UAAQ,MACxBlG,OAAQ0F,EAAKzN,KAAIC,GAAQA,EAAK6C,OAC9BoL,SAAU,CACR,CACE7N,MAAO,QACPoN,KAAMA,EAAKzN,KAAIC,GAAQA,EAAKmP,QAC5BjB,YAAa,UACb1I,gBAAiB,0BACjB2I,QAAS,GACTnI,MAAM,EACNoI,qBAAsB,UACtBC,iBAAkB,OAClBC,iBAAkB,EAClBC,YAAa,EACbC,iBAAkB,OAGpB,CAAChB,IAGCnR,GAAU2R,EAAAA,EAAAA,UAAQ,SAAAS,EAAA,MAAO,IAC1BnH,EAAAA,GACHG,QAAS,IACJH,EAAAA,GAAwBG,QAC3BM,QAAS,KAC4B,QAA/B0G,EAAAnH,EAAAA,GAAwBG,eAAO,IAAAgH,OAAA,EAA/BA,EAAiC1G,UAAW,CAAC,EACjD4G,UAAW,CACTvO,MAAO,SAAS7C,GACd,MAAO,GAAGA,EAAQqR,OAAOpG,EAAEqG,wBAC7B,KAIP,GAAG,IAEEO,GACJzP,EAAAA,EAAAA,KAAAwK,EAAAA,SAAA,CAAAzK,SACGuN,GACCtN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wCAAuCG,UACpDC,EAAAA,EAAAA,KAACoP,EAAAA,EAAc,CAACvK,KAAK,UAGvB7E,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,OAAMG,UACnBC,EAAAA,EAAAA,KAACqP,EAAAA,GAAI,CACHxB,KAAMO,EACN1R,QAASA,EACT2F,IAAMA,IACAA,IACF4L,EAASjC,QAAU3J,EACrB,QAQZ,OAAKkN,GAKHzP,EAAAA,EAAAA,MAACa,EAAAA,EAAI,CAACf,UAAU,SAAQG,SAAA,EACtBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6EAA4EG,SAAA,EACzFC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,oCAAmCG,SAAE9B,KACnD6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BG,SAAA,EAC1CC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,QAAjBiJ,EAAyB,UAAY,UAC9C7M,QAASA,IAAMgN,EAAmB,OAAOnO,SAC1C,SAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,SAAjBiJ,EAA0B,UAAY,UAC/C7M,QAASA,IAAMgN,EAAmB,QAAQnO,SAC3C,UAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,UAAjBiJ,EAA2B,UAAY,UAChD7M,QAASA,IAAMgN,EAAmB,SAASnO,SAC5C,WAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLvE,KAAK,KACLC,QAA0B,SAAjBiJ,EAA0B,UAAY,UAC/C7M,QAASA,IAAMgN,EAAmB,QAAQnO,SAC3C,eAKJ0P,KAtCIA,CAuCA,EAIX,GAAe/O,EAAAA,EAAAA,MAAK4O,GCrIdI,EAAsElQ,IAMrE,IAADmQ,EAAA,IANuE,KAC3E9B,EAAI,UACJP,GAAY,EAAK,MACjBrP,EAAQ,wBAAuB,SAC/BsR,GAAW,EAAI,UACf3P,EAAY,IACbJ,EACC,MAAMyO,GAAW3C,EAAAA,EAAAA,QAAuB,OAGxCC,EAAAA,EAAAA,YAAU,IACD,KACL,KACEtC,EAAAA,EAAAA,IAAagF,EAASjC,QACxB,CAAE,MAAOzO,GAEPoG,QAAQiM,KAAK,6BAA8BrS,EAC7C,IAED,IAGH,MAAM6Q,GAAYC,EAAAA,EAAAA,UAAQ,MACxBlG,OAAQ0F,EAAK1F,QAAU,GACvBmG,SAAUT,EAAKS,UAAY,MACzB,CAACT,EAAK1F,OAAQ0F,EAAKS,WAGjBuB,GAAexB,EAAAA,EAAAA,UAAQ,SAAAyB,EAAA,MAAO,IAC/B/G,EAAAA,GACHjB,QAAS,IACJiB,EAAAA,GAAuBjB,QAC1B7J,MAAO,IAC4B,QAAjC6R,EAAG/G,EAAAA,GAAuBjB,eAAO,IAAAgI,OAAA,EAA9BA,EAAgC7R,MACnC8J,SAAS,IAGd,GAAG,IAGEgI,EAAelC,EAAK1F,QAAU0F,EAAK1F,OAAOvL,OAAS,GACrCiR,EAAKS,UAAYT,EAAKS,SAAS1R,OAAS,IACxB,QADyB+S,EACzC9B,EAAKS,SAAS,UAAE,IAAAqB,OAAA,EAAhBA,EAAkB9B,OAAQA,EAAKS,SAAS,GAAGT,KAAKjR,OAAS,EAEvE6S,GACJzP,EAAAA,EAAAA,KAAAwK,EAAAA,SAAA,CAAAzK,SACGuN,GACCtN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wCAAuCG,UACpDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,oEAEdmQ,GAOHjQ,EAAAA,EAAAA,MAAA0K,EAAAA,SAAA,CAAAzK,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,OAAMG,UACnBC,EAAAA,EAAAA,KAACgQ,EAAAA,GAAG,CACF3N,IAAMA,IACAA,IACF4L,EAASjC,QAAU3J,EACrB,EAEFwL,KAAMO,EACN1R,QAASmT,OAKb7P,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,iBAAgBG,SAC5B8N,EAAK1F,OAAO/H,KAAI,CAACK,EAAOH,KAAK,IAAA2P,EAAAC,EAAAC,EAAAC,EAAA,OAC5BtQ,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCG,SAAA,EAC5DD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBG,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OACEJ,UAAU,4BACVqE,MAAO,CACL4B,iBAAiC,QAAhBoK,EAAApC,EAAKS,SAAS,UAAE,IAAA2B,GAAiB,QAAjBC,EAAhBD,EAAkBpK,uBAAe,IAAAqK,OAAjB,EAAhBA,EAAoC5P,KAAoB,WAG7EN,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,wBAAuBG,SAAEU,QAE3CX,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sBAAqBG,SAAA,EAClB,QAAhBoQ,EAAAtC,EAAKS,SAAS,UAAE,IAAA6B,GAAM,QAANC,EAAhBD,EAAkBtC,YAAI,IAAAuC,OAAN,EAAhBA,EAAyB9P,KAAU,EAAE,SAXhCA,EAaJ,UAnCZN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wCAAuCG,UACpDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,4BAA2BG,SAAC,0BA0CnD,OAAKwP,GAKHvP,EAAAA,EAAAA,KAACW,EAAAA,EAAI,CACH1C,OAAO+B,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,mCAAkCG,SAAE9B,IACzD2B,UAAWA,EAAUG,SAEpB0P,KARIzP,EAAAA,EAAAA,KAAA,OAAKJ,UAAWA,EAAUG,SAAE0P,GAS5B,EAIX,EAAexJ,EAAAA,KAAWyJ,G,uGCvG1B,MA2GA,EA3G4DlQ,IAIrD,IAJsD,MAC3D6Q,EAAK,OACLvF,EAAM,QACNC,GACDvL,EACC,IAAK6Q,EAAO,OAAO,KAkCnB,OACErQ,EAAAA,EAAAA,KAAC6K,EAAAA,EAAK,CACJC,OAAQA,EACRC,QAASA,EACT9M,MAAM,gBACN4G,KAAK,KACL5D,QACEjB,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLtE,QAAQ,UACR5D,QAAS6J,EAAQhL,SAClB,UAGFA,UAEDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oCAAmCG,SAAA,CAAC,UAAQsQ,EAAM5J,OAChEzG,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,OAAMG,UACnBD,EAAAA,EAAAA,MAAA,QAAMF,UAAW,2EAnCA0Q,KAC3B,OAAOA,GACL,IAAK,UACH,MAAO,gCACT,IAAK,aACH,MAAO,4BACT,IAAK,YACH,MAAO,8BACT,IAAK,YACH,MAAO,0BACT,QACE,MAAO,GACX,EAuBsGC,CAAoBF,EAAMC,UAAUvQ,SAAA,CAnDrHuQ,KACrB,OAAOA,GACL,IAAK,UACH,OAAOtQ,EAAAA,EAAAA,KAACwQ,EAAAA,EAAS,CAAC5Q,UAAU,4BAC9B,IAAK,aACH,OAAOI,EAAAA,EAAAA,KAACyQ,EAAAA,EAAe,CAAC7Q,UAAU,0BACpC,IAAK,YACH,OAAOI,EAAAA,EAAAA,KAAC0Q,EAAAA,EAAS,CAAC9Q,UAAU,2BAC9B,IAAK,YACH,OAAOI,EAAAA,EAAAA,KAAC2Q,EAAAA,EAAW,CAAC/Q,UAAU,yBAChC,QACE,OAAO,KACX,EAwCagR,CAAcP,EAAMC,SACrBtQ,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,OAAMG,SAAEsQ,EAAMC,OAASD,EAAMC,OAAOO,OAAO,GAAGC,cAAgBT,EAAMC,OAAOS,MAAM,GAAK,qBAI5GjR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYG,SAAA,EACzBC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wBAAuBG,SAAC,kBACvCD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCG,SAAA,CAAC,IAAEsQ,EAAMxM,OAAOqL,2BAInEpP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,KAAC8F,EAAAA,EAAQ,CAAClG,UAAU,uCACpBE,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,oCAAmCG,SAAC,cACnDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wBAAuBG,SAAEsQ,EAAMW,kBAIlDlR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,KAAC4K,EAAAA,EAAY,CAAChL,UAAU,uCACxBE,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,oCAAmCG,SAAC,gBACnDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wBAAuBG,SAAEsQ,EAAMnN,cAIlDpD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,KAACiR,EAAAA,EAAkB,CAACrR,UAAU,uCAC9BE,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,oCAAmCG,SAAC,aACnDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBG,SAAA,CAAC,IAAEsQ,EAAMxM,OAAOqL,8BAK5DlP,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,qCAAoCG,UACjDC,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,wBAAuBG,SAAC,qJAKnC,EC6BZ,EA1IkDP,IAI3C,IAJ4C,OACjD0R,EAAM,UACN5D,GAAY,EAAK,YACjB6D,GACD3R,EACC,MAAM4R,GAAWC,EAAAA,EAAAA,OACVC,EAAeC,IAAoBlU,EAAAA,EAAAA,UAA6B,OAChEmU,EAAaC,IAAkBpU,EAAAA,EAAAA,WAAS,GAgBzCqU,EAAkBpB,IACtB,OAAQA,GACN,IAAK,UACH,MAAO,gCACT,IAAK,aACH,MAAO,4BACT,IAAK,YACH,MAAO,8BACT,IAAK,YACH,MAAO,0BACT,QACE,MAAO,4BACX,EAGF,OACExQ,EAAAA,EAAAA,MAACa,EAAAA,EAAI,CAACf,UAAU,SAAQG,SAAA,EACtBD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCG,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,oCAAmCG,SAAC,mBAClDC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAIyR,EAAAA,EAAOC,OACXhS,UAAU,2DAA0DG,SACrE,gBAKFuN,GACCtN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,YAAWG,SACvB,IAAI8R,MAAM,IAAIzR,KAAI,CAAC0R,EAAGxR,KACrBR,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,kDAAiDG,SAAA,EAC1ED,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWG,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,kCACfI,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,qCAEjBI,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,kCACfI,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,kCACfI,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,uCAPPU,QAYdN,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,kBAAiBG,UAC9BD,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sCAAqCG,SAAA,EACpDC,EAAAA,EAAAA,KAAA,SAAOJ,UAAU,aAAYG,UAC3BD,EAAAA,EAAAA,MAAA,MAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,iFAAgFG,SAAC,WAG3GC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,iFAAgFG,SAAC,cAG3GC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,iFAAgFG,SAAC,YAG3GC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,iFAAgFG,SAAC,YAG3GC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,iFAAgFG,SAAC,UAG3GC,EAAAA,EAAAA,KAAA,MAAI+R,MAAM,MAAMnS,UAAU,qBAAoBG,UAC5CC,EAAAA,EAAAA,KAAA,QAAMJ,UAAU,UAASG,SAAC,oBAIhCC,EAAAA,EAAAA,KAAA,SAAOJ,UAAU,oCAAmCG,SACjDmR,EAAO9Q,KAAKiQ,IACXvQ,EAAAA,EAAAA,MAAA,MAEEF,UAAU,kCACVsB,QAASA,KAAM8Q,OA3ECC,EA2EsB5B,EAAM5J,QA1E1D2K,EAASO,EAAAA,EAAOO,qBAAqBD,IADPA,KA2EgC,EAAAlS,SAAA,EAEhDD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gEAA+DG,SAAA,CAAC,IAC1EsQ,EAAM5J,OAEVzG,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,oDAAmDG,SAC9DsQ,EAAMW,YAETlR,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oDAAmDG,SAAA,CAAC,IAC9DsQ,EAAMxM,OAAOqL,qBAEjBlP,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,8BAA6BG,UACzCC,EAAAA,EAAAA,KAAA,QAAMJ,UAAW,iEAAiE8R,EAAerB,EAAMC,UAAUvQ,SAC9GsQ,EAAMC,OAAOO,OAAO,GAAGC,cAAgBT,EAAMC,OAAOS,MAAM,QAG/D/Q,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,oDAAmDG,SAC9DsQ,EAAMnN,QAETlD,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,6DAA4DG,UACxEC,EAAAA,EAAAA,KAAA,UACEkB,QAAUuK,GA1GN0G,EAAC9B,EAAoB5E,KAE3CA,EAAEoB,kBACF0E,EAAiBlB,GACjBoB,GAAe,GACXN,GACFA,EAAYd,EAAM5J,GACpB,EAmGkC0L,CAAgB9B,EAAO5E,GACvC7L,UAAU,mCAAkCG,UAE5CC,EAAAA,EAAAA,KAACoS,EAAAA,EAAO,CAACxS,UAAU,kBA1BlByQ,EAAM5J,cAqCvBzG,EAAAA,EAAAA,KAACqS,EAAiB,CAChBhC,MAAOiB,EACPxG,OAAQ0G,EACRzG,QAASA,IAAM0G,GAAe,OAE3B,E,cCpIX,MAqFaa,EAAe,CAI1BC,kBAAmBrT,UACjB,IACE,MAAMsT,QAAiBC,EAAAA,EAAUC,IAA4B,oBAE7D,GAAIF,EAAS3E,KACX,MA7FC,CACL8E,QAAS,CACPC,YAH2BC,EA8FML,EAAS3E,MA3FlB+E,WACxBE,eAAgBD,EAAYC,eAC5BC,YAAaF,EAAYE,YACzBC,aAAcH,EAAYG,aAC1BC,qBAAsBJ,EAAYI,qBAClCC,YAAaL,EAAYK,aAE3BC,cAAeN,EAAYM,cAE3BC,YAAa,CACXjL,OAAQ,GACRmG,SAAU,CAAC,CACT7N,MAAO,UACPoN,KAAM,GACNU,YAAa,UACb1I,gBAAiB,6BAGrBwN,WAAY,CACVlL,OAAQ,GACRmG,SAAU,CAAC,CACT7N,MAAO,QACPoN,KAAM,GACNU,YAAa,UACb1I,gBAAiB,6BAGrByN,qBAAsB,CACpBnL,OAAQ,GACRmG,SAAU,CAAC,CACTT,KAAM,GACNhI,gBAAiB,GACjB0N,YAAa,KAGjBC,aAAc,GACdC,aAAc,IAyDV,MAAM,IAAItV,MAAM,wCAEpB,CAAE,MAAOZ,GACP,MAAMO,EAAAA,EAAAA,IAAeP,EACvB,CApG6BsV,KAoG7B,EAMFa,aAAcxU,UACZ,IACE,MAAMsT,QAAiBC,EAAAA,EAAUC,IAAuB,mBAAoB,CAAEiB,OAAQ,CAAExF,YACxF,GAAIqE,EAAS3E,KACX,OAA0B2E,EAAS3E,KA/DtBA,KAAKzN,KAAIC,IAAI,CAC9B6C,KAAM7C,EAAK6C,KACXW,OAAQxD,EAAKuT,MACb1C,OAAQ7Q,EAAK6Q,WA8DT,MAAM,IAAI/S,MAAM,yBAEpB,CAAE,MAAOZ,GACP,MAAMO,EAAAA,EAAAA,IAAeP,EACvB,GAMFsW,cAAe3U,UACb,IACE,MAAMsT,QAAiBC,EAAAA,EAAUC,IAAwB,yBAA0B,CAAEiB,OAAQ,CAAExF,YAC/F,GAAIqE,EAAS3E,KACX,OAA2B2E,EAAS3E,KApEvBA,KAAKzN,KAAIC,IAAI,CAC9B6C,KAAM7C,EAAK6C,KACXsM,MAAOnP,EAAKuS,WACZkB,SAAUzT,EAAKyT,SACflB,WAAYvS,EAAKuS,eAkEb,MAAM,IAAIzU,MAAM,+BAEpB,CAAE,MAAOZ,GACP,MAAMO,EAAAA,EAAAA,IAAeP,EACvB,GAMFwW,wBAAyB7U,UACvB,IACE,MAAMsT,QAAiBC,EAAAA,EAAUC,IAAoC,oCACrE,GAAIF,EAAS3E,KACX,MAzE+BgF,KACrC,MAAMmB,EAAS,CACb,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAG9C,MAAO,CACL7L,OAAQ0K,EAAYzS,KAAIC,GAAQA,EAAK4T,WACrC3F,SAAU,CAAC,CACTT,KAAMgF,EAAYzS,KAAIC,GAAQA,EAAK6T,QACnCrO,gBAAiBgN,EAAYzS,KAAI,CAAC0R,EAAGxR,IAAU0T,EAAO1T,EAAQ0T,EAAOpX,SAAW,YAChF2W,YAAa,IAEhB,EA4DYY,CAA8B3B,EAAS3E,MAE9C,MAAM,IAAI1P,MAAM,yCAEpB,CAAE,MAAOZ,GACP,MAAMO,EAAAA,EAAAA,IAAeP,EACvB,IAIJ,ICvGA,EAxDgEiC,IAAoB,IAAnB,UAAEI,GAAWJ,EAC5E,MAAO4U,EAAWC,IAAgBhX,EAAAA,EAAAA,UAAsB,KACjDiQ,EAAWgH,IAAgBjX,EAAAA,EAAAA,WAAS,IACpCE,EAAOgX,IAAYlX,EAAAA,EAAAA,UAAuB,MAE3CmX,GAAiB9W,EAAAA,EAAAA,cAAYwB,UACjCoV,GAAa,GACbC,EAAS,MACT,IACE,MAAM1G,QAAayE,EAAaoB,aAAavF,GAC7CkG,EAAaxG,EACf,CAAE,MAAO4G,GACPF,EAASE,GACT9Q,QAAQpG,MAAM,8BAA+BkX,EAC/C,CAAC,QACCH,GAAa,EACf,IACC,KAGH/I,EAAAA,EAAAA,YAAU,KACRiJ,EAAe,QAAQ,GACtB,CAACA,IAEJ,MAAMtG,GAAqBxQ,EAAAA,EAAAA,cAAayQ,IACtCqG,EAAerG,EAAO,GACrB,CAACqG,IAEJ,OAAIjX,GAEAuC,EAAAA,EAAAA,MAAA,OAAKF,UAAW,kCAAkCA,GAAa,KAAKG,SAAA,EAClEC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,2CAA0CG,SAAC,gBACzDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BG,SAAA,EACvCC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,+BACHC,EAAAA,EAAAA,KAAA,UACEkB,QAASA,IAAMsT,EAAe,SAC9B5U,UAAU,iDAAgDG,SAC3D,qBASPC,EAAAA,EAAAA,KAAA,OAAKJ,UAAWA,EAAUG,UACxBC,EAAAA,EAAAA,KAAC4N,EAAU,CACTC,KAAMuG,EACN9G,UAAWA,EACXQ,eAAgBI,KAEd,ECMV,EA1D0E1O,IAAoB,IAAnB,UAAEI,GAAWJ,EACtF,MAAOkV,EAAgBC,IAAqBtX,EAAAA,EAAAA,UAAuB,KAC5DiQ,EAAWgH,IAAgBjX,EAAAA,EAAAA,WAAS,IACpCE,EAAOgX,IAAYlX,EAAAA,EAAAA,UAAuB,MAE3CuX,GAAsBlX,EAAAA,EAAAA,cAAYwB,UACtCoV,GAAa,GACbC,EAAS,MACT,IACE,MAAM1G,QAAayE,EAAauB,cAAc1F,GAC9CwG,EAAkB9G,EACpB,CAAE,MAAO4G,GACPF,EAASE,GACT9Q,QAAQpG,MAAM,oCAAqCkX,EACrD,CAAC,QACCH,GAAa,EACf,IACC,KAGH/I,EAAAA,EAAAA,YAAU,KACRqJ,EAAoB,QAAQ,GAC3B,CAACA,IAEJ,MAAM1G,GAAqBxQ,EAAAA,EAAAA,cAAayQ,IAGtCyG,EAD+B,QAAXzG,EAAmB,OAASA,EAChB,GAC/B,CAACyG,IAEJ,OAAIrX,GAEAuC,EAAAA,EAAAA,MAAA,OAAKF,UAAW,kCAAkCA,GAAa,KAAKG,SAAA,EAClEC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,2CAA0CG,SAAC,iBACzDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BG,SAAA,EACvCC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,qCACHC,EAAAA,EAAAA,KAAA,UACEkB,QAASA,IAAM0T,EAAoB,SACnChV,UAAU,iDAAgDG,SAC3D,qBASPC,EAAAA,EAAAA,KAAA,OAAKJ,UAAWA,EAAUG,UACxBC,EAAAA,EAAAA,KAACsP,EAAe,CACdzB,KAAM6G,EACNpH,UAAWA,EACXQ,eAAgBI,KAEd,ECSV,EA9D8F1O,IAGvF,IAHwF,UAC7FI,EAAS,MACT3B,EAAQ,yBACTuB,EACC,MAAOqV,EAAcC,IAAmBzX,EAAAA,EAAAA,UAAmC,CACzE8K,OAAQ,GACRmG,SAAU,CAAC,CACTT,KAAM,GACNhI,gBAAiB,GACjB0N,YAAa,OAGVjG,EAAWgH,IAAgBjX,EAAAA,EAAAA,WAAS,IACpCE,EAAOgX,IAAYlX,EAAAA,EAAAA,UAAuB,MAE3C0X,GAAoBrX,EAAAA,EAAAA,cAAYwB,UACpCoV,GAAa,GACbC,EAAS,MACT,IACE,MAAM1G,QAAayE,EAAayB,0BAChCe,EAAgBjH,EAClB,CAAE,MAAO4G,GACPF,EAASE,GACT9Q,QAAQpG,MAAM,8CAA+CkX,EAC/D,CAAC,QACCH,GAAa,EACf,IACC,IAOH,OAJA/I,EAAAA,EAAAA,YAAU,KACRwJ,GAAmB,GAClB,CAACA,IAEAxX,GAEAuC,EAAAA,EAAAA,MAAA,OAAKF,UAAW,kCAAkCA,GAAa,KAAKG,SAAA,EAClEC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,2CAA0CG,SAAE9B,KAC1D6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BG,SAAA,EACvCC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,+CACHC,EAAAA,EAAAA,KAAA,UACEkB,QAAS6T,EACTnV,UAAU,iDAAgDG,SAC3D,qBASPC,EAAAA,EAAAA,KAAA,OAAKJ,UAAWA,EAAUG,UACxBC,EAAAA,EAAAA,KAAC0P,EAAyB,CACxB7B,KAAMgH,EACNvH,UAAWA,EACXrP,MAAOA,KAEL,E,cC/DH,MAyJP,EAzJ4B+W,KAC1B,MAAOC,EAAOC,IAAY7X,EAAAA,EAAAA,UAAgC,OACnDiQ,EAAWgH,IAAgBjX,EAAAA,EAAAA,WAAS,IACpCE,EAAOgX,IAAYlX,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEJ,IAAqBC,EAAAA,EAAAA,KAGvBiY,GAAsB7J,EAAAA,EAAAA,QAAOrO,GAC7BmY,GAAoB9J,EAAAA,EAAAA,SAAO,GAC3B+J,GAAW/J,EAAAA,EAAAA,QAA2D,MACtEgK,EAAY,KAGlB/J,EAAAA,EAAAA,YAAU,KACR4J,EAAoBnJ,QAAU/O,CAAgB,IAIhD,MAAMsY,GAAe7X,EAAAA,EAAAA,cAAY,MAC1B2X,EAASrJ,SACP7I,KAAKqS,MAAQH,EAASrJ,QAAQyJ,UAAYH,GAChD,CAACA,IAGEI,GAAahY,EAAAA,EAAAA,cAAYwB,iBAE7B,KAFgDvC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,KAE3B4Y,KAAkBF,EAASrJ,QAE9C,OADAkJ,EAASG,EAASrJ,QAAQ6B,MACnBwH,EAASrJ,QAAQ6B,KAG1ByG,GAAa,GACbC,EAAS,MACT,IACE,MAAM1G,QAAayE,EAAaC,oBAOhC,OANA2C,EAASrH,GAETwH,EAASrJ,QAAU,CACjB6B,OACA4H,UAAWtS,KAAKqS,OAEX3H,CACT,CAAE,MAAO4G,GACP,MAAMlX,EAAQkX,EACdF,EAAShX,GAGT,MAAMoY,EAAepY,EAAMW,SAAW,uCAQtC,MAPAyF,QAAQpG,MAAM,yBAA0BA,GAExC4X,EAAoBnJ,QAAQ,CAC1BhO,KAAM,QACNC,MAAO,kBACPC,QAASyX,IAELpY,CACR,CAAC,QACC+W,GAAa,EACf,CACF,GAAG,CAACiB,IAGEf,GAAiB9W,EAAAA,EAAAA,cAAYwB,UACjCoV,GAAa,GACbC,EAAS,MACT,IAEE,aADmBjC,EAAaoB,aAAavF,EAE/C,CAAE,MAAOsG,GAOP,MANAF,EAASE,GACTU,EAAoBnJ,QAAQ,CAC1BhO,KAAM,QACNC,MAAO,QACPC,QAAS,kCAAkCiQ,MAEvCsG,CACR,CAAC,QACCH,GAAa,EACf,IACC,IAGGsB,GAAkBlY,EAAAA,EAAAA,cAAYwB,UAClCoV,GAAa,GACbC,EAAS,MACT,IAEE,aADmBjC,EAAauB,cAAc1F,EAEhD,CAAE,MAAOsG,GAOP,MANAF,EAASE,GACTU,EAAoBnJ,QAAQ,CAC1BhO,KAAM,QACNC,MAAO,QACPC,QAAS,wCAAwCiQ,MAE7CsG,CACR,CAAC,QACCH,GAAa,EACf,IACC,IAGGuB,GAA4BnY,EAAAA,EAAAA,cAAYwB,UAC5CoV,GAAa,GACbC,EAAS,MACT,IAEE,aADmBjC,EAAayB,yBAElC,CAAE,MAAOU,GAOP,MANAF,EAASE,GACTU,EAAoBnJ,QAAQ,CAC1BhO,KAAM,QACNC,MAAO,QACPC,QAAS,+CAELuW,CACR,CAAC,QACCH,GAAa,EACf,IACC,IAuBH,OApBA/I,EAAAA,EAAAA,YAAU,KACH6J,EAAkBpJ,UACrBoJ,EAAkBpJ,SAAU,EAC5B0J,IAAaI,OAAMrB,IACjB9Q,QAAQpG,MAAM,2CAA4CkX,EAAI,IAGlE,GACC,CAACiB,KAGJnK,EAAAA,EAAAA,YAAU,IACD,KAGL+I,GAAa,GACbC,EAAS,KAAK,GAEf,IAEI,CACLU,QACA3H,YACA/P,QACAmY,aACAlB,iBACAoB,kBACAC,4BACD,ECgCH,GAAehU,EAAAA,EAAAA,KA9JiBkU,KAC9B,MAAOC,EAAcC,IAAmB5Y,EAAAA,EAAAA,WAAS,IAGzC4X,MAAOiB,EAAa,UAAE5I,EAAS,WAAEoI,GAAeV,KAGlD,mBACJnW,IACEsX,EAAAA,EAAAA,GAAgB,CAClBrZ,qBAAqB,EACrBC,iBAAiB,IAuCnB,OAAIuQ,IAAc4I,GAEdlW,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,wCAAuCG,UACpDC,EAAAA,EAAAA,KAACoP,EAAAA,EAAc,CAACvK,KAAK,KAAKC,QAAQ,aAMtChF,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEC,EAAAA,EAAAA,KAACT,EAAAA,EAAU,CACTtB,MAAM,YACNwB,YAAY,0CACZC,SACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBG,SAAA,EACnCC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLtE,QAAQ,UACR9D,MAAMhB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAiB,CAACjH,UAAU,YAAaG,SACjD,mBAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLpI,MAAMhB,EAAAA,EAAAA,KAACgN,EAAoB,CAACpN,UAAU,YAAaG,SACpD,qBAGDC,EAAAA,EAAAA,KAACoJ,EAAAA,EAAM,CACLtE,QAAQ,UACR9D,MAAMhB,EAAAA,EAAAA,KAACiN,EAAa,CAACrN,UAAU,YAC/BsB,QAhEUhC,UACpB+W,GAAgB,GAEhB,MAAMG,QAAeC,EAAAA,EAAAA,KACnBnX,gBAEQ,IAAIoX,SAAQ,CAACC,EAASC,KAC1BC,YAAW,KACLjS,KAAKkS,SAAW,GAClBF,EAAO,IAAIrY,MAAM,qCAEjBoY,GAAQ,EACV,GACC,KAAK,UAIJb,GAAW,IACV,IAET,CACEiB,QAAS,IACTC,QAAS,EACTC,cAAe,sBAIdT,EAAOlM,SACVrL,EAAmBuX,EAAO7Y,MAAO,qBAGnC0Y,GAAgB,EAAM,EAkCZ3U,QAAS0U,EAAajW,SACvB,kBAQPD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DG,SAAA,EACxEC,EAAAA,EAAAA,KAAC8W,EAAQ,CACP7Y,MAAM,cACNmP,MAAO8I,EAAcvD,QAAQC,WAAW1D,iBACxClO,MAAMhB,EAAAA,EAAAA,KAAC+W,EAAAA,EAAS,CAACnX,UAAU,yBAC3ByN,OAAQ,CACND,MAAO5I,KAAKwS,IAAId,EAAc/C,cAAc3D,OAC5CjC,WAAY2I,EAAc/C,cAAc3D,OAAS,MAKrDxP,EAAAA,EAAAA,KAAC8W,EAAQ,CACP7Y,MAAM,eACNmP,MAAO8I,EAAcvD,QAAQI,YAAY7D,iBACzClO,MAAMhB,EAAAA,EAAAA,KAACiX,EAAAA,EAAgB,CAACrX,UAAU,0BAClCyN,OAAQ,CACND,MAAO5I,KAAKwS,IAAId,EAAc/C,cAAcjC,QAC5C3D,WAAY2I,EAAc/C,cAAcjC,QAAU,MAItDlR,EAAAA,EAAAA,KAAC8W,EAAQ,CACP7Y,MAAM,eACNmP,MAAO8I,EAAcvD,QAAQO,YAAYhE,iBACzClO,MAAMhB,EAAAA,EAAAA,KAACwQ,EAAAA,EAAS,CAAC5Q,UAAU,2BAC3ByN,OAAQ,CAAED,MAAO,EAAGG,YAAY,MAGlCvN,EAAAA,EAAAA,KAAC8W,EAAQ,CACP7Y,MAAM,UACNmP,OAAOxJ,EAAAA,EAAAA,IAAesS,EAAcvD,QAAQK,cAC5ChS,MAAMhB,EAAAA,EAAAA,KAACiR,EAAAA,EAAkB,CAACrR,UAAU,2BACpCyN,OAAQ,CACND,MAAO5I,KAAKwS,IAAId,EAAc/C,cAAc+D,SAC5C3J,WAAY2I,EAAc/C,cAAc+D,SAAW,SAMzDpX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CG,SAAA,EACzDC,EAAAA,EAAAA,KAACmX,EAAmB,KACpBnX,EAAAA,EAAAA,KAACoX,EAAwB,QAG3BtX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCG,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,gBAAeG,UAC5BC,EAAAA,EAAAA,KAACqX,EAAY,CACXnG,OAAQgF,EAAc1C,aAAapT,KAAIiQ,IAAK,CAC1C5J,GAAI4J,EAAMiH,YACVtG,SAAUX,EAAMkH,aAChB1T,OAAQwM,EAAMxM,OACdyM,OAAQD,EAAMC,OACdpN,KAAMmN,EAAMnN,SAEdiO,YAAcc,GAAYtO,QAAQe,IAAI,iBAAkBuN,QAI5DjS,EAAAA,EAAAA,KAACwX,EAAkC,CACjCvZ,MAAM,+BAGN,GAKsC,CAC9C+D,SAAUxC,IAAA,IAAC,MAAEjC,EAAK,WAAEqE,GAAYpC,EAAA,OAC9BM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DG,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,OAAKJ,UAAU,6BAA4BG,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIJ,UAAU,6BAA4BG,SAAC,qBAC3CC,EAAAA,EAAAA,KAAA,KAAGJ,UAAU,0CAAyCG,SACnDxC,EAAMW,SAAW,mDAEpB8B,EAAAA,EAAAA,KAAA,UACEkB,QAASU,EACThC,UAAU,kFAAiFG,SAC5F,uBAGG,EAERnC,QAAS,iB", "sources": ["hooks/useErrorHandler.ts", "components/layout/PageHeader.tsx", "components/common/Card.tsx", "components/common/withErrorBoundary.tsx", "utils/formatters.ts", "components/common/LoadingSpinner.tsx", "../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "utils/chartConfig.ts", "components/common/Button.tsx", "../node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js", "features/dashboard/components/StatCard.tsx", "features/dashboard/components/SalesChart.tsx", "features/dashboard/components/UserGrowthChart.tsx", "features/dashboard/components/CategoryDistributionChart.tsx", "features/dashboard/components/OrderDetailsModal.tsx", "features/dashboard/components/RecentOrders.tsx", "features/dashboard/api/dashboardApi.ts", "features/dashboard/components/SalesChartContainer.tsx", "features/dashboard/components/UserGrowthChartContainer.tsx", "features/dashboard/components/CategoryDistributionChartContainer.tsx", "features/dashboard/hooks/useDashboard.ts", "pages/DashboardPage.tsx"], "sourcesContent": ["/**\r\n * Error <PERSON>ler Hook\r\n * \r\n * This hook provides React-specific error handling utilities and state management.\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { \r\n  handleApiError, \r\n  handleValidationError, \r\n  handleFormError,\r\n  logError,\r\n  reportError,\r\n  type ApiError,\r\n  type ValidationError \r\n} from '../utils/errorHandling';\r\nimport useNotification from './useNotification';\r\n\r\ninterface ErrorState {\r\n  hasError: boolean;\r\n  error: Error | ApiError | ValidationError | null;\r\n  errorType: 'api' | 'validation' | 'form' | 'general' | null;\r\n  context?: string;\r\n}\r\n\r\ninterface UseErrorHandlerOptions {\r\n  enableNotifications?: boolean;\r\n  enableReporting?: boolean;\r\n  onError?: (error: any, context?: string) => void;\r\n}\r\n\r\nexport const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {\r\n  const { enableNotifications = true, enableReporting = true, onError } = options;\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [errorState, setErrorState] = useState<ErrorState>({\r\n    hasError: false,\r\n    error: null,\r\n    errorType: null\r\n  });\r\n\r\n  // Clear error state\r\n  const clearError = useCallback(() => {\r\n    setErrorState({\r\n      hasError: false,\r\n      error: null,\r\n      errorType: null\r\n    });\r\n  }, []);\r\n\r\n  // Handle API errors\r\n  const handleApiErrorWithState = useCallback((error: any, context?: string) => {\r\n    const apiError = handleApiError(\r\n      error,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: apiError,\r\n      errorType: 'api',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return apiError;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle validation errors\r\n  const handleValidationErrorWithState = useCallback((\r\n    field: string,\r\n    message: string,\r\n    code?: string,\r\n    context?: string\r\n  ) => {\r\n    const validationError = handleValidationError(field, message, code);\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: validationError,\r\n      errorType: 'validation',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Validation Error',\r\n        message: validationError.message\r\n      });\r\n    }\r\n\r\n    if (onError) {\r\n      onError(validationError, context);\r\n    }\r\n\r\n    return validationError;\r\n  }, [enableNotifications, showNotification, onError]);\r\n\r\n  // Handle form errors\r\n  const handleFormErrorWithState = useCallback((\r\n    error: any,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ) => {\r\n    handleFormError(\r\n      error,\r\n      setFieldError,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error,\r\n      errorType: 'form',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle general errors\r\n  const handleGeneralError = useCallback((error: any, context?: string) => {\r\n    const errorObj = error instanceof Error ? error : new Error(String(error));\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: errorObj,\r\n      errorType: 'general',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorObj.message\r\n      });\r\n    }\r\n\r\n    if (enableReporting) {\r\n      reportError(errorObj, context);\r\n    }\r\n\r\n    logError(errorObj, context);\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return errorObj;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Async operation wrapper with error handling\r\n  const withErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleApiErrorWithState(error, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleApiErrorWithState]);\r\n\r\n  // Form submission wrapper with error handling\r\n  const withFormErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleFormErrorWithState(error, setFieldError, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleFormErrorWithState]);\r\n\r\n  return {\r\n    // Error state\r\n    ...errorState,\r\n    \r\n    // Error handlers\r\n    handleApiError: handleApiErrorWithState,\r\n    handleValidationError: handleValidationErrorWithState,\r\n    handleFormError: handleFormErrorWithState,\r\n    handleGeneralError,\r\n    clearError,\r\n    \r\n    // Wrapper functions\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    \r\n    // Utility functions\r\n    isApiError: (error: any): error is ApiError => \r\n      error && typeof error === 'object' && 'status' in error,\r\n    isValidationError: (error: any): error is ValidationError => \r\n      error && typeof error === 'object' && 'field' in error,\r\n  };\r\n};\r\n\r\nexport default useErrorHandler;\r\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "/**\r\n * Higher-Order Component for Error Boundary\r\n * \r\n * This HOC wraps components with an error boundary to catch and handle errors gracefully.\r\n */\r\n\r\nimport React, { ComponentType, forwardRef } from 'react';\r\nimport ErrorBoundary from './ErrorBoundary';\r\nimport { reportError } from '../../utils/errorHandling';\r\n\r\ninterface ErrorBoundaryConfig {\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\r\n  enableReporting?: boolean;\r\n  context?: string;\r\n}\r\n\r\n/**\r\n * Default error fallback component\r\n */\r\nconst DefaultErrorFallback: React.FC<{ error: Error; resetError: () => void }> = ({ \r\n  error, \r\n  resetError \r\n}) => (\r\n  <div className=\"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50\">\r\n    <div className=\"text-red-500 text-2xl mb-2\">⚠️</div>\r\n    <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Something went wrong</h3>\r\n    <p className=\"text-red-600 text-sm mb-4 text-center max-w-md\">\r\n      {error.message || 'An unexpected error occurred'}\r\n    </p>\r\n    <button\r\n      onClick={resetError}\r\n      className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\"\r\n    >\r\n      Try Again\r\n    </button>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Higher-order component that wraps a component with an error boundary\r\n */\r\nexport function withErrorBoundary<P extends object>(\r\n  Component: ComponentType<P>,\r\n  config: ErrorBoundaryConfig = {}\r\n) {\r\n  const {\r\n    fallback: FallbackComponent = DefaultErrorFallback,\r\n    onError,\r\n    enableReporting = true,\r\n    context\r\n  } = config;\r\n\r\n  const WrappedComponent = forwardRef<any, P>((props, ref) => {\r\n    const handleError = (error: Error, errorInfo: React.ErrorInfo) => {\r\n      // Report error if enabled\r\n      if (enableReporting) {\r\n        reportError(error, context || Component.displayName || Component.name, {\r\n          componentStack: errorInfo.componentStack,\r\n          errorBoundary: true\r\n        });\r\n      }\r\n\r\n      // Call custom error handler if provided\r\n      if (onError) {\r\n        onError(error, errorInfo);\r\n      }\r\n    };\r\n\r\n    return (\r\n      <ErrorBoundary\r\n        fallback={<FallbackComponent error={new Error()} resetError={() => window.location.reload()} />}\r\n        onError={handleError}\r\n      >\r\n        <Component {...(props as any)} ref={ref} />\r\n      </ErrorBoundary>\r\n    );\r\n  });\r\n\r\n  // Set display name for debugging\r\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\r\n\r\n  return WrappedComponent;\r\n}\r\n\r\n/**\r\n * Hook for creating error boundary configuration\r\n */\r\nexport const useErrorBoundaryConfig = (\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void,\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>,\r\n  context?: string\r\n): ErrorBoundaryConfig => {\r\n  return {\r\n    ...(onError && { onError }),\r\n    ...(fallback && { fallback }),\r\n    ...(context && { context }),\r\n    enableReporting: true\r\n  };\r\n};\r\n\r\n/**\r\n * Decorator for class components\r\n */\r\nexport const errorBoundary = (config: ErrorBoundaryConfig = {}) => {\r\n  return <P extends object>(Component: ComponentType<P>) => {\r\n    return withErrorBoundary(Component, config);\r\n  };\r\n};\r\n\r\nexport default withErrorBoundary;\r\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownTrayIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "/**\r\n * Chart.js Configuration\r\n *\r\n * This file configures Chart.js and registers all necessary components.\r\n */\r\n\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler,\r\n  RadialLinearScale,\r\n  type ChartOptions\r\n} from 'chart.js';\r\n\r\n// Register ChartJS components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement, // Required for Pie and Doughnut charts\r\n  RadialLinearScale,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n);\r\n\r\n// Default chart options\r\nexport const defaultLineChartOptions: ChartOptions<'line'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultPieChartOptions: ChartOptions<'pie'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      position: 'bottom',\r\n      labels: {\r\n        color: '#000000',\r\n        font: {\r\n          size: 12\r\n        }\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultBarChartOptions: ChartOptions<'bar'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Helper function to destroy chart instances\r\nexport const destroyChart = (chartInstance: ChartJS | null) => {\r\n  if (chartInstance) {\r\n    chartInstance.destroy();\r\n  }\r\n};\r\n\r\nexport default ChartJS;\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "import * as React from \"react\";\nfunction CalendarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CalendarIcon);\nexport default ForwardRef;", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction DocumentChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(DocumentChartBarIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowPathIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowPathIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport Card from '../../../components/common/Card';\r\n\r\n// Helper function to get appropriate background class for icon\r\nconst getIconBackgroundClass = (icon: React.ReactNode): string => {\r\n  if (!React.isValidElement(icon)) return 'bg-primary bg-opacity-10';\r\n\r\n  // Get the className from the icon props\r\n  const className = icon.props.className || '';\r\n\r\n  // Debug the className to see what we're working with\r\n  console.log('Icon className:', className);\r\n\r\n  // Extract color from text-{color} class with more specific matching\r\n  if (className.includes('text-primary')) return 'bg-primary-500 bg-opacity-10';\r\n  if (className.includes('text-blue')) return 'bg-blue-500 bg-opacity-10';\r\n  if (className.includes('text-green')) return 'bg-green-500 bg-opacity-10';\r\n  if (className.includes('text-yellow')) return 'bg-yellow-500 bg-opacity-10';\r\n  if (className.includes('text-red')) return 'bg-red-500 bg-opacity-10';\r\n  if (className.includes('text-purple')) return 'bg-purple-500 bg-opacity-10';\r\n  if (className.includes('text-indigo')) return 'bg-indigo-500 bg-opacity-10';\r\n  if (className.includes('text-pink')) return 'bg-pink-500 bg-opacity-10';\r\n  if (className.includes('text-gray')) return 'bg-gray-500 bg-opacity-10';\r\n\r\n  // Default fallback - use primary color\r\n  return 'bg-primary bg-opacity-10';\r\n};\r\n\r\ninterface StatCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  icon: React.ReactNode;\r\n  change?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n  isLoading?: boolean;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n}\r\n\r\nconst StatCard: React.FC<StatCardProps> = ({\r\n  title,\r\n  value,\r\n  icon,\r\n  change,\r\n  isLoading = false,\r\n  onClick,\r\n  hoverable = true,\r\n}) => {\r\n  return (\r\n    <Card\r\n      className=\"transition-transform duration-300 hover:scale-105\"\r\n      {...(onClick && { onClick })}\r\n      hoverable={hoverable}\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <p className=\"text-sm font-medium text-gray-500\">{title}</p>\r\n\r\n          {isLoading ? (\r\n            <div className=\"h-8 w-24 bg-gray-200 animate-pulse rounded mt-1\"></div>\r\n          ) : (\r\n            <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{value}</p>\r\n          )}\r\n\r\n          {change && !isLoading && (\r\n            <div className=\"flex items-center mt-1\">\r\n              <span\r\n                className={`text-sm font-medium ${\r\n                  change.isPositive ? 'text-green-600' : 'text-red-600'\r\n                }`}\r\n              >\r\n                {change.isPositive ? '↑' : '↓'} {change.value}%\r\n              </span>\r\n              <span className=\"text-sm text-gray-500 ml-1\">vs last period</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className={`p-3 rounded-full ${getIconBackgroundClass(icon)}`}>\r\n          {/* Ensure consistent icon styling while preserving color */}\r\n          {React.isValidElement(icon) ? (\r\n            (() => {\r\n              const iconElement = icon as React.ReactElement;\r\n              const existingClassName = iconElement.props.className || '';\r\n              const colorMatch = existingClassName.match(/text-[a-z0-9-]+/);\r\n              const colorClass = colorMatch ? colorMatch[0] : 'text-primary';\r\n\r\n              return React.cloneElement(iconElement, {\r\n                className: `w-6 h-6 ${colorClass}`\r\n              });\r\n            })()\r\n          ) : (\r\n            icon\r\n          )}\r\n        </div>\r\n      </div>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default StatCard;\r\n", "/**\r\n * Sales Chart Component\r\n *\r\n * This component displays a chart of sales data.\r\n */\r\n\r\nimport React, { useState, useRef, useEffect, useMemo, useCallback, memo } from 'react';\r\nimport { Line } from 'react-chartjs-2';\r\nimport { Chart as ChartJS } from 'chart.js';\r\nimport Card from '../../../components/common/Card';\r\nimport Button from '../../../components/common/Button';\r\nimport LoadingSpinner from '../../../components/common/LoadingSpinner';\r\nimport type { SalesData } from '../types/index';\r\nimport { defaultLineChartOptions, destroyChart } from '../../../utils/chartConfig';\r\n\r\ninterface SalesChartProps {\r\n  data: SalesData[];\r\n  isLoading?: boolean;\r\n  onPeriodChange?: (period: 'day' | 'week' | 'month' | 'year') => void;\r\n}\r\n\r\nconst SalesChart: React.FC<SalesChartProps> = ({\r\n  data,\r\n  isLoading = false,\r\n  onPeriodChange\r\n}) => {\r\n  const [activePeriod, setActivePeriod] = useState<'day' | 'week' | 'month' | 'year'>('week');\r\n  const chartRef = useRef<ChartJS | null>(null);\r\n\r\n  // Clean up chart instance on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      destroyChart(chartRef.current);\r\n    };\r\n  }, []);\r\n\r\n  // Memoize event handler to prevent unnecessary re-renders\r\n  const handlePeriodChange = useCallback((period: 'day' | 'week' | 'month' | 'year') => {\r\n    setActivePeriod(period);\r\n    if (onPeriodChange) {\r\n      onPeriodChange(period);\r\n    }\r\n  }, [onPeriodChange]);\r\n\r\n  // Memoize chart data to prevent unnecessary recalculations\r\n  const chartData = useMemo(() => ({\r\n    labels: data.map(item => item.date),\r\n    datasets: [\r\n      {\r\n        label: 'Sales',\r\n        data: data.map(item => item.amount),\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n        tension: 0.4,\r\n        fill: true,\r\n        pointBackgroundColor: '#F28B22',\r\n        pointBorderColor: '#fff',\r\n        pointBorderWidth: 2,\r\n        pointRadius: 4,\r\n        pointHoverRadius: 6\r\n      }\r\n    ]\r\n  }), [data]);\r\n\r\n  // Memoize chart options to prevent unnecessary re-initializations\r\n  const options = useMemo(() => ({\r\n    ...defaultLineChartOptions,\r\n    plugins: {\r\n      ...defaultLineChartOptions.plugins,\r\n      tooltip: {\r\n        ...(defaultLineChartOptions.plugins?.tooltip || {}),\r\n        callbacks: {\r\n          label: function(context: any) {\r\n            return `$${context.parsed.y.toLocaleString()}`;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    scales: {\r\n      ...defaultLineChartOptions.scales,\r\n      y: {\r\n        ...(defaultLineChartOptions.scales?.y || {}),\r\n        ticks: {\r\n          callback: function(value: any) {\r\n            return '$' + value.toLocaleString();\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }), []);\r\n\r\n  return (\r\n    <Card className=\"h-full\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900\">Sales Overview</h3>\r\n        <div className=\"flex space-x-2 mt-3 sm:mt-0\">\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'day' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('day')}\r\n          >\r\n            Day\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'week' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('week')}\r\n          >\r\n            Week\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'month' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('month')}\r\n          >\r\n            Month\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'year' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('year')}\r\n          >\r\n            Year\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {isLoading ? (\r\n        <div className=\"h-80 flex items-center justify-center\">\r\n          <LoadingSpinner size=\"lg\" />\r\n        </div>\r\n      ) : (\r\n        <div className=\"h-80\">\r\n          <Line\r\n            data={chartData}\r\n            options={options as any}\r\n            ref={(ref) => {\r\n              if (ref) {\r\n                chartRef.current = ref;\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default memo(SalesChart);\r\n\r\n", "/**\r\n * User Growth Chart Component\r\n *\r\n * This component displays a chart of user growth data.\r\n */\r\n\r\nimport React, { useState, useRef, useEffect, useMemo, useCallback, memo } from 'react';\r\nimport { Line } from 'react-chartjs-2';\r\nimport { Chart as ChartJS } from 'chart.js';\r\nimport Card from '../../../components/common/Card';\r\nimport Button from '../../../components/common/Button';\r\nimport LoadingSpinner from '../../../components/common/LoadingSpinner';\r\nimport { defaultLineChartOptions, destroyChart } from '../../../utils/chartConfig';\r\n\r\ninterface UserGrowthData {\r\n  date: string;\r\n  users: number;\r\n}\r\n\r\ninterface UserGrowthChartProps {\r\n  data: UserGrowthData[];\r\n  isLoading?: boolean;\r\n  onPeriodChange?: (period: 'day' | 'week' | 'month' | 'year') => void;\r\n  showCard?: boolean;\r\n  title?: string;\r\n}\r\n\r\nconst UserGrowthChart: React.FC<UserGrowthChartProps> = ({\r\n  data,\r\n  isLoading = false,\r\n  onPeriodChange,\r\n  showCard = true,\r\n  title = \"User Growth\"\r\n}) => {\r\n  const [activePeriod, setActivePeriod] = useState<'day' | 'week' | 'month' | 'year'>('week');\r\n  const chartRef = useRef<ChartJS | null>(null);\r\n\r\n  // Clean up chart instance on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      destroyChart(chartRef.current);\r\n    };\r\n  }, []);\r\n\r\n  // Memoize event handler to prevent unnecessary re-renders\r\n  const handlePeriodChange = useCallback((period: 'day' | 'week' | 'month' | 'year') => {\r\n    setActivePeriod(period);\r\n    if (onPeriodChange) {\r\n      onPeriodChange(period);\r\n    }\r\n  }, [onPeriodChange]);\r\n\r\n  // Memoize chart data to prevent unnecessary recalculations\r\n  const chartData = useMemo(() => ({\r\n    labels: data.map(item => item.date),\r\n    datasets: [\r\n      {\r\n        label: 'Users',\r\n        data: data.map(item => item.users),\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n        tension: 0.4,\r\n        fill: true,\r\n        pointBackgroundColor: '#F28B22',\r\n        pointBorderColor: '#fff',\r\n        pointBorderWidth: 2,\r\n        pointRadius: 4,\r\n        pointHoverRadius: 6\r\n      }\r\n    ]\r\n  }), [data]);\r\n\r\n  // Memoize chart options to prevent unnecessary re-initializations\r\n  const options = useMemo(() => ({\r\n    ...defaultLineChartOptions,\r\n    plugins: {\r\n      ...defaultLineChartOptions.plugins,\r\n      tooltip: {\r\n        ...(defaultLineChartOptions.plugins?.tooltip || {}),\r\n        callbacks: {\r\n          label: function(context: any) {\r\n            return `${context.parsed.y.toLocaleString()} users`;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }), []);\r\n\r\n  const chartContent = (\r\n    <>\r\n      {isLoading ? (\r\n        <div className=\"h-80 flex items-center justify-center\">\r\n          <LoadingSpinner size=\"lg\" />\r\n        </div>\r\n      ) : (\r\n        <div className=\"h-80\">\r\n          <Line\r\n            data={chartData}\r\n            options={options as any}\r\n            ref={(ref) => {\r\n              if (ref) {\r\n                chartRef.current = ref;\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n\r\n  if (!showCard) {\r\n    return chartContent;\r\n  }\r\n\r\n  return (\r\n    <Card className=\"h-full\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\r\n        <div className=\"flex space-x-2 mt-3 sm:mt-0\">\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'day' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('day')}\r\n          >\r\n            Day\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'week' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('week')}\r\n          >\r\n            Week\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'month' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('month')}\r\n          >\r\n            Month\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            variant={activePeriod === 'year' ? 'primary' : 'outline'}\r\n            onClick={() => handlePeriodChange('year')}\r\n          >\r\n            Year\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      {chartContent}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default memo(UserGrowthChart);\r\n", "/**\n * Category Distribution Chart Component\n *\n * This component displays a pie chart showing the distribution of categories.\n */\n\nimport React, { useRef, useEffect, useMemo } from 'react';\nimport { Pie } from 'react-chartjs-2';\nimport { Chart as ChartJS } from 'chart.js';\nimport Card from '../../../components/common/Card';\nimport type { CategoryDistributionData } from '../types/index';\nimport { defaultPieChartOptions, destroyChart } from '../../../utils/chartConfig';\n\ninterface CategoryDistributionChartProps {\n  data: CategoryDistributionData;\n  isLoading?: boolean;\n  title?: string;\n  showCard?: boolean;\n  className?: string;\n}\n\nconst CategoryDistributionChart: React.FC<CategoryDistributionChartProps> = ({\n  data,\n  isLoading = false,\n  title = \"Category Distribution\",\n  showCard = true,\n  className = ''\n}) => {\n  const chartRef = useRef<ChartJS | null>(null);\n\n  // Clean up chart instance on unmount with safe destruction\n  useEffect(() => {\n    return () => {\n      try {\n        destroyChart(chartRef.current);\n      } catch (error) {\n        // Silently handle any destruction errors\n        console.warn('Chart destruction warning:', error);\n      }\n    };\n  }, []);\n\n  // Memoize chart data to prevent unnecessary re-renders\n  const chartData = useMemo(() => ({\n    labels: data.labels || [],\n    datasets: data.datasets || []\n  }), [data.labels, data.datasets]);\n\n  // Memoize chart options to prevent unnecessary re-initializations\n  const chartOptions = useMemo(() => ({\n    ...defaultPieChartOptions,\n    plugins: {\n      ...defaultPieChartOptions.plugins,\n      title: {\n        ...defaultPieChartOptions.plugins?.title,\n        display: false // Disable Chart.js title since we use Card title\n      }\n    }\n  }), []);\n\n  // Check for missing or empty data\n  const hasValidData = data.labels && data.labels.length > 0 &&\n                      data.datasets && data.datasets.length > 0 &&\n                      data.datasets[0]?.data && data.datasets[0].data.length > 0;\n\n  const chartContent = (\n    <>\n      {isLoading ? (\n        <div className=\"h-80 flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"></div>\n        </div>\n      ) : !hasValidData ? (\n        <div className=\"h-80 flex items-center justify-center\">\n          <div className=\"text-center text-gray-500\">\n            No data available\n          </div>\n        </div>\n      ) : (\n        <>\n          <div className=\"h-80\">\n            <Pie\n              ref={(ref) => {\n                if (ref) {\n                  chartRef.current = ref;\n                }\n              }}\n              data={chartData}\n              options={chartOptions}\n            />\n          </div>\n\n          {/* Legend */}\n          <div className=\"mt-4 space-y-2\">\n            {data.labels.map((label, index) => (\n              <div key={index} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <div\n                    className=\"w-3 h-3 rounded-full mr-2\"\n                    style={{\n                      backgroundColor: data.datasets[0]?.backgroundColor?.[index] as string || '#ccc'\n                    }}\n                  ></div>\n                  <span className=\"text-sm text-gray-600\">{label}</span>\n                </div>\n                <span className=\"text-sm font-medium\">\n                  {data.datasets[0]?.data?.[index] || 0}%\n                </span>\n              </div>\n            ))}\n          </div>\n        </>\n      )}\n    </>\n  );\n\n  if (!showCard) {\n    return <div className={className}>{chartContent}</div>;\n  }\n\n  return (\n    <Card\n      title={<h3 className=\"text-lg font-semibold text-black\">{title}</h3>}\n      className={className}\n    >\n      {chartContent}\n    </Card>\n  );\n};\n\nexport default React.memo(CategoryDistributionChart);\n", "/**\r\n * Order Details Modal Component\r\n * \r\n * This component displays detailed information about an order in a modal.\r\n */\r\n\r\nimport React from 'react';\r\nimport Modal from '../../../components/common/Modal';\r\nimport Button from '../../../components/common/Button';\r\nimport type { RecentOrder } from '../types/index';\r\nimport { \r\n  CheckCircleIcon, \r\n  ClockIcon, \r\n  XCircleIcon,\r\n  TruckIcon,\r\n  CalendarIcon,\r\n  UserIcon,\r\n  CurrencyDollarIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface OrderDetailsModalProps {\r\n  order: RecentOrder | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst OrderDetailsModal: React.FC<OrderDetailsModalProps> = ({ \r\n  order, \r\n  isOpen, \r\n  onClose \r\n}) => {\r\n  if (!order) return null;\r\n\r\n  // Helper function to get status icon\r\n  const getStatusIcon = (status: string) => {\r\n    switch(status) {\r\n      case 'pending':\r\n        return <ClockIcon className=\"w-5 h-5 text-yellow-500\" />;\r\n      case 'processing':\r\n        return <CheckCircleIcon className=\"w-5 h-5 text-blue-500\" />;\r\n      case 'completed':\r\n        return <TruckIcon className=\"w-5 h-5 text-green-500\" />;\r\n      case 'cancelled':\r\n        return <XCircleIcon className=\"w-5 h-5 text-red-500\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Helper function to get status color class\r\n  const getStatusColorClass = (status: string) => {\r\n    switch(status) {\r\n      case 'pending':\r\n        return 'bg-yellow-100 text-yellow-800';\r\n      case 'processing':\r\n        return 'bg-blue-100 text-blue-800';\r\n      case 'completed':\r\n        return 'bg-green-100 text-green-800';\r\n      case 'cancelled':\r\n        return 'bg-red-100 text-red-800';\r\n      default:\r\n        return '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Order Details\"\r\n      size=\"md\"\r\n      footer={\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={onClose}\r\n        >\r\n          Close\r\n        </Button>\r\n      }\r\n    >\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h3 className=\"text-lg font-medium text-gray-900\">Order #{order.id}</h3>\r\n            <div className=\"mt-1\">\r\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColorClass(order.status)}`}>\r\n                {getStatusIcon(order.status)}\r\n                <span className=\"ml-1\">{order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Unknown'}</span>\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <div className=\"text-sm text-gray-500\">Total Amount</div>\r\n            <div className=\"text-xl font-bold text-primary\">${order.amount.toLocaleString()}</div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex items-start\">\r\n            <UserIcon className=\"w-5 h-5 text-gray-400 mt-0.5 mr-2\" />\r\n            <div>\r\n              <div className=\"text-sm font-medium text-gray-500\">Customer</div>\r\n              <div className=\"text-sm text-gray-900\">{order.customer}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-start\">\r\n            <CalendarIcon className=\"w-5 h-5 text-gray-400 mt-0.5 mr-2\" />\r\n            <div>\r\n              <div className=\"text-sm font-medium text-gray-500\">Order Date</div>\r\n              <div className=\"text-sm text-gray-900\">{order.date}</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-start\">\r\n            <CurrencyDollarIcon className=\"w-5 h-5 text-gray-400 mt-0.5 mr-2\" />\r\n            <div>\r\n              <div className=\"text-sm font-medium text-gray-500\">Payment</div>\r\n              <div className=\"text-sm text-gray-900\">${order.amount.toLocaleString()}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\r\n          <p className=\"text-sm text-gray-500\">\r\n            This is a simplified view of the order. To see full details including items, shipping information, and more, please visit the Orders page.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default OrderDetailsModal;\r\n", "/**\r\n * Recent Orders Component\r\n *\r\n * This component displays a list of recent orders.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport Card from '../../../components/common/Card';\r\nimport type { RecentOrder } from '../types/index';\r\nimport { EyeIcon } from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../../../constants/routes';\r\nimport OrderDetailsModal from './OrderDetailsModal';\r\n\r\ninterface RecentOrdersProps {\r\n  orders: RecentOrder[];\r\n  isLoading?: boolean;\r\n  onViewOrder?: (orderId: string) => void;\r\n}\r\n\r\nconst RecentOrders: React.FC<RecentOrdersProps> = ({\r\n  orders,\r\n  isLoading = false,\r\n  onViewOrder\r\n}) => {\r\n  const navigate = useNavigate();\r\n  const [selectedOrder, setSelectedOrder] = useState<RecentOrder | null>(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  const handleViewOrder = (order: RecentOrder, e: React.MouseEvent) => {\r\n    // Stop propagation to prevent row click navigation when clicking the eye icon\r\n    e.stopPropagation();\r\n    setSelectedOrder(order);\r\n    setIsModalOpen(true);\r\n    if (onViewOrder) {\r\n      onViewOrder(order.id);\r\n    }\r\n  };\r\n\r\n  const navigateToOrderDetails = (orderId: string) => {\r\n    navigate(ROUTES.getOrderDetailsRoute(orderId));\r\n  };\r\n\r\n  const getStatusColor = (status: RecentOrder['status']) => {\r\n    switch (status) {\r\n      case 'pending':\r\n        return 'bg-yellow-100 text-yellow-800';\r\n      case 'processing':\r\n        return 'bg-blue-100 text-blue-800';\r\n      case 'completed':\r\n        return 'bg-green-100 text-green-800';\r\n      case 'cancelled':\r\n        return 'bg-red-100 text-red-800';\r\n      default:\r\n        return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"h-full\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900\">Recent Orders</h3>\r\n        <Link\r\n          to={ROUTES.ORDERS}\r\n          className=\"text-sm text-primary hover:text-primary-dark font-medium\"\r\n        >\r\n          View All\r\n        </Link>\r\n      </div>\r\n\r\n      {isLoading ? (\r\n        <div className=\"space-y-4\">\r\n          {[...Array(5)].map((_, index) => (\r\n            <div key={index} className=\"animate-pulse flex items-center justify-between\">\r\n              <div className=\"space-y-2\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\r\n                <div className=\"h-3 bg-gray-200 rounded w-16\"></div>\r\n              </div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\r\n              <div className=\"h-6 bg-gray-200 rounded w-20\"></div>\r\n              <div className=\"h-8 bg-gray-200 rounded-full w-8\"></div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-hidden\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Order\r\n                </th>\r\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Customer\r\n                </th>\r\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Amount\r\n                </th>\r\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Date\r\n                </th>\r\n                <th scope=\"col\" className=\"relative px-4 py-3\">\r\n                  <span className=\"sr-only\">Actions</span>\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {orders.map((order) => (\r\n                <tr\r\n                  key={order.id}\r\n                  className=\"hover:bg-gray-50 cursor-pointer\"\r\n                  onClick={() => navigateToOrderDetails(order.id)}\r\n                >\r\n                  <td className=\"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                    #{order.id}\r\n                  </td>\r\n                  <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {order.customer}\r\n                  </td>\r\n                  <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                    ${order.amount.toLocaleString()}\r\n                  </td>\r\n                  <td className=\"px-4 py-4 whitespace-nowrap\">\r\n                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(order.status)}`}>\r\n                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {order.date}\r\n                  </td>\r\n                  <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <button\r\n                      onClick={(e) => handleViewOrder(order, e)}\r\n                      className=\"text-gray-500 hover:text-primary\"\r\n                    >\r\n                      <EyeIcon className=\"h-5 w-5\" />\r\n                    </button>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Order Details Modal */}\r\n      <OrderDetailsModal\r\n        order={selectedOrder}\r\n        isOpen={isModalOpen}\r\n        onClose={() => setIsModalOpen(false)}\r\n      />\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default RecentOrders;\r\n", "/**\r\n * Dashboard API Service\r\n *\r\n * This file provides methods for interacting with the dashboard API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport type {\r\n  DashboardStats,\r\n  DashboardStatsResponse,\r\n  SalesData,\r\n  SalesDataResponse,\r\n  UserGrowth,\r\n  UserGrowthResponse,\r\n  CategoryDistributionData,\r\n  CategoryDistributionResponse\r\n} from '../types';\r\n\r\n/**\r\n * Transform backend dashboard stats response to frontend format\r\n */\r\nconst transformDashboardStats = (backendData: DashboardStatsResponse): DashboardStats => {\r\n  return {\r\n    summary: {\r\n      totalUsers: backendData.totalUsers,\r\n      totalSuppliers: backendData.totalSuppliers,\r\n      totalOrders: backendData.totalOrders,\r\n      totalRevenue: backendData.totalRevenue,\r\n      pendingVerifications: backendData.pendingVerifications,\r\n      activeUsers: backendData.activeUsers,\r\n    },\r\n    monthlyGrowth: backendData.monthlyGrowth,\r\n    // Initialize with empty chart data - will be populated by separate API calls\r\n    revenueData: {\r\n      labels: [],\r\n      datasets: [{\r\n        label: 'Revenue',\r\n        data: [],\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n      }]\r\n    },\r\n    userGrowth: {\r\n      labels: [],\r\n      datasets: [{\r\n        label: 'Users',\r\n        data: [],\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n      }]\r\n    },\r\n    categoryDistribution: {\r\n      labels: [],\r\n      datasets: [{\r\n        data: [],\r\n        backgroundColor: [],\r\n        borderWidth: 1,\r\n      }]\r\n    },\r\n    recentOrders: [],\r\n    topSuppliers: []\r\n  };\r\n};\r\n\r\n/**\r\n * Transform backend sales data response to frontend format\r\n */\r\nconst transformSalesData = (backendData: SalesDataResponse): SalesData[] => {\r\n  return backendData.data.map(item => ({\r\n    date: item.date,\r\n    amount: item.sales,\r\n    orders: item.orders\r\n  }));\r\n};\r\n\r\n/**\r\n * Transform backend user growth response to frontend format\r\n */\r\nconst transformUserGrowth = (backendData: UserGrowthResponse): UserGrowth[] => {\r\n  return backendData.data.map(item => ({\r\n    date: item.date,\r\n    users: item.totalUsers,\r\n    newUsers: item.newUsers,\r\n    totalUsers: item.totalUsers\r\n  }));\r\n};\r\n\r\n/**\r\n * Transform backend category distribution response to frontend format\r\n */\r\nconst transformCategoryDistribution = (backendData: CategoryDistributionResponse[]): CategoryDistributionData => {\r\n  const colors = [\r\n    '#F28B22', '#3B82F6', '#10B981', '#F59E0B', '#EF4444',\r\n    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316', '#EC4899'\r\n  ];\r\n\r\n  return {\r\n    labels: backendData.map(item => item.category),\r\n    datasets: [{\r\n      data: backendData.map(item => item.count),\r\n      backgroundColor: backendData.map((_, index) => colors[index % colors.length] || '#F28B22'),\r\n      borderWidth: 1,\r\n    }]\r\n  };\r\n};\r\n\r\nexport const dashboardApi = {\r\n  /**\r\n   * Get dashboard statistics\r\n   */\r\n  getDashboardStats: async (): Promise<DashboardStats> => {\r\n    try {\r\n      const response = await apiClient.get<DashboardStatsResponse>('/dashboard/stats');\r\n      // Use direct response validation since this is not a getById operation\r\n      if (response.data) {\r\n        return transformDashboardStats(response.data);\r\n      } else {\r\n        throw new Error('No dashboard statistics data received');\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get sales data for a specific period\r\n   */\r\n  getSalesData: async (period: 'day' | 'week' | 'month' | 'year'): Promise<SalesData[]> => {\r\n    try {\r\n      const response = await apiClient.get<SalesDataResponse>(`/dashboard/sales`, { params: { period } });\r\n      if (response.data) {\r\n        return transformSalesData(response.data);\r\n      } else {\r\n        throw new Error('No sales data received');\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get user growth data\r\n   */\r\n  getUserGrowth: async (period: 'week' | 'month' | 'year'): Promise<UserGrowth[]> => {\r\n    try {\r\n      const response = await apiClient.get<UserGrowthResponse>(`/dashboard/user-growth`, { params: { period } });\r\n      if (response.data) {\r\n        return transformUserGrowth(response.data);\r\n      } else {\r\n        throw new Error('No user growth data received');\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get category distribution data\r\n   */\r\n  getCategoryDistribution: async (): Promise<CategoryDistributionData> => {\r\n    try {\r\n      const response = await apiClient.get<CategoryDistributionResponse[]>(`/dashboard/category-distribution`);\r\n      if (response.data) {\r\n        return transformCategoryDistribution(response.data);\r\n      } else {\r\n        throw new Error('No category distribution data received');\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default dashboardApi;\r\n", "/**\n * Sales Chart Container Component\n *\n * This component handles fetching sales data and displaying the sales chart.\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport SalesChart from './SalesChart';\nimport { dashboardApi } from '../api/dashboardApi';\nimport type { SalesData } from '../types';\n\ninterface SalesChartContainerProps {\n  className?: string;\n}\n\nconst SalesChartContainer: React.FC<SalesChartContainerProps> = ({ className }) => {\n  const [salesData, setSalesData] = useState<SalesData[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  const fetchSalesData = useCallback(async (period: 'day' | 'week' | 'month' | 'year') => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const data = await dashboardApi.getSalesData(period);\n      setSalesData(data);\n    } catch (err) {\n      setError(err as Error);\n      console.error('Failed to fetch sales data:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Load initial data\n  useEffect(() => {\n    fetchSalesData('month');\n  }, [fetchSalesData]);\n\n  const handlePeriodChange = useCallback((period: 'day' | 'week' | 'month' | 'year') => {\n    fetchSalesData(period);\n  }, [fetchSalesData]);\n\n  if (error) {\n    return (\n      <div className={`bg-white rounded-lg shadow p-6 ${className || ''}`}>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Sales Data</h3>\n        <div className=\"text-center text-red-500\">\n          <p>Failed to load sales data</p>\n          <button \n            onClick={() => fetchSalesData('month')}\n            className=\"mt-2 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            Try again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={className}>\n      <SalesChart\n        data={salesData}\n        isLoading={isLoading}\n        onPeriodChange={handlePeriodChange}\n      />\n    </div>\n  );\n};\n\nexport default SalesChartContainer;\n", "/**\n * User Growth Chart Container Component\n *\n * This component handles fetching user growth data and displaying the user growth chart.\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport UserGrowthChart from './UserGrowthChart';\nimport { dashboardApi } from '../api/dashboardApi';\nimport type { UserGrowth } from '../types';\n\ninterface UserGrowthChartContainerProps {\n  className?: string;\n}\n\nconst UserGrowthChartContainer: React.FC<UserGrowthChartContainerProps> = ({ className }) => {\n  const [userGrowthData, setUserGrowthData] = useState<UserGrowth[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  const fetchUserGrowthData = useCallback(async (period: 'week' | 'month' | 'year') => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const data = await dashboardApi.getUserGrowth(period);\n      setUserGrowthData(data);\n    } catch (err) {\n      setError(err as Error);\n      console.error('Failed to fetch user growth data:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Load initial data\n  useEffect(() => {\n    fetchUserGrowthData('month');\n  }, [fetchUserGrowthData]);\n\n  const handlePeriodChange = useCallback((period: 'day' | 'week' | 'month' | 'year') => {\n    // Convert day to week since user growth API doesn't support day period\n    const validPeriod = period === 'day' ? 'week' : period as 'week' | 'month' | 'year';\n    fetchUserGrowthData(validPeriod);\n  }, [fetchUserGrowthData]);\n\n  if (error) {\n    return (\n      <div className={`bg-white rounded-lg shadow p-6 ${className || ''}`}>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">User Growth</h3>\n        <div className=\"text-center text-red-500\">\n          <p>Failed to load user growth data</p>\n          <button \n            onClick={() => fetchUserGrowthData('month')}\n            className=\"mt-2 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            Try again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={className}>\n      <UserGrowthChart\n        data={userGrowthData}\n        isLoading={isLoading}\n        onPeriodChange={handlePeriodChange}\n      />\n    </div>\n  );\n};\n\nexport default UserGrowthChartContainer;\n", "/**\n * Category Distribution Chart Container Component\n *\n * This component handles fetching category distribution data and displaying the chart.\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport CategoryDistributionChart from './CategoryDistributionChart';\nimport { dashboardApi } from '../api/dashboardApi';\nimport type { CategoryDistributionData } from '../types';\n\ninterface CategoryDistributionChartContainerProps {\n  className?: string;\n  title?: string;\n}\n\nconst CategoryDistributionChartContainer: React.FC<CategoryDistributionChartContainerProps> = ({ \n  className,\n  title = \"Category Distribution\"\n}) => {\n  const [categoryData, setCategoryData] = useState<CategoryDistributionData>({\n    labels: [],\n    datasets: [{\n      data: [],\n      backgroundColor: [],\n      borderWidth: 1,\n    }]\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  const fetchCategoryData = useCallback(async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const data = await dashboardApi.getCategoryDistribution();\n      setCategoryData(data);\n    } catch (err) {\n      setError(err as Error);\n      console.error('Failed to fetch category distribution data:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Load initial data\n  useEffect(() => {\n    fetchCategoryData();\n  }, [fetchCategoryData]);\n\n  if (error) {\n    return (\n      <div className={`bg-white rounded-lg shadow p-6 ${className || ''}`}>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{title}</h3>\n        <div className=\"text-center text-red-500\">\n          <p>Failed to load category distribution data</p>\n          <button \n            onClick={fetchCategoryData}\n            className=\"mt-2 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            Try again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={className}>\n      <CategoryDistributionChart\n        data={categoryData}\n        isLoading={isLoading}\n        title={title}\n      />\n    </div>\n  );\n};\n\nexport default CategoryDistributionChartContainer;\n", "/**\r\n * Dashboard Hook\r\n * \r\n * This hook provides methods and state for working with dashboard data.\r\n */\r\n\r\nimport { useState, useCallback, useEffect, useRef } from 'react';\r\nimport type { DashboardStats } from '../types/index';\r\nimport dashboardApi from '../api/dashboardApi';\r\nimport useNotification from '../../../hooks/useNotification';\r\n\r\nexport const useDashboard = () => {\r\n  const [stats, setStats] = useState<DashboardStats | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues with showNotification\r\n  const showNotificationRef = useRef(showNotification);\r\n  const hasInitialFetched = useRef(false);\r\n  const cacheRef = useRef<{ data: DashboardStats; timestamp: number } | null>(null);\r\n  const CACHE_TTL = 5 * 60 * 1000; // 5 minutes\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n\r\n  // Check if cached data is still valid\r\n  const isCacheValid = useCallback(() => {\r\n    if (!cacheRef.current) return false;\r\n    return Date.now() - cacheRef.current.timestamp < CACHE_TTL;\r\n  }, [CACHE_TTL]);\r\n\r\n  // Fetch dashboard statistics\r\n  const fetchStats = useCallback(async (forceRefresh = false) => {\r\n    // Use cache if available and valid, unless force refresh is requested\r\n    if (!forceRefresh && isCacheValid() && cacheRef.current) {\r\n      setStats(cacheRef.current.data);\r\n      return cacheRef.current.data;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await dashboardApi.getDashboardStats();\r\n      setStats(data);\r\n      // Cache the data\r\n      cacheRef.current = {\r\n        data,\r\n        timestamp: Date.now()\r\n      };\r\n      return data;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n\r\n      // Provide more specific error message\r\n      const errorMessage = error.message || 'Failed to fetch dashboard statistics';\r\n      console.error('Dashboard fetch error:', error);\r\n\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Dashboard Error',\r\n        message: errorMessage\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [isCacheValid]);\r\n\r\n  // Fetch sales data for a specific period\r\n  const fetchSalesData = useCallback(async (period: 'day' | 'week' | 'month' | 'year') => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await dashboardApi.getSalesData(period);\r\n      return data;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch sales data for ${period}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch user growth data\r\n  const fetchUserGrowth = useCallback(async (period: 'week' | 'month' | 'year') => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await dashboardApi.getUserGrowth(period);\r\n      return data;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch user growth data for ${period}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch category distribution data\r\n  const fetchCategoryDistribution = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await dashboardApi.getCategoryDistribution();\r\n      return data;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch category distribution data'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Load dashboard stats on mount (only if not already fetched)\r\n  useEffect(() => {\r\n    if (!hasInitialFetched.current) {\r\n      hasInitialFetched.current = true;\r\n      fetchStats().catch(err => {\r\n        console.error('Failed to fetch initial dashboard stats:', err);\r\n        // Error is already handled in fetchStats, just log here\r\n      });\r\n    }\r\n  }, [fetchStats]);\r\n\r\n  // Reset state when component unmounts to prevent stale data issues\r\n  useEffect(() => {\r\n    return () => {\r\n      // Don't reset cache on unmount to preserve data between navigations\r\n      // Only reset loading and error states\r\n      setIsLoading(false);\r\n      setError(null);\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    stats,\r\n    isLoading,\r\n    error,\r\n    fetchStats,\r\n    fetchSalesData,\r\n    fetchUserGrowth,\r\n    fetchCategoryDistribution\r\n  };\r\n};\r\n\r\nexport default useDashboard;\r\n\r\n", "/**\r\n * Dashboard Page\r\n *\r\n * The main dashboard page for the ConnectChain admin panel.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Button from '../components/common/Button';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport { formatCurrency } from '../utils/formatters';\r\n\r\nimport useErrorHandler from '../hooks/useErrorHandler';\r\nimport { safeAsyncOperation } from '../utils/errorHandling';\r\nimport withErrorBoundary from '../components/common/withErrorBoundary';\r\nimport {\r\n  UsersIcon,\r\n  ShoppingCartIcon,\r\n  ClockIcon,\r\n  CurrencyDollarIcon,\r\n  ArrowDownTrayIcon,\r\n  DocumentChartBarIcon,\r\n  ArrowPathIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\n// Import dashboard feature components\r\nimport {\r\n  StatCard,\r\n  SalesChartContainer,\r\n  UserGrowthChartContainer,\r\n  CategoryDistributionChartContainer,\r\n  RecentOrders,\r\n  useDashboard\r\n} from '../features/dashboard/index';\r\n\r\nconst DashboardPage: React.FC = () => {\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  // Use dashboard hook\r\n  const { stats: dashboardData, isLoading, fetchStats } = useDashboard();\r\n\r\n  // Error handling\r\n  const {\r\n    handleGeneralError\r\n  } = useErrorHandler({\r\n    enableNotifications: true,\r\n    enableReporting: true\r\n  });\r\n\r\n  // Handle refresh\r\n  const handleRefresh = async () => {\r\n    setIsRefreshing(true);\r\n\r\n    const result = await safeAsyncOperation(\r\n      async () => {\r\n        // Simulate data refresh with potential failure\r\n        await new Promise((resolve, reject) => {\r\n          setTimeout(() => {\r\n            if (Math.random() < 0.1) {\r\n              reject(new Error('Failed to refresh dashboard data'));\r\n            } else {\r\n              resolve(true);\r\n            }\r\n          }, 1500);\r\n        });\r\n\r\n        // Use the dashboard hook's fetchStats method with force refresh\r\n        await fetchStats(true);\r\n        return true;\r\n      },\r\n      {\r\n        timeout: 10000,\r\n        retries: 2,\r\n        operationName: 'Refresh Dashboard'\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      handleGeneralError(result.error, 'Dashboard Refresh');\r\n    }\r\n\r\n    setIsRefreshing(false);\r\n  };\r\n\r\n  // If data is not loaded yet\r\n  if (isLoading || !dashboardData) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <LoadingSpinner size=\"lg\" variant=\"pulse\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <PageHeader\r\n        title=\"Dashboard\"\r\n        description=\"Welcome to ConnectChain Admin Dashboard\"\r\n        actions={\r\n          <div className=\"flex flex-wrap gap-3\">\r\n            <Button\r\n              variant=\"outline\"\r\n              icon={<ArrowDownTrayIcon className=\"h-5 w-5\" />}\r\n            >\r\n              Export Report\r\n            </Button>\r\n            <Button\r\n              icon={<DocumentChartBarIcon className=\"h-5 w-5\" />}\r\n            >\r\n              Generate Report\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              icon={<ArrowPathIcon className=\"h-5 w-5\" />}\r\n              onClick={handleRefresh}\r\n              loading={isRefreshing}\r\n            >\r\n              Refresh\r\n            </Button>\r\n          </div>\r\n        }\r\n      />\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n        <StatCard\r\n          title=\"Total Users\"\r\n          value={dashboardData.summary.totalUsers.toLocaleString()}\r\n          icon={<UsersIcon className=\"w-6 h-6 text-primary\" />}\r\n          change={{\r\n            value: Math.abs(dashboardData.monthlyGrowth.users),\r\n            isPositive: dashboardData.monthlyGrowth.users >= 0\r\n          }}\r\n          // onClick={() => console.log('Card clicked')}\r\n        />\r\n\r\n        <StatCard\r\n          title=\"Total Orders\"\r\n          value={dashboardData.summary.totalOrders.toLocaleString()}\r\n          icon={<ShoppingCartIcon className=\"w-6 h-6 text-blue-500\" />}\r\n          change={{\r\n            value: Math.abs(dashboardData.monthlyGrowth.orders),\r\n            isPositive: dashboardData.monthlyGrowth.orders >= 0\r\n          }}\r\n        />\r\n\r\n        <StatCard\r\n          title=\"Active Users\"\r\n          value={dashboardData.summary.activeUsers.toLocaleString()}\r\n          icon={<ClockIcon className=\"w-6 h-6 text-green-500\" />}\r\n          change={{ value: 0, isPositive: true }}\r\n        />\r\n\r\n        <StatCard\r\n          title=\"Revenue\"\r\n          value={formatCurrency(dashboardData.summary.totalRevenue)}\r\n          icon={<CurrencyDollarIcon className=\"w-6 h-6 text-green-500\" />}\r\n          change={{\r\n            value: Math.abs(dashboardData.monthlyGrowth.revenue),\r\n            isPositive: dashboardData.monthlyGrowth.revenue >= 0\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Charts */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n        <SalesChartContainer />\r\n        <UserGrowthChartContainer />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n        <div className=\"lg:col-span-2\">\r\n          <RecentOrders\r\n            orders={dashboardData.recentOrders.map(order => ({\r\n              id: order.orderNumber,\r\n              customer: order.customerName,\r\n              amount: order.amount,\r\n              status: order.status as any,\r\n              date: order.date\r\n            }))}\r\n            onViewOrder={(orderId) => console.log('Order clicked:', orderId)}\r\n          />\r\n        </div>\r\n\r\n        <CategoryDistributionChartContainer\r\n          title=\"Category Distribution\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Wrap with error boundary\r\nexport default withErrorBoundary(DashboardPage, {\r\n  fallback: ({ error, resetError }) => (\r\n    <div className=\"flex flex-col items-center justify-center min-h-[400px] p-8\">\r\n      <div className=\"text-red-500 text-4xl mb-4\">📊</div>\r\n      <h2 className=\"text-xl font-semibold mb-2\">Dashboard Error</h2>\r\n      <p className=\"text-gray-600 mb-4 text-center max-w-md\">\r\n        {error.message || 'An error occurred while loading the dashboard'}\r\n      </p>\r\n      <button\r\n        onClick={resetError}\r\n        className=\"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors\"\r\n      >\r\n        Reload Dashboard\r\n      </button>\r\n    </div>\r\n  ),\r\n  context: 'DashboardPage'\r\n});\r\n"], "names": ["options", "arguments", "length", "undefined", "enableNotifications", "enableReporting", "onError", "showNotification", "useNotification", "errorState", "setErrorState", "useState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorType", "clearError", "useCallback", "handleApiErrorWithState", "context", "apiError", "handleApiError", "notification", "type", "title", "message", "Error", "reportError", "handleValidationErrorWithState", "field", "code", "validationError", "handleValidationError", "handleFormErrorWithState", "setFieldError", "handleFormError", "handleGeneralError", "errorObj", "String", "logError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "operation", "withFormError<PERSON>andling", "isApiError", "isValidationError", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "description", "actions", "breadcrumbs", "className", "testId", "_jsxs", "children", "_jsx", "Link", "to", "HomeIcon", "map", "item", "index", "ChevronRightIcon", "path", "label", "memo", "Card", "subtitle", "bodyClassName", "headerClassName", "footerClassName", "icon", "footer", "onClick", "hoverable", "noPadding", "bordered", "loading", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "DefaultError<PERSON><PERSON><PERSON>", "resetError", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "config", "fallback", "FallbackComponent", "WrappedComponent", "forwardRef", "props", "ref", "Error<PERSON>ou<PERSON><PERSON>", "window", "location", "reload", "handleError", "errorInfo", "displayName", "name", "componentStack", "errorBoundary", "formatDate", "dateString", "date", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "console", "formatCurrency", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "formatFileSize", "bytes", "i", "Math", "floor", "log", "parseFloat", "pow", "size", "variant", "color", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "borderTopColor", "borderRightColor", "backgroundColor", "UserIcon", "svgRef", "titleId", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "id", "strokeLinecap", "strokeLinejoin", "d", "ArrowDownTrayIcon", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "RadialLinearScale", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "defaultLineChartOptions", "responsive", "maintainAspectRatio", "plugins", "display", "font", "weight", "legend", "labels", "tooltip", "titleColor", "bodyColor", "padding", "displayColors", "scales", "x", "grid", "ticks", "y", "beginAtZero", "defaultPieChartOptions", "position", "destroy<PERSON>hart", "chartInstance", "destroy", "<PERSON><PERSON>", "disabled", "iconPosition", "fullWidth", "rounded", "href", "target", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "text", "link", "xs", "xl", "content", "_Fragment", "cx", "cy", "r", "CalendarIcon", "Modal", "isOpen", "onClose", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "backdropClassName", "modalRef", "useRef", "useEffect", "handleEscape", "e", "key", "document", "addEventListener", "body", "overflow", "removeEventListener", "current", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "full", "stopPropagation", "XMarkIcon", "createPortal", "DocumentChartBarIcon", "ArrowPathIcon", "getIconBackgroundClass", "includes", "value", "change", "isLoading", "isPositive", "iconElement", "colorMatch", "match", "colorClass", "SalesChart", "data", "onPeriodChange", "activePeriod", "setActivePeriod", "chartRef", "handlePeriodChange", "period", "chartData", "useMemo", "datasets", "borderColor", "tension", "pointBackgroundColor", "pointBorderColor", "pointBorderWidth", "pointRadius", "pointHoverRadius", "_defaultLineChartOpti", "_defaultLineChartOpti2", "callbacks", "parsed", "toLocaleString", "callback", "LoadingSpinner", "Line", "UserGrowthChart", "showCard", "users", "chartContent", "CategoryDistributionChart", "_data$datasets$", "warn", "chartOptions", "_defaultPieChartOptio", "hasValidData", "Pie", "_data$datasets$2", "_data$datasets$2$back", "_data$datasets$3", "_data$datasets$3$data", "order", "status", "getStatusColorClass", "ClockIcon", "CheckCircleIcon", "TruckIcon", "XCircleIcon", "getStatusIcon", "char<PERSON>t", "toUpperCase", "slice", "customer", "CurrencyDollarIcon", "orders", "onViewOrder", "navigate", "useNavigate", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "isModalOpen", "setIsModalOpen", "getStatusColor", "ROUTES", "ORDERS", "Array", "_", "scope", "navigateToOrderDetails", "orderId", "getOrderDetailsRoute", "handleViewOrder", "EyeIcon", "OrderDetailsModal", "dashboardApi", "getDashboardStats", "response", "apiClient", "get", "summary", "totalUsers", "backendData", "totalSuppliers", "totalOrders", "totalRevenue", "pendingVerifications", "activeUsers", "monthlyGrowth", "revenueData", "userGrowth", "categoryDistribution", "borderWidth", "recentOrders", "topSuppliers", "getSalesData", "params", "sales", "getUser<PERSON>rowth", "newUsers", "getCategoryDistribution", "colors", "category", "count", "transformCategoryDistribution", "salesData", "setSalesData", "setIsLoading", "setError", "fetchSalesData", "err", "userGrowthData", "setUserGrowthData", "fetchUserGrowthData", "categoryData", "setCategoryData", "fetchCategoryData", "useDashboard", "stats", "setStats", "showNotificationRef", "hasInitialFetched", "cacheRef", "CACHE_TTL", "isCache<PERSON><PERSON>d", "now", "timestamp", "fetchStats", "errorMessage", "fetchUserGrowth", "fetchCategoryDistribution", "catch", "DashboardPage", "isRefreshing", "setIsRefreshing", "dashboardData", "useErrorHandler", "result", "safeAsyncOperation", "Promise", "resolve", "reject", "setTimeout", "random", "timeout", "retries", "operationName", "StatCard", "UsersIcon", "abs", "ShoppingCartIcon", "revenue", "SalesChartContainer", "UserGrowthChartContainer", "RecentOrders", "orderNumber", "customerName", "CategoryDistributionChartContainer"], "sourceRoot": ""}