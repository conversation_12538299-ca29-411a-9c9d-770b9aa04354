{"version": 3, "file": "static/js/139.952ba1fe.chunk.js", "mappings": "yJAiCA,MAAMA,EAAgCC,IAmB/B,IAnBgC,SACrCC,EAAQ,QACRC,EAAU,UAAS,KACnBC,EAAO,KAAI,UACXC,EAAY,GAAE,QACdC,EAAO,SACPC,GAAW,EAAK,KAChBC,EAAO,SAAQ,KACfC,EAAI,aACJC,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBC,GAAU,EAAK,QACfC,GAAU,EAAK,KACfC,EAAI,OACJC,EAAM,IACNC,EAAG,MACHC,EAAK,UACLC,EAAS,OACTC,GACDlB,EACC,MAwBMmB,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTC,KAAM,2EACNC,KAAM,kFAiBWxB,WAdC,CAClByB,GAAI,oBACJC,GAAI,sBACJC,GAAI,oBACJC,GAAI,wBACJC,GAAI,qBAUU5B,WAPQG,EAAW,gCAAkC,yBAClDI,EAAY,SAAW,WACrBE,EAAU,eAAiB,qBAS5CR,QAGE4B,GACJC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAjC,SAAA,CACGU,IACCsB,EAAAA,EAAAA,MAAA,OACE7B,UAAU,+CACV+B,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMpC,SAAA,EAElBqC,EAAAA,EAAAA,KAAA,UACElC,UAAU,aACVmC,GAAG,KACHC,GAAG,KACHC,EAAE,KACFC,OAAO,eACPC,YAAY,OAEdL,EAAAA,EAAAA,KAAA,QACElC,UAAU,aACVgC,KAAK,eACLQ,EAAE,uHAKPpC,GAAyB,SAAjBC,IAA4BE,IACnC2B,EAAAA,EAAAA,KAAA,QAAMlC,UAAU,OAAMH,SAAEO,IAGzBP,EAEAO,GAAyB,UAAjBC,IACP6B,EAAAA,EAAAA,KAAA,QAAMlC,UAAU,OAAMH,SAAEO,OAM9B,OAAIK,GAEAyB,EAAAA,EAAAA,KAAA,KACEzB,KAAMA,EACNT,UAAWe,EACXL,OAAQA,EACRC,IAAKA,IAAmB,WAAXD,EAAsB,2BAAwB+B,GAC3DxC,QAASA,EACTW,MAAOA,EACP,aAAYC,EACZ,cAAaC,EAAOjB,SAEnB+B,KAOLM,EAAAA,EAAAA,KAAA,UACE/B,KAAMA,EACNH,UAAWe,EACXd,QAASA,EACTC,SAAUA,GAAYK,EACtBK,MAAOA,EACP,aAAYC,EACZ,cAAaC,EAAOjB,SAEnB+B,GACM,EAIb,GAAec,EAAAA,EAAAA,MAAK/C,E,iGCnJpB,MA2BA,EA3B+BgD,KAE3BT,EAAAA,EAAAA,KAAA,OAAKlC,UAAU,yDAAwDH,UACrEgC,EAAAA,EAAAA,MAAA,OAAK7B,UAAU,cAAaH,SAAA,EAC1BqC,EAAAA,EAAAA,KAAA,MAAIlC,UAAU,mCAAkCH,SAAC,SACjDgC,EAAAA,EAAAA,MAAA,OAAK7B,UAAU,OAAMH,SAAA,EACnBqC,EAAAA,EAAAA,KAAA,MAAIlC,UAAU,mCAAkCH,SAAC,oBACjDqC,EAAAA,EAAAA,KAAA,KAAGlC,UAAU,6BAA4BH,SAAC,sEAI5CqC,EAAAA,EAAAA,KAAA,OAAKlC,UAAU,OAAMH,UACnBqC,EAAAA,EAAAA,KAACvC,EAAAA,EAAM,CACLG,QAAQ,UACRC,KAAK,KACLK,MAAM8B,EAAAA,EAAAA,KAACU,EAAAA,EAAQ,CAAC5C,UAAU,YAC1BK,aAAa,OACbI,KAAMoC,EAAAA,EAAOC,UAAUjD,SACxB,4B", "sources": ["components/common/Button.tsx", "pages/NotFoundPage.tsx"], "sourcesContent": ["/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "/**\r\n * NotFoundPage Component\r\n * \r\n * The 404 page for the ConnectChain admin panel.\r\n */\r\n\r\nimport React from 'react';\r\n\r\nimport { HomeIcon } from '@heroicons/react/24/outline';\r\nimport Button from '../components/common/Button';\r\nimport { ROUTES } from '../constants/routes';\r\n\r\nconst NotFoundPage: React.FC = () => {\r\n  return (\r\n    <div className=\"min-h-[80vh] flex flex-col items-center justify-center\">\r\n      <div className=\"text-center\">\r\n        <h1 className=\"text-9xl font-bold text-gray-200\">404</h1>\r\n        <div className=\"mt-4\">\r\n          <h2 className=\"text-3xl font-bold text-gray-800\">Page Not Found</h2>\r\n          <p className=\"mt-2 text-lg text-gray-600\">\r\n            The page you are looking for doesn't exist or has been moved.\r\n          </p>\r\n        </div>\r\n        <div className=\"mt-8\">\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"lg\"\r\n            icon={<HomeIcon className=\"h-5 w-5\" />}\r\n            iconPosition=\"left\"\r\n            href={ROUTES.DASHBOARD}\r\n          >\r\n            Back to Dashboard\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotFoundPage;\r\n"], "names": ["<PERSON><PERSON>", "_ref", "children", "variant", "size", "className", "onClick", "disabled", "type", "icon", "iconPosition", "fullWidth", "loading", "rounded", "href", "target", "rel", "title", "aria<PERSON><PERSON><PERSON>", "testId", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "text", "link", "xs", "sm", "md", "lg", "xl", "content", "_jsxs", "_Fragment", "xmlns", "fill", "viewBox", "_jsx", "cx", "cy", "r", "stroke", "strokeWidth", "d", "undefined", "memo", "NotFoundPage", "HomeIcon", "ROUTES", "DASHBOARD"], "sourceRoot": ""}