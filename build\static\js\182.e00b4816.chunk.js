/*! For license information please see 182.e00b4816.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[182],{2683:(t,e,i)=>{i.d(e,{A:()=>o});var s=i(5043);function n(t,e){let{title:i,titleId:n,...o}=t;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=s.forwardRef(n)},5889:(t,e,i)=>{i.d(e,{A:()=>o});var s=i(5043);function n(t,e){let{title:i,titleId:n,...o}=t;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const o=s.forwardRef(n)},6058:(t,e,i)=>{i.d(e,{Fq:()=>g,N1:()=>f});var s=i(5043),n=i(7304);const o="label";function r(t,e){"function"===typeof t?t(e):t&&(t.current=e)}function a(t,e){t.labels=e}function h(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o;const s=[];t.datasets=e.map((e=>{const n=t.datasets.find((t=>t[i]===e[i]));return n&&e.data&&!s.includes(n)?(s.push(n),Object.assign(n,e),n):{...e}}))}function l(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;const i={labels:[],datasets:[]};return a(i,t.labels),h(i,t.datasets,e),i}function c(t,e){const{height:i=150,width:o=300,redraw:c=!1,datasetIdKey:d,type:u,data:f,options:g,plugins:p=[],fallbackContent:m,updateMode:x,...b}=t,y=(0,s.useRef)(null),_=(0,s.useRef)(null),v=()=>{y.current&&(_.current=new n.t1(y.current,{type:u,data:l(f,d),options:g&&{...g},plugins:p}),r(e,_.current))},w=()=>{r(e,null),_.current&&(_.current.destroy(),_.current=null)};return(0,s.useEffect)((()=>{!c&&_.current&&g&&function(t,e){const i=t.options;i&&e&&Object.assign(i,e)}(_.current,g)}),[c,g]),(0,s.useEffect)((()=>{!c&&_.current&&a(_.current.config.data,f.labels)}),[c,f.labels]),(0,s.useEffect)((()=>{!c&&_.current&&f.datasets&&h(_.current.config.data,f.datasets,d)}),[c,f.datasets]),(0,s.useEffect)((()=>{_.current&&(c?(w(),setTimeout(v)):_.current.update(x))}),[c,g,f.labels,f.datasets,x]),(0,s.useEffect)((()=>{_.current&&(w(),setTimeout(v))}),[u]),(0,s.useEffect)((()=>(v(),()=>w())),[]),s.createElement("canvas",{ref:y,role:"img",height:i,width:o,...b},m)}const d=(0,s.forwardRef)(c);function u(t,e){return n.t1.register(e),(0,s.forwardRef)(((e,i)=>s.createElement(d,{...e,ref:i,type:t})))}const f=u("line",n.ZT),g=u("pie",n.P$)},7012:(t,e,i)=>{i.d(e,{A:()=>o});var s=i(5043);function n(t,e){let{title:i,titleId:n,...o}=t;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=s.forwardRef(n)},7098:(t,e,i)=>{i.d(e,{A:()=>o});var s=i(5043);function n(t,e){let{title:i,titleId:n,...o}=t;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=s.forwardRef(n)},7304:(t,e,i)=>{function s(t){return t+.5|0}i.d(e,{Bs:()=>Sn,E8:()=>Nn,PP:()=>To,t1:()=>yn,dN:()=>ro,s$:()=>co,ZT:()=>$i,No:()=>In,kc:()=>Eo,P$:()=>Yi,FN:()=>zn,pr:()=>$o,hE:()=>fo,m_:()=>Do});const n=(t,e,i)=>Math.max(Math.min(t,i),e);function o(t){return n(s(2.55*t),0,255)}function r(t){return n(s(255*t),0,255)}function a(t){return n(s(t/2.55)/100,0,1)}function h(t){return n(s(100*t),0,100)}const l={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},c=[..."0123456789ABCDEF"],d=t=>c[15&t],u=t=>c[(240&t)>>4]+c[15&t],f=t=>(240&t)>>4===(15&t);function g(t){var e=(t=>f(t.r)&&f(t.g)&&f(t.b)&&f(t.a))(t)?d:u;return t?"#"+e(t.r)+e(t.g)+e(t.b)+((t,e)=>t<255?e(t):"")(t.a,e):void 0}const p=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function m(t,e,i){const s=e*Math.min(i,1-i),n=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+t/30)%12;return i-s*Math.max(Math.min(n-3,9-n,1),-1)};return[n(0),n(8),n(4)]}function x(t,e,i){const s=function(s){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(s+t/60)%6;return i-i*e*Math.max(Math.min(n,4-n,1),0)};return[s(5),s(3),s(1)]}function b(t,e,i){const s=m(t,1,.5);let n;for(e+i>1&&(n=1/(e+i),e*=n,i*=n),n=0;n<3;n++)s[n]*=1-e-i,s[n]+=e;return s}function y(t){const e=t.r/255,i=t.g/255,s=t.b/255,n=Math.max(e,i,s),o=Math.min(e,i,s),r=(n+o)/2;let a,h,l;return n!==o&&(l=n-o,h=r>.5?l/(2-n-o):l/(n+o),a=function(t,e,i,s,n){return t===n?(e-i)/s+(e<i?6:0):e===n?(i-t)/s+2:(t-e)/s+4}(e,i,s,l,n),a=60*a+.5),[0|a,h||0,r]}function _(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(r)}function v(t,e,i){return _(m,t,e,i)}function w(t){return(t%360+360)%360}function M(t){const e=p.exec(t);let i,s=255;if(!e)return;e[5]!==i&&(s=e[6]?o(+e[5]):r(+e[5]));const n=w(+e[2]),a=+e[3]/100,h=+e[4]/100;return i="hwb"===e[1]?function(t,e,i){return _(b,t,e,i)}(n,a,h):"hsv"===e[1]?function(t,e,i){return _(x,t,e,i)}(n,a,h):v(n,a,h),{r:i[0],g:i[1],b:i[2],a:s}}const k={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},S={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let P;function C(t){P||(P=function(){const t={},e=Object.keys(S),i=Object.keys(k);let s,n,o,r,a;for(s=0;s<e.length;s++){for(r=a=e[s],n=0;n<i.length;n++)o=i[n],a=a.replace(o,k[o]);o=parseInt(S[r],16),t[a]=[o>>16&255,o>>8&255,255&o]}return t}(),P.transparent=[0,0,0,0]);const e=P[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}const D=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;const A=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,O=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function T(t,e,i){if(t){let s=y(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),s=v(s),t.r=s[0],t.g=s[1],t.b=s[2]}}function L(t,e){return t?Object.assign(e||{},t):t}function R(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=r(t[3]))):(e=L(t,{r:0,g:0,b:0,a:1})).a=r(e.a),e}function I(t){return"r"===t.charAt(0)?function(t){const e=D.exec(t);let i,s,r,a=255;if(e){if(e[7]!==i){const t=+e[7];a=e[8]?o(t):n(255*t,0,255)}return i=+e[1],s=+e[3],r=+e[5],i=255&(e[2]?o(i):n(i,0,255)),s=255&(e[4]?o(s):n(s,0,255)),r=255&(e[6]?o(r):n(r,0,255)),{r:i,g:s,b:r,a:a}}}(t):M(t)}class E{constructor(t){if(t instanceof E)return t;const e=typeof t;let i;"object"===e?i=R(t):"string"===e&&(i=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*l[t[1]],g:255&17*l[t[2]],b:255&17*l[t[3]],a:5===i?17*l[t[4]]:255}:7!==i&&9!==i||(e={r:l[t[1]]<<4|l[t[2]],g:l[t[3]]<<4|l[t[4]],b:l[t[5]]<<4|l[t[6]],a:9===i?l[t[7]]<<4|l[t[8]]:255})),e}(t)||C(t)||I(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=L(this._rgb);return t&&(t.a=a(t.a)),t}set rgb(t){this._rgb=R(t)}rgbString(){return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${a(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0;var t}hexString(){return this._valid?g(this._rgb):void 0}hslString(){return this._valid?function(t){if(!t)return;const e=y(t),i=e[0],s=h(e[1]),n=h(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${n}%, ${a(t.a)})`:`hsl(${i}, ${s}%, ${n}%)`}(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,s=t.rgb;let n;const o=e===n?.5:e,r=2*o-1,a=i.a-s.a,h=((r*a===-1?r:(r+a)/(1+r*a))+1)/2;n=1-h,i.r=255&h*i.r+n*s.r+.5,i.g=255&h*i.g+n*s.g+.5,i.b=255&h*i.b+n*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){const s=O(a(t.r)),n=O(a(t.g)),o=O(a(t.b));return{r:r(A(s+i*(O(a(e.r))-s))),g:r(A(n+i*(O(a(e.g))-n))),b:r(A(o+i*(O(a(e.b))-o))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new E(this.rgb)}alpha(t){return this._rgb.a=r(t),this}clearer(t){return this._rgb.a*=1-t,this}greyscale(){const t=this._rgb,e=s(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){return this._rgb.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return T(this._rgb,2,t),this}darken(t){return T(this._rgb,2,-t),this}saturate(t){return T(this._rgb,1,t),this}desaturate(t){return T(this._rgb,1,-t),this}rotate(t){return function(t,e){var i=y(t);i[0]=w(i[0]+e),i=v(i),t.r=i[0],t.g=i[1],t.b=i[2]}(this._rgb,t),this}}function z(){}const F=(()=>{let t=0;return()=>t++})();function W(t){return null===t||void 0===t}function B(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function H(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function V(t){return("number"===typeof t||t instanceof Number)&&isFinite(+t)}function j(t,e){return V(t)?t:e}function N(t,e){return"undefined"===typeof t?e:t}const $=(t,e)=>"string"===typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function Y(t,e,i){if(t&&"function"===typeof t.call)return t.apply(i,e)}function U(t,e,i,s){let n,o,r;if(B(t))if(o=t.length,s)for(n=o-1;n>=0;n--)e.call(i,t[n],n);else for(n=0;n<o;n++)e.call(i,t[n],n);else if(H(t))for(r=Object.keys(t),o=r.length,n=0;n<o;n++)e.call(i,t[r[n]],r[n])}function X(t,e){let i,s,n,o;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(n=t[i],o=e[i],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function q(t){if(B(t))return t.map(q);if(H(t)){const e=Object.create(null),i=Object.keys(t),s=i.length;let n=0;for(;n<s;++n)e[i[n]]=q(t[i[n]]);return e}return t}function K(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function Z(t,e,i,s){if(!K(t))return;const n=e[t],o=i[t];H(n)&&H(o)?G(n,o,s):e[t]=q(o)}function G(t,e,i){const s=B(e)?e:[e],n=s.length;if(!H(t))return t;const o=(i=i||{}).merger||Z;let r;for(let a=0;a<n;++a){if(r=s[a],!H(r))continue;const e=Object.keys(r);for(let s=0,n=e.length;s<n;++s)o(e[s],t,r,i)}return t}function J(t,e){return G(t,e,{merger:Q})}function Q(t,e,i){if(!K(t))return;const s=e[t],n=i[t];H(s)&&H(n)?J(s,n):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=q(n))}const tt={"":t=>t,x:t=>t.x,y:t=>t.y};function et(t,e){const i=tt[e]||(tt[e]=function(t){const e=function(t){const e=t.split("."),i=[];let s="";for(const n of e)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(const i of e){if(""===i)break;t=t&&t[i]}return t}}(e));return i(t)}function it(t){return t.charAt(0).toUpperCase()+t.slice(1)}const st=t=>"undefined"!==typeof t,nt=t=>"function"===typeof t,ot=(t,e)=>{if(t.size!==e.size)return!1;for(const i of t)if(!e.has(i))return!1;return!0};const rt=Math.PI,at=2*rt,ht=at+rt,lt=Number.POSITIVE_INFINITY,ct=rt/180,dt=rt/2,ut=rt/4,ft=2*rt/3,gt=Math.log10,pt=Math.sign;function mt(t,e,i){return Math.abs(t-e)<i}function xt(t){const e=Math.round(t);t=mt(t,e,t/1e3)?e:t;const i=Math.pow(10,Math.floor(gt(t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function bt(t){return!function(t){return"symbol"===typeof t||"object"===typeof t&&null!==t&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function yt(t,e,i){let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s][i],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function _t(t){return t*(rt/180)}function vt(t){return t*(180/rt)}function wt(t){if(!V(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function Mt(t,e){const i=e.x-t.x,s=e.y-t.y,n=Math.sqrt(i*i+s*s);let o=Math.atan2(s,i);return o<-.5*rt&&(o+=at),{angle:o,distance:n}}function kt(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function St(t,e){return(t-e+ht)%at-rt}function Pt(t){return(t%at+at)%at}function Ct(t,e,i,s){const n=Pt(t),o=Pt(e),r=Pt(i),a=Pt(o-n),h=Pt(r-n),l=Pt(n-o),c=Pt(n-r);return n===o||n===r||s&&o===r||a>h&&l<c}function Dt(t,e,i){return Math.max(e,Math.min(i,t))}function At(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e-6;return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function Ot(t,e,i){i=i||(i=>t[i]<e);let s,n=t.length-1,o=0;for(;n-o>1;)s=o+n>>1,i(s)?o=s:n=s;return{lo:o,hi:n}}const Tt=(t,e,i,s)=>Ot(t,i,s?s=>{const n=t[s][e];return n<i||n===i&&t[s+1][e]===i}:s=>t[s][e]<i),Lt=(t,e,i)=>Ot(t,i,(s=>t[s][e]>=i));const Rt=["push","pop","shift","splice","unshift"];function It(t,e){const i=t._chartjs;if(!i)return;const s=i.listeners,n=s.indexOf(e);-1!==n&&s.splice(n,1),s.length>0||(Rt.forEach((e=>{delete t[e]})),delete t._chartjs)}function Et(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const zt="undefined"===typeof window?function(t){return t()}:window.requestAnimationFrame;function Ft(t,e){let i=[],s=!1;return function(){for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];i=o,s||(s=!0,zt.call(window,(()=>{s=!1,t.apply(e,i)})))}}const Wt=t=>"start"===t?"left":"end"===t?"right":"center",Bt=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2;function Ht(t,e,i){const s=e.length;let n=0,o=s;if(t._sorted){const{iScale:r,vScale:a,_parsed:h}=t,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=r.axis,{min:d,max:u,minDefined:f,maxDefined:g}=r.getUserBounds();if(f){if(n=Math.min(Tt(h,c,d).lo,i?s:Tt(e,c,r.getPixelForValue(d)).lo),l){const t=h.slice(0,n+1).reverse().findIndex((t=>!W(t[a.axis])));n-=Math.max(0,t)}n=Dt(n,0,s-1)}if(g){let t=Math.max(Tt(h,r.axis,u,!0).hi+1,i?0:Tt(e,c,r.getPixelForValue(u),!0).hi+1);if(l){const e=h.slice(t-1).findIndex((t=>!W(t[a.axis])));t+=Math.max(0,e)}o=Dt(t,n,s)-n}else o=s-n}return{start:n,count:o}}function Vt(t){const{xScale:e,yScale:i,_scaleRanges:s}=t,n={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=n,!0;const o=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,n),o}const jt=t=>0===t||1===t,Nt=(t,e,i)=>-Math.pow(2,10*(t-=1))*Math.sin((t-e)*at/i),$t=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*at/i)+1,Yt={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*dt),easeOutSine:t=>Math.sin(t*dt),easeInOutSine:t=>-.5*(Math.cos(rt*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>jt(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>jt(t)?t:Nt(t,.075,.3),easeOutElastic:t=>jt(t)?t:$t(t,.075,.3),easeInOutElastic(t){const e=.1125;return jt(t)?t:t<.5?.5*Nt(2*t,e,.45):.5+.5*$t(2*t-1,e,.45)},easeInBack(t){const e=1.70158;return t*t*((e+1)*t-e)},easeOutBack(t){const e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-Yt.easeOutBounce(1-t),easeOutBounce(t){const e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375},easeInOutBounce:t=>t<.5?.5*Yt.easeInBounce(2*t):.5*Yt.easeOutBounce(2*t-1)+.5};function Ut(t){if(t&&"object"===typeof t){const e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function Xt(t){return Ut(t)?t:new E(t)}function qt(t){return Ut(t)?t:new E(t).saturate(.5).darken(.1).hexString()}const Kt=["x","y","borderWidth","radius","tension"],Zt=["color","borderColor","backgroundColor"];const Gt=new Map;function Jt(t,e,i){return function(t,e){e=e||{};const i=t+JSON.stringify(e);let s=Gt.get(i);return s||(s=new Intl.NumberFormat(t,e),Gt.set(i,s)),s}(e,i).format(t)}const Qt={values:t=>B(t)?t:""+t,numeric(t,e,i){if(0===t)return"0";const s=this.chart.options.locale;let n,o=t;if(i.length>1){const e=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(e<1e-4||e>1e15)&&(n="scientific"),o=function(t,e){let i=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;Math.abs(i)>=1&&t!==Math.floor(t)&&(i=t-Math.floor(t));return i}(t,i)}const r=gt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),h={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(h,this.options.ticks.format),Jt(t,s,h)},logarithmic(t,e,i){if(0===t)return"0";const s=i[e].significand||t/Math.pow(10,Math.floor(gt(t)));return[1,2,3,5,10,15].includes(s)||e>.8*i.length?Qt.numeric.call(this,t,e,i):""}};var te={formatters:Qt};const ee=Object.create(null),ie=Object.create(null);function se(t,e){if(!e)return t;const i=e.split(".");for(let s=0,n=i.length;s<n;++s){const e=i[s];t=t[e]||(t[e]=Object.create(null))}return t}function ne(t,e,i){return"string"===typeof e?G(se(t,e),i):G(se(t,""),e)}class oe{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>qt(e.backgroundColor),this.hoverBorderColor=(t,e)=>qt(e.borderColor),this.hoverColor=(t,e)=>qt(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return ne(this,t,e)}get(t){return se(this,t)}describe(t,e){return ne(ie,t,e)}override(t,e){return ne(ee,t,e)}route(t,e,i,s){const n=se(this,t),o=se(this,i),r="_"+e;Object.defineProperties(n,{[r]:{value:n[e],writable:!0},[e]:{enumerable:!0,get(){const t=this[r],e=o[s];return H(t)?Object.assign({},e,t):N(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach((t=>t(this)))}}var re=new oe({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:Zt},numbers:{type:"number",properties:Kt}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:te.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function ae(t,e,i,s,n){let o=e[n];return o||(o=e[n]=t.measureText(n).width,i.push(n)),o>s&&(s=o),s}function he(t,e,i,s){let n=(s=s||{}).data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(n=s.data={},o=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let r=0;const a=i.length;let h,l,c,d,u;for(h=0;h<a;h++)if(d=i[h],void 0===d||null===d||B(d)){if(B(d))for(l=0,c=d.length;l<c;l++)u=d[l],void 0===u||null===u||B(u)||(r=ae(t,n,o,r,u))}else r=ae(t,n,o,r,d);t.restore();const f=o.length/2;if(f>i.length){for(h=0;h<f;h++)delete n[o[h]];o.splice(0,f)}return r}function le(t,e,i){const s=t.currentDevicePixelRatio,n=0!==i?Math.max(i/2,.5):0;return Math.round((e-n)*s)/s+n}function ce(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function de(t,e,i,s){ue(t,e,i,s,null)}function ue(t,e,i,s,n){let o,r,a,h,l,c,d,u;const f=e.pointStyle,g=e.rotation,p=e.radius;let m=(g||0)*ct;if(f&&"object"===typeof f&&(o=f.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void t.restore();if(!(isNaN(p)||p<=0)){switch(t.beginPath(),f){default:n?t.ellipse(i,s,n/2,p,0,0,at):t.arc(i,s,p,0,at),t.closePath();break;case"triangle":c=n?n/2:p,t.moveTo(i+Math.sin(m)*c,s-Math.cos(m)*p),m+=ft,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*p),m+=ft,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":l=.516*p,h=p-l,r=Math.cos(m+ut)*h,d=Math.cos(m+ut)*(n?n/2-l:h),a=Math.sin(m+ut)*h,u=Math.sin(m+ut)*(n?n/2-l:h),t.arc(i-d,s-a,l,m-rt,m-dt),t.arc(i+u,s-r,l,m-dt,m),t.arc(i+d,s+a,l,m,m+dt),t.arc(i-u,s+r,l,m+dt,m+rt),t.closePath();break;case"rect":if(!g){h=Math.SQRT1_2*p,c=n?n/2:h,t.rect(i-c,s-h,2*c,2*h);break}m+=ut;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+u,s-r),t.lineTo(i+d,s+a),t.lineTo(i-u,s+r),t.closePath();break;case"crossRot":m+=ut;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r),m+=ut,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,t.moveTo(i-r,s-a),t.lineTo(i+r,s+a);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function fe(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function ge(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function pe(t){t.restore()}function me(t,e,i,s,n){if(!e)return t.lineTo(i.x,i.y);if("middle"===n){const s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===n!==!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function xe(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function be(t,e,i,s,n){if(n.strikethrough||n.underline){const o=t.measureText(s),r=e-o.actualBoundingBoxLeft,a=e+o.actualBoundingBoxRight,h=i-o.actualBoundingBoxAscent,l=i+o.actualBoundingBoxDescent,c=n.strikethrough?(h+l)/2:l;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=n.decorationWidth||2,t.moveTo(r,c),t.lineTo(a,c),t.stroke()}}function ye(t,e){const i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}function _e(t,e,i,s,n){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};const r=B(e)?e:[e],a=o.strokeWidth>0&&""!==o.strokeColor;let h,l;for(t.save(),t.font=n.string,function(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),W(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}(t,o),h=0;h<r.length;++h)l=r[h],o.backdrop&&ye(t,o.backdrop),a&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),W(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(l,i,s,o.maxWidth)),t.fillText(l,i,s,o.maxWidth),be(t,i,s,l,o),s+=Number(n.lineHeight);t.restore()}function ve(t,e){const{x:i,y:s,w:n,h:o,radius:r}=e;t.arc(i+r.topLeft,s+r.topLeft,r.topLeft,1.5*rt,rt,!0),t.lineTo(i,s+o-r.bottomLeft),t.arc(i+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,rt,dt,!0),t.lineTo(i+n-r.bottomRight,s+o),t.arc(i+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,dt,0,!0),t.lineTo(i+n,s+r.topRight),t.arc(i+n-r.topRight,s+r.topRight,r.topRight,0,-dt,!0),t.lineTo(i+r.topLeft,s)}const we=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Me=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function ke(t,e){const i=(""+t).match(we);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}function Se(t,e){const i={},s=H(e),n=s?Object.keys(e):e,o=H(t)?s?i=>N(t[i],t[e[i]]):e=>t[e]:()=>t;for(const r of n)i[r]=+o(r)||0;return i}function Pe(t){return Se(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Ce(t){return Se(t,["topLeft","topRight","bottomLeft","bottomRight"])}function De(t){const e=Pe(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Ae(t,e){t=t||{},e=e||re.font;let i=N(t.size,e.size);"string"===typeof i&&(i=parseInt(i,10));let s=N(t.style,e.style);s&&!(""+s).match(Me)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:N(t.family,e.family),lineHeight:ke(N(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:N(t.weight,e.weight),string:""};return n.string=function(t){return!t||W(t.size)||W(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}(n),n}function Oe(t,e,i,s){let n,o,r,a=!0;for(n=0,o=t.length;n<o;++n)if(r=t[n],void 0!==r&&(void 0!==e&&"function"===typeof r&&(r=r(e),a=!1),void 0!==i&&B(r)&&(r=r[i%r.length],a=!1),void 0!==r))return s&&!a&&(s.cacheable=!1),r}function Te(t,e){return Object.assign(Object.create(t),e)}function Le(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[""],i=arguments.length>3?arguments[3]:void 0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>t[0];const n=(arguments.length>2?arguments[2]:void 0)||t;"undefined"===typeof i&&(i=Ne("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:i,_getTarget:s,override:s=>Le([s,...t],e,n,i)};return new Proxy(o,{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>Fe(i,s,(()=>function(t,e,i,s){let n;for(const o of e)if(n=Ne(Ee(o,t),i),"undefined"!==typeof n)return ze(t,n)?Ve(i,s,t,n):n}(s,e,t,i))),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>$e(t).includes(e),ownKeys:t=>$e(t),set(t,e,i){const n=t._storage||(t._storage=s());return t[e]=n[e]=i,delete t._keys,!0}})}function Re(t,e,i,s){const n={_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:Ie(t,s),setContext:e=>Re(t,e,i,s),override:n=>Re(t.override(n),e,i,s)};return new Proxy(n,{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>Fe(t,e,(()=>function(t,e,i){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=t;let a=s[e];nt(a)&&r.isScriptable(e)&&(a=function(t,e,i,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=i;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let h=e(o,r||s);a.delete(t),ze(t,h)&&(h=Ve(n._scopes,n,t,h));return h}(e,a,t,i));B(a)&&a.length&&(a=function(t,e,i,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=i;if("undefined"!==typeof o.index&&s(t))return e[o.index%e.length];if(H(e[0])){const i=e,s=n._scopes.filter((t=>t!==i));e=[];for(const h of i){const i=Ve(s,n,t,h);e.push(Re(i,o,r&&r[t],a))}}return e}(e,a,t,r.isIndexable));ze(e,a)&&(a=Re(a,n,o&&o[e],r));return a}(t,e,i))),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function Ie(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{scriptable:!0,indexable:!0};const{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:n=e.allKeys}=t;return{allKeys:n,scriptable:i,indexable:s,isScriptable:nt(i)?i:()=>i,isIndexable:nt(s)?s:()=>s}}const Ee=(t,e)=>t?t+it(e):e,ze=(t,e)=>H(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function Fe(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];const s=i();return t[e]=s,s}function We(t,e,i){return nt(t)?t(e,i):t}const Be=(t,e)=>!0===t?e:"string"===typeof t?et(e,t):void 0;function He(t,e,i,s,n){for(const o of e){const e=Be(i,o);if(e){t.add(e);const o=We(e._fallback,i,n);if("undefined"!==typeof o&&o!==i&&o!==s)return o}else if(!1===e&&"undefined"!==typeof s&&i!==s)return null}return!1}function Ve(t,e,i,s){const n=e._rootScopes,o=We(e._fallback,i,s),r=[...t,...n],a=new Set;a.add(s);let h=je(a,r,i,o||i,s);return null!==h&&(("undefined"===typeof o||o===i||(h=je(a,r,o,h,s),null!==h))&&Le(Array.from(a),[""],n,o,(()=>function(t,e,i){const s=t._getTarget();e in s||(s[e]={});const n=s[e];if(B(n)&&H(i))return i;return n||{}}(e,i,s))))}function je(t,e,i,s,n){for(;i;)i=He(t,e,i,s,n);return i}function Ne(t,e){for(const i of e){if(!i)continue;const e=i[t];if("undefined"!==typeof e)return e}}function $e(t){let e=t._keys;return e||(e=t._keys=function(t){const e=new Set;for(const i of t)for(const t of Object.keys(i).filter((t=>!t.startsWith("_"))))e.add(t);return Array.from(e)}(t._scopes)),e}const Ye=Number.EPSILON||1e-14,Ue=(t,e)=>e<t.length&&!t[e].skip&&t[e],Xe=t=>"x"===t?"y":"x";function qe(t,e,i,s){const n=t.skip?e:t,o=e,r=i.skip?e:i,a=kt(o,n),h=kt(r,o);let l=a/(a+h),c=h/(a+h);l=isNaN(l)?0:l,c=isNaN(c)?0:c;const d=s*l,u=s*c;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function Ke(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"x";const i=Xe(e),s=t.length,n=Array(s).fill(0),o=Array(s);let r,a,h,l=Ue(t,0);for(r=0;r<s;++r)if(a=h,h=l,l=Ue(t,r+1),h){if(l){const t=l[e]-h[e];n[r]=0!==t?(l[i]-h[i])/t:0}o[r]=a?l?pt(n[r-1])!==pt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}!function(t,e,i){const s=t.length;let n,o,r,a,h,l=Ue(t,0);for(let c=0;c<s-1;++c)h=l,l=Ue(t,c+1),h&&l&&(mt(e[c],0,Ye)?i[c]=i[c+1]=0:(n=i[c]/e[c],o=i[c+1]/e[c],a=Math.pow(n,2)+Math.pow(o,2),a<=9||(r=3/Math.sqrt(a),i[c]=n*r*e[c],i[c+1]=o*r*e[c])))}(t,n,o),function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"x";const s=Xe(i),n=t.length;let o,r,a,h=Ue(t,0);for(let l=0;l<n;++l){if(r=a,a=h,h=Ue(t,l+1),!a)continue;const n=a[i],c=a[s];r&&(o=(n-r[i])/3,a[`cp1${i}`]=n-o,a[`cp1${s}`]=c-o*e[l]),h&&(o=(h[i]-n)/3,a[`cp2${i}`]=n+o,a[`cp2${s}`]=c+o*e[l])}}(t,o,e)}function Ze(t,e,i){return Math.max(Math.min(t,i),e)}function Ge(t,e,i,s,n){let o,r,a,h;if(e.spanGaps&&(t=t.filter((t=>!t.skip))),"monotone"===e.cubicInterpolationMode)Ke(t,n);else{let i=s?t[t.length-1]:t[0];for(o=0,r=t.length;o<r;++o)a=t[o],h=qe(i,a,t[Math.min(o+1,r-(s?0:1))%r],e.tension),a.cp1x=h.previous.x,a.cp1y=h.previous.y,a.cp2x=h.next.x,a.cp2y=h.next.y,i=a}e.capBezierPoints&&function(t,e){let i,s,n,o,r,a=fe(t[0],e);for(i=0,s=t.length;i<s;++i)r=o,o=a,a=i<s-1&&fe(t[i+1],e),o&&(n=t[i],r&&(n.cp1x=Ze(n.cp1x,e.left,e.right),n.cp1y=Ze(n.cp1y,e.top,e.bottom)),a&&(n.cp2x=Ze(n.cp2x,e.left,e.right),n.cp2y=Ze(n.cp2y,e.top,e.bottom)))}(t,i)}function Je(){return"undefined"!==typeof window&&"undefined"!==typeof document}function Qe(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function ti(t,e,i){let s;return"string"===typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}const ei=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);const ii=["top","right","bottom","left"];function si(t,e,i){const s={};i=i?"-"+i:"";for(let n=0;n<4;n++){const o=ii[n];s[o]=parseFloat(t[e+"-"+o+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}function ni(t,e){if("native"in t)return t;const{canvas:i,currentDevicePixelRatio:s}=e,n=ei(i),o="border-box"===n.boxSizing,r=si(n,"padding"),a=si(n,"border","width"),{x:h,y:l,box:c}=function(t,e){const i=t.touches,s=i&&i.length?i[0]:t,{offsetX:n,offsetY:o}=s;let r,a,h=!1;if(((t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot))(n,o,t.target))r=n,a=o;else{const t=e.getBoundingClientRect();r=s.clientX-t.left,a=s.clientY-t.top,h=!0}return{x:r,y:a,box:h}}(t,i),d=r.left+(c&&a.left),u=r.top+(c&&a.top);let{width:f,height:g}=e;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((h-d)/f*i.width/s),y:Math.round((l-u)/g*i.height/s)}}const oi=t=>Math.round(10*t)/10;function ri(t,e,i,s){const n=ei(t),o=si(n,"margin"),r=ti(n.maxWidth,t,"clientWidth")||lt,a=ti(n.maxHeight,t,"clientHeight")||lt,h=function(t,e,i){let s,n;if(void 0===e||void 0===i){const o=t&&Qe(t);if(o){const t=o.getBoundingClientRect(),r=ei(o),a=si(r,"border","width"),h=si(r,"padding");e=t.width-h.width-a.width,i=t.height-h.height-a.height,s=ti(r.maxWidth,o,"clientWidth"),n=ti(r.maxHeight,o,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||lt,maxHeight:n||lt}}(t,e,i);let{width:l,height:c}=h;if("content-box"===n.boxSizing){const t=si(n,"border","width"),e=si(n,"padding");l-=e.width+t.width,c-=e.height+t.height}l=Math.max(0,l-o.width),c=Math.max(0,s?l/s:c-o.height),l=oi(Math.min(l,r,h.maxWidth)),c=oi(Math.min(c,a,h.maxHeight)),l&&!c&&(c=oi(l/2));return(void 0!==e||void 0!==i)&&s&&h.height&&c>h.height&&(c=h.height,l=oi(Math.floor(c*s))),{width:l,height:c}}function ai(t,e,i){const s=e||1,n=Math.floor(t.height*s),o=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||r.height!==n||r.width!==o)&&(t.currentDevicePixelRatio=s,r.height=n,r.width=o,t.ctx.setTransform(s,0,0,s,0,0),!0)}const hi=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};Je()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(e){}return t}();function li(t,e){const i=function(t,e){return ei(t).getPropertyValue(e)}(t,e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function ci(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function di(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function ui(t,e,i,s){const n={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},r=ci(t,n,i),a=ci(n,o,i),h=ci(o,e,i),l=ci(r,a,i),c=ci(a,h,i);return ci(l,c,i)}function fi(t,e,i){return t?function(t,e){return{x:i=>t+t+e-i,setWidth(t){e=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}}(e,i):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function gi(t,e){let i,s;"ltr"!==e&&"rtl"!==e||(i=t.canvas.style,s=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function pi(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function mi(t){return"angle"===t?{between:Ct,compare:St,normalize:Pt}:{between:At,compare:(t,e)=>t-e,normalize:t=>t}}function xi(t){let{start:e,end:i,count:s,loop:n,style:o}=t;return{start:e%s,end:i%s,loop:n&&(i-e+1)%s===0,style:o}}function bi(t,e,i){if(!i)return[t];const{property:s,start:n,end:o}=i,r=e.length,{compare:a,between:h,normalize:l}=mi(s),{start:c,end:d,loop:u,style:f}=function(t,e,i){const{property:s,start:n,end:o}=i,{between:r,normalize:a}=mi(s),h=e.length;let l,c,{start:d,end:u,loop:f}=t;if(f){for(d+=h,u+=h,l=0,c=h;l<c&&r(a(e[d%h][s]),n,o);++l)d--,u--;d%=h,u%=h}return u<d&&(u+=h),{start:d,end:u,loop:f,style:t.style}}(t,e,i),g=[];let p,m,x,b=!1,y=null;const _=()=>b||h(n,x,p)&&0!==a(n,x),v=()=>!b||0===a(o,p)||h(o,x,p);for(let w=c,M=c;w<=d;++w)m=e[w%r],m.skip||(p=l(m[s]),p!==x&&(b=h(p,n,o),null===y&&_()&&(y=0===a(p,n)?w:M),null!==y&&v()&&(g.push(xi({start:y,end:w,loop:u,count:r,style:f})),y=null),M=w,x=p));return null!==y&&g.push(xi({start:y,end:d,loop:u,count:r,style:f})),g}function yi(t,e){const i=[],s=t.segments;for(let n=0;n<s.length;n++){const o=bi(s[n],t.points,e);o.length&&i.push(...o)}return i}function _i(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){const n=t._chart.getContext(),o=vi(t.options),{_datasetIndex:r,options:{spanGaps:a}}=t,h=i.length,l=[];let c=o,d=e[0].start,u=d;function f(t,e,s,n){const o=a?-1:1;if(t!==e){for(t+=h;i[t%h].skip;)t-=o;for(;i[e%h].skip;)e+=o;t%h!==e%h&&(l.push({start:t%h,end:e%h,loop:s,style:n}),c=n,d=e%h)}}for(const g of e){d=a?d:g.start;let t,e=i[d%h];for(u=d+1;u<=g.end;u++){const o=i[u%h];t=vi(s.setContext(Te(n,{type:"segment",p0:e,p1:o,p0DataIndex:(u-1)%h,p1DataIndex:u%h,datasetIndex:r}))),wi(t,c)&&f(d,u-1,g.loop,c),e=o,c=t}d<u-1&&f(d,u-1,g.loop,c)}return l}(t,e,i,s):e}function vi(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function wi(t,e){if(!e)return!1;const i=[],s=function(t,e){return Ut(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)}function Mi(t,e,i){return t.options.clip?t[i]:e[i]}function ki(t,e){const i=e._clip;if(i.disabled)return!1;const s=function(t,e){const{xScale:i,yScale:s}=t;return i&&s?{left:Mi(i,e,"left"),right:Mi(i,e,"right"),top:Mi(s,e,"top"),bottom:Mi(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}class Si{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){const n=e.listeners[s],o=e.duration;n.forEach((s=>s({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)})))}_refresh(){this._request||(this._running=!0,this._request=zt.call(window,(()=>{this._update(),this._request=null,this._running&&this._refresh()})))}_update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now(),e=0;this._charts.forEach(((i,s)=>{if(!i.running||!i.items.length)return;const n=i.items;let o,r=n.length-1,a=!1;for(;r>=0;--r)o=n[r],o._active?(o._total>i.duration&&(i.duration=o._total),o.tick(t),a=!0):(n[r]=n[n.length-1],n.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length})),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce(((t,e)=>Math.max(t,e._duration)),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var Pi=new Si;const Ci="transparent",Di={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){const s=Xt(t||Ci),n=s.valid&&Xt(e||Ci);return n&&n.valid?n.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class Ai{constructor(t,e,i,s){const n=e[i];s=Oe([t.to,s,n,t.from]);const o=Oe([t.from,n,s]);this._active=!0,this._fn=t.fn||Di[t.type||typeof o],this._easing=Yt[t.easing]||Yt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const s=this._target[this._prop],n=i-this._start,o=this._duration-n;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=n,this._loop=!!t.loop,this._to=Oe([t.to,e,s,t.from]),this._from=Oe([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,s=this._prop,n=this._from,o=this._loop,r=this._to;let a;if(this._active=n!==r&&(o||e<i),!this._active)return this._target[s]=r,void this._notify(!0);e<0?this._target[s]=n:(a=e/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[s]=this._fn(n,r,a))}wait(){const t=this._promises||(this._promises=[]);return new Promise(((e,i)=>{t.push({res:e,rej:i})}))}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}}class Oi{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!H(t))return;const e=Object.keys(re.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach((s=>{const n=t[s];if(!H(n))return;const o={};for(const t of e)o[t]=n[t];(B(n.properties)&&n.properties||[s]).forEach((t=>{t!==s&&i.has(t)||i.set(t,o)}))}))}_animateOptions(t,e){const i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i)return void(t.options=e);i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}}));return i}(t,i);if(!s)return[];const n=this._createAnimations(s,i);return i.$shared&&function(t,e){const i=[],s=Object.keys(e);for(let n=0;n<s.length;n++){const e=t[s[n]];e&&e.active()&&i.push(e.wait())}return Promise.all(i)}(t.options.$animations,i).then((()=>{t.options=i}),(()=>{})),n}_createAnimations(t,e){const i=this._properties,s=[],n=t.$animations||(t.$animations={}),o=Object.keys(e),r=Date.now();let a;for(a=o.length-1;a>=0;--a){const h=o[a];if("$"===h.charAt(0))continue;if("options"===h){s.push(...this._animateOptions(t,e));continue}const l=e[h];let c=n[h];const d=i.get(h);if(c){if(d&&c.active()){c.update(d,l,r);continue}c.cancel()}d&&d.duration?(n[h]=c=new Ai(d,t,h,l),s.push(c)):t[h]=l}return s}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);const i=this._createAnimations(t,e);return i.length?(Pi.add(this._chart,i),!0):void 0}}function Ti(t,e){const i=t&&t.options||{},s=i.reverse,n=void 0===i.min?e:0,o=void 0===i.max?e:0;return{start:s?o:n,end:s?n:o}}function Li(t,e){const i=[],s=t._getSortedDatasetMetas(e);let n,o;for(n=0,o=s.length;n<o;++n)i.push(s[n].index);return i}function Ri(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const n=t.keys,o="single"===s.mode;let r,a,h,l;if(null===e)return;let c=!1;for(r=0,a=n.length;r<a;++r){if(h=+n[r],h===i){if(c=!0,s.all)continue;break}l=t.values[h],V(l)&&(o||0===e||pt(e)===pt(l))&&(e+=l)}return c||s.all?e:0}function Ii(t,e){const i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function Ei(t,e,i){const s=t[e]||(t[e]={});return s[i]||(s[i]={})}function zi(t,e,i,s){for(const n of e.getMatchingVisibleMetas(s).reverse()){const e=t[n.index];if(i&&e>0||!i&&e<0)return n.index}return null}function Fi(t,e){const{chart:i,_cachedMeta:s}=t,n=i._stacks||(i._stacks={}),{iScale:o,vScale:r,index:a}=s,h=o.axis,l=r.axis,c=function(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}(o,r,s),d=e.length;let u;for(let f=0;f<d;++f){const t=e[f],{[h]:i,[l]:o}=t;u=(t._stacks||(t._stacks={}))[l]=Ei(n,c,i),u[a]=o,u._top=zi(u,r,!0,s.type),u._bottom=zi(u,r,!1,s.type);(u._visualValues||(u._visualValues={}))[a]=o}}function Wi(t,e){const i=t.scales;return Object.keys(i).filter((t=>i[t].axis===e)).shift()}function Bi(t,e){const i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||void 0===e[s]||void 0===e[s][i])return;delete e[s][i],void 0!==e[s]._visualValues&&void 0!==e[s]._visualValues[i]&&delete e[s]._visualValues[i]}}}const Hi=t=>"reset"===t||"none"===t,Vi=(t,e)=>e?t:Object.assign({},t);class ji{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ii(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Bi(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,n=e.xAxisID=N(i.xAxisID,Wi(t,"x")),o=e.yAxisID=N(i.yAxisID,Wi(t,"y")),r=e.rAxisID=N(i.rAxisID,Wi(t,"r")),a=e.indexAxis,h=e.iAxisID=s(a,n,o,r),l=e.vAxisID=s(a,o,n,r);e.xScale=this.getScaleForId(n),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(h),e.vScale=this.getScaleForId(l)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&It(this._data,this),t._stacked&&Bi(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(H(e)){const t=this._cachedMeta;this._data=function(t,e){const{iScale:i,vScale:s}=e,n="x"===i.axis?"x":"y",o="x"===s.axis?"x":"y",r=Object.keys(t),a=new Array(r.length);let h,l,c;for(h=0,l=r.length;h<l;++h)c=r[h],a[h]={[n]:c,[o]:t[c]};return a}(e,t)}else if(i!==e){if(i){It(i,this);const t=this._cachedMeta;Bi(t),t._parsed=[]}e&&Object.isExtensible(e)&&(n=this,(s=e)._chartjs?s._chartjs.listeners.push(n):(Object.defineProperty(s,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[n]}}),Rt.forEach((t=>{const e="_onData"+it(t),i=s[t];Object.defineProperty(s,t,{configurable:!0,enumerable:!1,value(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];const r=i.apply(this,n);return s._chartjs.listeners.forEach((t=>{"function"===typeof t[e]&&t[e](...n)})),r}})})))),this._syncList=[],this._data=e}var s,n}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const n=e._stacked;e._stacked=Ii(e.vScale,e),e.stack!==i.stack&&(s=!0,Bi(e),e.stack=i.stack),this._resyncElements(t),(s||n!==e._stacked)&&(Fi(this,e._parsed),e._stacked=Ii(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:s}=this,{iScale:n,_stacked:o}=i,r=n.axis;let a,h,l,c=0===t&&e===s.length||i._sorted,d=t>0&&i._parsed[t-1];if(!1===this._parsing)i._parsed=s,i._sorted=!0,l=s;else{l=B(s[t])?this.parseArrayData(i,s,t,e):H(s[t])?this.parseObjectData(i,s,t,e):this.parsePrimitiveData(i,s,t,e);const n=()=>null===h[r]||d&&h[r]<d[r];for(a=0;a<e;++a)i._parsed[a+t]=h=l[a],c&&(n()&&(c=!1),d=h);i._sorted=c}o&&Fi(this,l)}parsePrimitiveData(t,e,i,s){const{iScale:n,vScale:o}=t,r=n.axis,a=o.axis,h=n.getLabels(),l=n===o,c=new Array(s);let d,u,f;for(d=0,u=s;d<u;++d)f=d+i,c[d]={[r]:l||n.parse(h[f],f),[a]:o.parse(e[f],f)};return c}parseArrayData(t,e,i,s){const{xScale:n,yScale:o}=t,r=new Array(s);let a,h,l,c;for(a=0,h=s;a<h;++a)l=a+i,c=e[l],r[a]={x:n.parse(c[0],l),y:o.parse(c[1],l)};return r}parseObjectData(t,e,i,s){const{xScale:n,yScale:o}=t,{xAxisKey:r="x",yAxisKey:a="y"}=this._parsing,h=new Array(s);let l,c,d,u;for(l=0,c=s;l<c;++l)d=l+i,u=e[d],h[l]={x:n.parse(et(u,r),d),y:o.parse(et(u,a),d)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const s=this.chart,n=this._cachedMeta,o=e[t.axis];return Ri({keys:Li(s,!0),values:e._stacks[t.axis]._visualValues},o,n.index,{mode:i})}updateRangeFromParsed(t,e,i,s){const n=i[e.axis];let o=null===n?NaN:n;const r=s&&i._stacks[e.axis];s&&r&&(s.values=r,o=Ri(s,n,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,s=i._parsed,n=i._sorted&&t===i.iScale,o=s.length,r=this._getOtherScale(t),a=((t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:Li(i,!0),values:null})(e,i,this.chart),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:l,max:c}=function(t){const{min:e,max:i,minDefined:s,maxDefined:n}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:n?i:Number.POSITIVE_INFINITY}}(r);let d,u;function f(){u=s[d];const e=u[r.axis];return!V(u[t.axis])||l>e||c<e}for(d=0;d<o&&(f()||(this.updateRangeFromParsed(h,t,u,a),!n));++d);if(n)for(d=o-1;d>=0;--d)if(!f()){this.updateRangeFromParsed(h,t,u,a);break}return h}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let s,n,o;for(s=0,n=e.length;s<n;++s)o=e[s][t.axis],V(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,s=e.vScale,n=this.getParsed(t);return{label:i?""+i.getLabelForValue(n[i.axis]):"",value:s?""+s.getLabelForValue(n[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=function(t){let e,i,s,n;return H(t)?(e=t.top,i=t.right,s=t.bottom,n=t.left):e=i=s=n=t,{top:e,right:i,bottom:s,left:n,disabled:!1===t}}(N(this.options.clip,function(t,e,i){if(!1===i)return!1;const s=Ti(t,i),n=Ti(e,i);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],n=e.chartArea,o=[],r=this._drawStart||0,a=this._drawCount||s.length-r,h=this.options.drawActiveElementsOnTop;let l;for(i.dataset&&i.dataset.draw(t,n,r,a),l=r;l<r+a;++l){const e=s[l];e.hidden||(e.active&&h?o.push(e):e.draw(t,n))}for(l=0;l<o.length;++l)o[l].draw(t,n)}getStyle(t,e){const i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const s=this.getDataset();let n;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];n=e.$context||(e.$context=function(t,e,i){return Te(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}(this.getContext(),t,e)),n.parsed=this.getParsed(t),n.raw=s.data[t],n.index=n.dataIndex=t}else n=this.$context||(this.$context=function(t,e){return Te(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),n.dataset=s,n.index=n.datasetIndex=this.index;return n.active=!!e,n.mode=i,n}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",i=arguments.length>2?arguments[2]:void 0;const s="active"===e,n=this._cachedDataOpts,o=t+"-"+e,r=n[o],a=this.enableOptionSharing&&st(i);if(r)return Vi(r,a);const h=this.chart.config,l=h.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],d=h.getOptionScopes(this.getDataset(),l),u=Object.keys(re.elements[t]),f=h.resolveNamedOptions(d,u,(()=>this.getContext(i,s,e)),c);return f.$shared&&(f.$shared=a,n[o]=Object.freeze(Vi(f,a))),f}_resolveAnimations(t,e,i){const s=this.chart,n=this._cachedDataOpts,o=`animation-${e}`,r=n[o];if(r)return r;let a;if(!1!==s.options.animation){const s=this.chart.config,n=s.datasetAnimationScopeKeys(this._type,e),o=s.getOptionScopes(this.getDataset(),n);a=s.createResolver(o,this.getContext(t,i,e))}const h=new Oi(s,a&&a.animations);return a&&a._cacheable&&(n[o]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Hi(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,n=this.getSharedOptions(i),o=this.includeOptions(e,n)||n!==s;return this.updateSharedOptions(n,e,i),{sharedOptions:n,includeOptions:o}}updateElement(t,e,i,s){Hi(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!Hi(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;const n=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(n)||n})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[r,a,h]of this._syncList)this[r](a,h);this._syncList=[];const s=i.length,n=e.length,o=Math.min(n,s);o&&this.parse(0,o),n>s?this._insertElements(s,n-s,t):n<s&&this._removeElements(n,s-n)}_insertElements(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const s=this._cachedMeta,n=s.data,o=t+e;let r;const a=t=>{for(t.length+=e,r=t.length-1;r>=o;r--)t[r]=t[r-e]};for(a(n),r=t;r<o;++r)n[r]=new this.dataElementType;this._parsing&&a(s._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,e);i._stacked&&Bi(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}class Ni extends ji{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map(((e,n)=>{const o=t.getDatasetMeta(0).controller.getStyle(n);return{text:e,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,fontColor:s,lineWidth:o.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(n),index:n}}))}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let n,o,r=t=>+i[t];if(H(i[t])){const{key:t="value"}=this._parsing;r=e=>+et(i[e],t)}for(n=t,o=t+e;n<o;++n)s._parsed[n]=r(n)}}_getRotation(){return _t(this.options.rotation-90)}_getCircumference(){return _t(this.options.circumference)}_getRotationExtents(){let t=at,e=-at;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,n=s._getRotation(),o=s._getCircumference();t=Math.min(t,n),e=Math.max(e,n+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,s=this._cachedMeta,n=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(n)+this.options.spacing,r=Math.max((Math.min(i.width,i.height)-o)/2,0),a=Math.min((h=this.options.cutout,l=r,"string"===typeof h&&h.endsWith("%")?parseFloat(h)/100:+h/l),1);var h,l;const c=this._getRingWeight(this.index),{circumference:d,rotation:u}=this._getRotationExtents(),{ratioX:f,ratioY:g,offsetX:p,offsetY:m}=function(t,e,i){let s=1,n=1,o=0,r=0;if(e<at){const a=t,h=a+e,l=Math.cos(a),c=Math.sin(a),d=Math.cos(h),u=Math.sin(h),f=(t,e,s)=>Ct(t,a,h,!0)?1:Math.max(e,e*i,s,s*i),g=(t,e,s)=>Ct(t,a,h,!0)?-1:Math.min(e,e*i,s,s*i),p=f(0,l,d),m=f(dt,c,u),x=g(rt,l,d),b=g(rt+dt,c,u);s=(p-x)/2,n=(m-b)/2,o=-(p+x)/2,r=-(m+b)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}(u,d,a),x=(i.width-o)/f,b=(i.height-o)/g,y=Math.max(Math.min(x,b)/2,0),_=$(this.options.radius,y),v=(_-Math.max(_*a,0))/this._getVisibleDatasetWeightTotal();this.offsetX=p*_,this.offsetY=m*_,s.total=this.calculateTotal(),this.outerRadius=_-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*c,0),this.updateElements(n,0,n.length,t)}_circumference(t,e){const i=this.options,s=this._cachedMeta,n=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*n/at)}updateElements(t,e,i,s){const n="reset"===s,o=this.chart,r=o.chartArea,a=o.options.animation,h=(r.left+r.right)/2,l=(r.top+r.bottom)/2,c=n&&a.animateScale,d=c?0:this.innerRadius,u=c?0:this.outerRadius,{sharedOptions:f,includeOptions:g}=this._getSharedOptions(e,s);let p,m=this._getRotation();for(p=0;p<e;++p)m+=this._circumference(p,n);for(p=e;p<e+i;++p){const e=this._circumference(p,n),i=t[p],o={x:h+this.offsetX,y:l+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:u,innerRadius:d};g&&(o.options=f||this.resolveDataElementOptions(p,i.active?"active":s)),m+=e,this.updateElement(i,p,o,s)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i,s=0;for(i=0;i<e.length;i++){const n=t._parsed[i];null===n||isNaN(n)||!this.chart.getDataVisibility(i)||e[i].hidden||(s+=Math.abs(n))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?at*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,s=i.data.labels||[],n=Jt(e._parsed[t],i.options.locale);return{label:s[t]||"",value:n}}getMaxBorderWidth(t){let e=0;const i=this.chart;let s,n,o,r,a;if(!t)for(s=0,n=i.data.datasets.length;s<n;++s)if(i.isDatasetVisible(s)){o=i.getDatasetMeta(s),t=o.data,r=o.controller;break}if(!t)return 0;for(s=0,n=t.length;s<n;++s)a=r.resolveDataElementOptions(s),"inner"!==a.borderAlign&&(e=Math.max(e,a.borderWidth||0,a.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){const t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(N(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class $i extends ji{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:s=[],_dataset:n}=e,o=this.chart._animationsDisabled;let{start:r,count:a}=Ht(e,s,o);this._drawStart=r,this._drawCount=a,Vt(e)&&(r=0,a=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!n._decimated,i.points=s;const h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:h},t),this.updateElements(s,r,a,t)}updateElements(t,e,i,s){const n="reset"===s,{iScale:o,vScale:r,_stacked:a,_dataset:h}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,s),d=o.axis,u=r.axis,{spanGaps:f,segment:g}=this.options,p=bt(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||n||"none"===s,x=e+i,b=t.length;let y=e>0&&this.getParsed(e-1);for(let _=0;_<b;++_){const i=t[_],f=m?i:{};if(_<e||_>=x){f.skip=!0;continue}const b=this.getParsed(_),v=W(b[u]),w=f[d]=o.getPixelForValue(b[d],_),M=f[u]=n||v?r.getBasePixel():r.getPixelForValue(a?this.applyStack(r,b,a):b[u],_);f.skip=isNaN(w)||isNaN(M)||v,f.stop=_>0&&Math.abs(b[d]-y[d])>p,g&&(f.parsed=b,f.raw=h.data[_]),c&&(f.options=l||this.resolveDataElementOptions(_,i.active?"active":s)),m||this.updateElement(i,_,f,s),y=b}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;const n=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,n,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class Yi extends Ni{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}function Ui(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Xi{static override(t){Object.assign(Xi.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return Ui()}parse(){return Ui()}format(){return Ui()}add(){return Ui()}diff(){return Ui()}startOf(){return Ui()}endOf(){return Ui()}}var qi=Xi;function Ki(t,e,i,s){const{controller:n,data:o,_sorted:r}=t,a=n._cachedMeta.iScale,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&"r"!==e&&r&&o.length){const r=a._reversePixels?Lt:Tt;if(!s){const s=r(o,e,i);if(h){const{vScale:e}=n._cachedMeta,{_parsed:i}=t,o=i.slice(0,s.lo+1).reverse().findIndex((t=>!W(t[e.axis])));s.lo-=Math.max(0,o);const r=i.slice(s.hi).findIndex((t=>!W(t[e.axis])));s.hi+=Math.max(0,r)}return s}if(n._sharedOptions){const t=o[0],s="function"===typeof t.getRange&&t.getRange(e);if(s){const t=r(o,e,i-s),n=r(o,e,i+s);return{lo:t.lo,hi:n.hi}}}}return{lo:0,hi:o.length-1}}function Zi(t,e,i,s,n){const o=t.getSortedVisibleDatasetMetas(),r=i[e];for(let a=0,h=o.length;a<h;++a){const{index:t,data:i}=o[a],{lo:h,hi:l}=Ki(o[a],e,r,n);for(let e=h;e<=l;++e){const n=i[e];n.skip||s(n,t,e)}}}function Gi(t,e,i,s,n){const o=[];if(!n&&!t.isPointInArea(e))return o;return Zi(t,i,e,(function(i,r,a){(n||fe(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&o.push({element:i,datasetIndex:r,index:a})}),!0),o}function Ji(t,e,i,s,n,o){let r=[];const a=function(t){const e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){const n=e?Math.abs(t.x-s.x):0,o=i?Math.abs(t.y-s.y):0;return Math.sqrt(Math.pow(n,2)+Math.pow(o,2))}}(i);let h=Number.POSITIVE_INFINITY;return Zi(t,i,e,(function(i,l,c){const d=i.inRange(e.x,e.y,n);if(s&&!d)return;const u=i.getCenterPoint(n);if(!(!!o||t.isPointInArea(u))&&!d)return;const f=a(e,u);f<h?(r=[{element:i,datasetIndex:l,index:c}],h=f):f===h&&r.push({element:i,datasetIndex:l,index:c})})),r}function Qi(t,e,i,s,n,o){return o||t.isPointInArea(e)?"r"!==i||s?Ji(t,e,i,s,n,o):function(t,e,i,s){let n=[];return Zi(t,i,e,(function(t,i,o){const{startAngle:r,endAngle:a}=t.getProps(["startAngle","endAngle"],s),{angle:h}=Mt(t,{x:e.x,y:e.y});Ct(h,r,a)&&n.push({element:t,datasetIndex:i,index:o})})),n}(t,e,i,n):[]}function ts(t,e,i,s,n){const o=[],r="x"===i?"inXRange":"inYRange";let a=!1;return Zi(t,i,e,((t,s,h)=>{t[r]&&t[r](e[i],n)&&(o.push({element:t,datasetIndex:s,index:h}),a=a||t.inRange(e.x,e.y,n))})),s&&!a?[]:o}var es={evaluateInteractionItems:Zi,modes:{index(t,e,i,s){const n=ni(e,t),o=i.axis||"x",r=i.includeInvisible||!1,a=i.intersect?Gi(t,n,o,s,r):Qi(t,n,o,!1,s,r),h=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach((t=>{const e=a[0].index,i=t.data[e];i&&!i.skip&&h.push({element:i,datasetIndex:t.index,index:e})})),h):[]},dataset(t,e,i,s){const n=ni(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;let a=i.intersect?Gi(t,n,o,s,r):Qi(t,n,o,!1,s,r);if(a.length>0){const e=a[0].datasetIndex,i=t.getDatasetMeta(e).data;a=[];for(let t=0;t<i.length;++t)a.push({element:i[t],datasetIndex:e,index:t})}return a},point:(t,e,i,s)=>Gi(t,ni(e,t),i.axis||"xy",s,i.includeInvisible||!1),nearest(t,e,i,s){const n=ni(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;return Qi(t,n,o,i.intersect,s,r)},x:(t,e,i,s)=>ts(t,ni(e,t),"x",i.intersect,s),y:(t,e,i,s)=>ts(t,ni(e,t),"y",i.intersect,s)}};const is=["left","top","right","bottom"];function ss(t,e){return t.filter((t=>t.pos===e))}function ns(t,e){return t.filter((t=>-1===is.indexOf(t.pos)&&t.box.axis===e))}function os(t,e){return t.sort(((t,i)=>{const s=e?i:t,n=e?t:i;return s.weight===n.weight?s.index-n.index:s.weight-n.weight}))}function rs(t,e){const i=function(t){const e={};for(const i of t){const{stack:t,pos:s,stackWeight:n}=i;if(!t||!is.includes(s))continue;const o=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=n}return e}(t),{vBoxMaxWidth:s,hBoxMaxHeight:n}=e;let o,r,a;for(o=0,r=t.length;o<r;++o){a=t[o];const{fullSize:r}=a.box,h=i[a.stack],l=h&&a.stackWeight/h.weight;a.horizontal?(a.width=l?l*s:r&&e.availableWidth,a.height=n):(a.width=s,a.height=l?l*n:r&&e.availableHeight)}return i}function as(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function hs(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function ls(t,e,i,s){const{pos:n,box:o}=i,r=t.maxPadding;if(!H(n)){i.size&&(t[n]-=i.size);const e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?o.height:o.width),i.size=e.size/e.count,t[n]+=i.size}o.getPadding&&hs(r,o.getPadding());const a=Math.max(0,e.outerWidth-as(r,t,"left","right")),h=Math.max(0,e.outerHeight-as(r,t,"top","bottom")),l=a!==t.w,c=h!==t.h;return t.w=a,t.h=h,i.horizontal?{same:l,other:c}:{same:c,other:l}}function cs(t,e){const i=e.maxPadding;function s(t){const s={left:0,top:0,right:0,bottom:0};return t.forEach((t=>{s[t]=Math.max(e[t],i[t])})),s}return s(t?["left","right"]:["top","bottom"])}function ds(t,e,i,s){const n=[];let o,r,a,h,l,c;for(o=0,r=t.length,l=0;o<r;++o){a=t[o],h=a.box,h.update(a.width||e.w,a.height||e.h,cs(a.horizontal,e));const{same:r,other:d}=ls(e,i,a,s);l|=r&&n.length,c=c||d,h.fullSize||n.push(a)}return l&&ds(n,e,i,s)||c}function us(t,e,i,s,n){t.top=i,t.left=e,t.right=e+s,t.bottom=i+n,t.width=s,t.height=n}function fs(t,e,i,s){const n=i.padding;let{x:o,y:r}=e;for(const a of t){const t=a.box,h=s[a.stack]||{count:1,placed:0,weight:1},l=a.stackWeight/h.weight||1;if(a.horizontal){const s=e.w*l,o=h.size||t.height;st(h.start)&&(r=h.start),t.fullSize?us(t,n.left,r,i.outerWidth-n.right-n.left,o):us(t,e.left+h.placed,r,s,o),h.start=r,h.placed+=s,r=t.bottom}else{const s=e.h*l,r=h.size||t.width;st(h.start)&&(o=h.start),t.fullSize?us(t,o,n.top,r,i.outerHeight-n.bottom-n.top):us(t,o,e.top+h.placed,r,s),h.start=o,h.placed+=s,o=t.right}}e.x=o,e.y=r}var gs={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;const n=De(t.options.layout.padding),o=Math.max(e-n.width,0),r=Math.max(i-n.height,0),a=function(t){const e=function(t){const e=[];let i,s,n,o,r,a;for(i=0,s=(t||[]).length;i<s;++i)n=t[i],({position:o,options:{stack:r,stackWeight:a=1}}=n),e.push({index:i,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return e}(t),i=os(e.filter((t=>t.box.fullSize)),!0),s=os(ss(e,"left"),!0),n=os(ss(e,"right")),o=os(ss(e,"top"),!0),r=os(ss(e,"bottom")),a=ns(e,"x"),h=ns(e,"y");return{fullSize:i,leftAndTop:s.concat(o),rightAndBottom:n.concat(h).concat(r).concat(a),chartArea:ss(e,"chartArea"),vertical:s.concat(n).concat(h),horizontal:o.concat(r).concat(a)}}(t.boxes),h=a.vertical,l=a.horizontal;U(t.boxes,(t=>{"function"===typeof t.beforeLayout&&t.beforeLayout()}));const c=h.reduce(((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1),0)||1,d=Object.freeze({outerWidth:e,outerHeight:i,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/c,hBoxMaxHeight:r/2}),u=Object.assign({},n);hs(u,De(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=rs(h.concat(l),d);ds(a.fullSize,f,d,g),ds(h,f,d,g),ds(l,f,d,g)&&ds(h,f,d,g),function(t){const e=t.maxPadding;function i(i){const s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(f),fs(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,fs(a.rightAndBottom,f,d,g),t.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},U(a.chartArea,(e=>{const i=e.box;Object.assign(i,t.chartArea),i.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})}))}};class ps{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class ms extends ps{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const xs="$chartjs",bs={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ys=t=>null===t||""===t;const _s=!!hi&&{passive:!0};function vs(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,_s)}function ws(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function Ms(t,e,i){const s=t.canvas,n=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||ws(i.addedNodes,s),e=e&&!ws(i.removedNodes,s);e&&i()}));return n.observe(document,{childList:!0,subtree:!0}),n}function ks(t,e,i){const s=t.canvas,n=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||ws(i.removedNodes,s),e=e&&!ws(i.addedNodes,s);e&&i()}));return n.observe(document,{childList:!0,subtree:!0}),n}const Ss=new Map;let Ps=0;function Cs(){const t=window.devicePixelRatio;t!==Ps&&(Ps=t,Ss.forEach(((e,i)=>{i.currentDevicePixelRatio!==t&&e()})))}function Ds(t,e,i){const s=t.canvas,n=s&&Qe(s);if(!n)return;const o=Ft(((t,e)=>{const s=n.clientWidth;i(t,e),s<n.clientWidth&&i()}),window),r=new ResizeObserver((t=>{const e=t[0],i=e.contentRect.width,s=e.contentRect.height;0===i&&0===s||o(i,s)}));return r.observe(n),function(t,e){Ss.size||window.addEventListener("resize",Cs),Ss.set(t,e)}(t,o),r}function As(t,e,i){i&&i.disconnect(),"resize"===e&&function(t){Ss.delete(t),Ss.size||window.removeEventListener("resize",Cs)}(t)}function Os(t,e,i){const s=t.canvas,n=Ft((e=>{null!==t.ctx&&i(function(t,e){const i=bs[t.type]||t.type,{x:s,y:n}=ni(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==n?n:null}}(e,t))}),t);return function(t,e,i){t&&t.addEventListener(e,i,_s)}(s,e,n),n}class Ts extends ps{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){const i=t.style,s=t.getAttribute("height"),n=t.getAttribute("width");if(t[xs]={initial:{height:s,width:n,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",ys(n)){const e=li(t,"width");void 0!==e&&(t.width=e)}if(ys(s))if(""===t.style.height)t.height=t.width/(e||2);else{const e=li(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[xs])return!1;const i=e[xs].initial;["height","width"].forEach((t=>{const s=i[t];W(s)?e.removeAttribute(t):e.setAttribute(t,s)}));const s=i.style||{};return Object.keys(s).forEach((t=>{e.style[t]=s[t]})),e.width=e.width,delete e[xs],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const s=t.$proxies||(t.$proxies={}),n={attach:Ms,detach:ks,resize:Ds}[e]||Os;s[e]=n(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:As,detach:As,resize:As}[e]||vs)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return ri(t,e,i,s)}isAttached(t){const e=t&&Qe(t);return!(!e||!e.isConnected)}}class Ls{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return bt(this.x)&&bt(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const s={};return t.forEach((t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]})),s}}function Rs(t,e){const i=t.options.ticks,s=function(t){const e=t.options.offset,i=t._tickSize(),s=t._length/i+(e?0:1),n=t._maxLength/i;return Math.floor(Math.min(s,n))}(t),n=Math.min(i.maxTicksLimit||s,s),o=i.major.enabled?function(t){const e=[];let i,s;for(i=0,s=t.length;i<s;i++)t[i].major&&e.push(i);return e}(e):[],r=o.length,a=o[0],h=o[r-1],l=[];if(r>n)return function(t,e,i,s){let n,o=0,r=i[0];for(s=Math.ceil(s),n=0;n<t.length;n++)n===r&&(e.push(t[n]),o++,r=i[o*s])}(e,l,o,r/n),l;const c=function(t,e,i){const s=function(t){const e=t.length;let i,s;if(e<2)return!1;for(s=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==s)return!1;return s}(t),n=e.length/i;if(!s)return Math.max(n,1);const o=function(t){const e=[],i=Math.sqrt(t);let s;for(s=1;s<i;s++)t%s===0&&(e.push(s),e.push(t/s));return i===(0|i)&&e.push(i),e.sort(((t,e)=>t-e)).pop(),e}(s);for(let r=0,a=o.length-1;r<a;r++){const t=o[r];if(t>n)return t}return Math.max(n,1)}(o,e,n);if(r>0){let t,i;const s=r>1?Math.round((h-a)/(r-1)):null;for(Is(e,l,c,W(s)?0:a-s,a),t=0,i=r-1;t<i;t++)Is(e,l,c,o[t],o[t+1]);return Is(e,l,c,h,W(s)?e.length:h+s),l}return Is(e,l,c),l}function Is(t,e,i,s,n){const o=N(s,0),r=Math.min(N(n,t.length),t.length);let a,h,l,c=0;for(i=Math.ceil(i),n&&(a=n-s,i=a/Math.floor(a/i)),l=o;l<0;)c++,l=Math.round(o+c*i);for(h=Math.max(o,0);h<r;h++)h===l&&(e.push(t[h]),c++,l=Math.round(o+c*i))}const Es=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,zs=(t,e)=>Math.min(e||t,t);function Fs(t,e){const i=[],s=t.length/e,n=t.length;let o=0;for(;o<n;o+=s)i.push(t[Math.floor(o)]);return i}function Ws(t,e,i){const s=t.ticks.length,n=Math.min(e,s-1),o=t._startPixel,r=t._endPixel,a=1e-6;let h,l=t.getPixelForTick(n);if(!(i&&(h=1===s?Math.max(l-o,r-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,l+=n<e?h:-h,l<o-a||l>r+a)))return l}function Bs(t){return t.drawTicks?t.tickLength:0}function Hs(t,e){if(!t.display)return 0;const i=Ae(t.font,e),s=De(t.padding);return(B(t.text)?t.text.length:1)*i.lineHeight+s.height}function Vs(t,e,i){let s=Wt(t);return(i&&"right"!==e||!i&&"right"===e)&&(s=(t=>"left"===t?"right":"right"===t?"left":t)(s)),s}class js extends Ls{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=j(t,Number.POSITIVE_INFINITY),e=j(e,Number.NEGATIVE_INFINITY),i=j(i,Number.POSITIVE_INFINITY),s=j(s,Number.NEGATIVE_INFINITY),{min:j(t,i),max:j(e,s),minDefined:V(t),maxDefined:V(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:n,maxDefined:o}=this.getUserBounds();if(n&&o)return{min:i,max:s};const r=this.getMatchingVisibleMetas();for(let a=0,h=r.length;a<h;++a)e=r[a].controller.getMinMax(this,t),n||(i=Math.min(i,e.min)),o||(s=Math.max(s,e.max));return i=o&&i>s?s:i,s=n&&i>s?i:s,{min:j(i,j(s,i)),max:j(s,j(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.chart.chartArea;return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){Y(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:s,grace:n,ticks:o}=this.options,r=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){const{min:s,max:n}=t,o=$(e,(n-s)/2),r=(t,e)=>i&&0===t?0:t+e;return{min:r(s,-Math.abs(o)),max:r(n,o)}}(this,n,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=r<this.ticks.length;this._convertTicksToLabels(a?Fs(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||"auto"===o.source)&&(this.ticks=Rs(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){Y(this.options.afterUpdate,[this])}beforeSetDimensions(){Y(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){Y(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),Y(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){Y(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,s,n;for(i=0,s=t.length;i<s;i++)n=t[i],n.label=Y(e.callback,[n.value,i,t],this)}afterTickToLabelConversion(){Y(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){Y(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=zs(this.ticks.length,t.ticks.maxTicksLimit),s=e.minRotation||0,n=e.maxRotation;let o,r,a,h=s;if(!this._isVisible()||!e.display||s>=n||i<=1||!this.isHorizontal())return void(this.labelRotation=s);const l=this._getLabelSizes(),c=l.widest.width,d=l.highest.height,u=Dt(this.chart.width-c,0,this.maxWidth);o=t.offset?this.maxWidth/i:u/(i-1),c+6>o&&(o=u/(i-(t.offset?.5:1)),r=this.maxHeight-Bs(t.grid)-e.padding-Hs(t.title,this.chart.options.font),a=Math.sqrt(c*c+d*d),h=vt(Math.min(Math.asin(Dt((l.highest.height+6)/o,-1,1)),Math.asin(Dt(r/a,-1,1))-Math.asin(Dt(d/a,-1,1)))),h=Math.max(s,Math.min(n,h))),this.labelRotation=h}afterCalculateLabelRotation(){Y(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){Y(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:n}}=this,o=this._isVisible(),r=this.isHorizontal();if(o){const o=Hs(s,e.options.font);if(r?(t.width=this.maxWidth,t.height=Bs(n)+o):(t.height=this.maxHeight,t.width=Bs(n)+o),i.display&&this.ticks.length){const{first:e,last:s,widest:n,highest:o}=this._getLabelSizes(),a=2*i.padding,h=_t(this.labelRotation),l=Math.cos(h),c=Math.sin(h);if(r){const e=i.mirror?0:c*n.width+l*o.height;t.height=Math.min(this.maxHeight,t.height+e+a)}else{const e=i.mirror?0:l*n.width+c*o.height;t.width=Math.min(this.maxWidth,t.width+e+a)}this._calculatePadding(e,s,c,l)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){const{ticks:{align:n,padding:o},position:r}=this.options,a=0!==this.labelRotation,h="top"!==r&&"x"===this.axis;if(this.isHorizontal()){const r=this.getPixelForTick(0)-this.left,l=this.right-this.getPixelForTick(this.ticks.length-1);let c=0,d=0;a?h?(c=s*t.width,d=i*e.height):(c=i*t.height,d=s*e.width):"start"===n?d=e.width:"end"===n?c=t.width:"inner"!==n&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-r+o)*this.width/(this.width-r),0),this.paddingRight=Math.max((d-l+o)*this.width/(this.width-l),0)}else{let i=e.height/2,s=t.height/2;"start"===n?(i=0,s=t.height):"end"===n&&(i=e.height,s=0),this.paddingTop=i+o,this.paddingBottom=s+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){Y(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)W(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=Fs(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:s,_longestTextCache:n}=this,o=[],r=[],a=Math.floor(e/zs(e,i));let h,l,c,d,u,f,g,p,m,x,b,y=0,_=0;for(h=0;h<e;h+=a){if(d=t[h].label,u=this._resolveTickFontOptions(h),s.font=f=u.string,g=n[f]=n[f]||{data:{},gc:[]},p=u.lineHeight,m=x=0,W(d)||B(d)){if(B(d))for(l=0,c=d.length;l<c;++l)b=d[l],W(b)||B(b)||(m=ae(s,g.data,g.gc,m,b),x+=p)}else m=ae(s,g.data,g.gc,m,d),x=p;o.push(m),r.push(x),y=Math.max(m,y),_=Math.max(x,_)}!function(t,e){U(t,(t=>{const i=t.gc,s=i.length/2;let n;if(s>e){for(n=0;n<s;++n)delete t.data[i[n]];i.splice(0,s)}}))}(n,e);const v=o.indexOf(y),w=r.indexOf(_),M=t=>({width:o[t]||0,height:r[t]||0});return{first:M(0),last:M(e-1),widest:M(v),highest:M(w),widths:o,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Dt(this._alignToPixels?le(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=function(t,e,i){return Te(t,{tick:i,index:e,type:"tick"})}(this.getContext(),t,i))}return this.$context||(this.$context=Te(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){const t=this.options.ticks,e=_t(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),n=this._getLabelSizes(),o=t.autoSkipPadding||0,r=n?n.widest.width+o:0,a=n?n.highest.height+o:0;return this.isHorizontal()?a*i>r*s?r/i:a/s:a*s<r*i?a/i:r/s}_isVisible(){const t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,s=this.options,{grid:n,position:o,border:r}=s,a=n.offset,h=this.isHorizontal(),l=this.ticks.length+(a?1:0),c=Bs(n),d=[],u=r.setContext(this.getContext()),f=u.display?u.width:0,g=f/2,p=function(t){return le(i,t,f)};let m,x,b,y,_,v,w,M,k,S,P,C;if("top"===o)m=p(this.bottom),v=this.bottom-c,M=m-g,S=p(t.top)+g,C=t.bottom;else if("bottom"===o)m=p(this.top),S=t.top,C=p(t.bottom)-g,v=m+g,M=this.top+c;else if("left"===o)m=p(this.right),_=this.right-c,w=m-g,k=p(t.left)+g,P=t.right;else if("right"===o)m=p(this.left),k=t.left,P=p(t.right)-g,_=m+g,w=this.left+c;else if("x"===e){if("center"===o)m=p((t.top+t.bottom)/2+.5);else if(H(o)){const t=Object.keys(o)[0],e=o[t];m=p(this.chart.scales[t].getPixelForValue(e))}S=t.top,C=t.bottom,v=m+g,M=v+c}else if("y"===e){if("center"===o)m=p((t.left+t.right)/2);else if(H(o)){const t=Object.keys(o)[0],e=o[t];m=p(this.chart.scales[t].getPixelForValue(e))}_=m-g,w=_-c,k=t.left,P=t.right}const D=N(s.ticks.maxTicksLimit,l),A=Math.max(1,Math.ceil(l/D));for(x=0;x<l;x+=A){const t=this.getContext(x),e=n.setContext(t),s=r.setContext(t),o=e.lineWidth,l=e.color,c=s.dash||[],u=s.dashOffset,f=e.tickWidth,g=e.tickColor,p=e.tickBorderDash||[],m=e.tickBorderDashOffset;b=Ws(this,x,a),void 0!==b&&(y=le(i,b,o),h?_=w=k=P=y:v=M=S=C=y,d.push({tx1:_,ty1:v,tx2:w,ty2:M,x1:k,y1:S,x2:P,y2:C,width:o,color:l,borderDash:c,borderDashOffset:u,tickWidth:f,tickColor:g,tickBorderDash:p,tickBorderDashOffset:m}))}return this._ticksLength=l,this._borderValue=m,d}_computeLabelItems(t){const e=this.axis,i=this.options,{position:s,ticks:n}=i,o=this.isHorizontal(),r=this.ticks,{align:a,crossAlign:h,padding:l,mirror:c}=n,d=Bs(i.grid),u=d+l,f=c?-l:u,g=-_t(this.labelRotation),p=[];let m,x,b,y,_,v,w,M,k,S,P,C,D="middle";if("top"===s)v=this.bottom-f,w=this._getXAxisLabelAlignment();else if("bottom"===s)v=this.top+f,w=this._getXAxisLabelAlignment();else if("left"===s){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,_=t.x}else if("right"===s){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,_=t.x}else if("x"===e){if("center"===s)v=(t.top+t.bottom)/2+u;else if(H(s)){const t=Object.keys(s)[0],e=s[t];v=this.chart.scales[t].getPixelForValue(e)+u}w=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===s)_=(t.left+t.right)/2-u;else if(H(s)){const t=Object.keys(s)[0],e=s[t];_=this.chart.scales[t].getPixelForValue(e)}w=this._getYAxisLabelAlignment(d).textAlign}"y"===e&&("start"===a?D="top":"end"===a&&(D="bottom"));const A=this._getLabelSizes();for(m=0,x=r.length;m<x;++m){b=r[m],y=b.label;const t=n.setContext(this.getContext(m));M=this.getPixelForTick(m)+n.labelOffset,k=this._resolveTickFontOptions(m),S=k.lineHeight,P=B(y)?y.length:1;const e=P/2,i=t.color,a=t.textStrokeColor,l=t.textStrokeWidth;let d,u=w;if(o?(_=M,"inner"===w&&(u=m===x-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),C="top"===s?"near"===h||0!==g?-P*S+S/2:"center"===h?-A.highest.height/2-e*S+S:-A.highest.height+S/2:"near"===h||0!==g?S/2:"center"===h?A.highest.height/2-e*S:A.highest.height-P*S,c&&(C*=-1),0===g||t.showLabelBackdrop||(_+=S/2*Math.sin(g))):(v=M,C=(1-P)*S/2),t.showLabelBackdrop){const e=De(t.backdropPadding),i=A.heights[m],s=A.widths[m];let n=C-e.top,o=0-e.left;switch(D){case"middle":n-=i/2;break;case"bottom":n-=i}switch(w){case"center":o-=s/2;break;case"right":o-=s;break;case"inner":m===x-1?o-=s:m>0&&(o-=s/2)}d={left:o,top:n,width:s+e.width,height:i+e.height,color:t.backdropColor}}p.push({label:y,font:k,textOffset:C,options:{rotation:g,color:i,strokeColor:a,strokeWidth:l,textAlign:u,textBaseline:D,translation:[_,v],backdrop:d}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-_t(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:s,padding:n}}=this.options,o=t+n,r=this._getLabelSizes().widest.width;let a,h;return"left"===e?s?(h=this.right+n,"near"===i?a="left":"center"===i?(a="center",h+=r/2):(a="right",h+=r)):(h=this.right-o,"near"===i?a="right":"center"===i?(a="center",h-=r/2):(a="left",h=this.left)):"right"===e?s?(h=this.left+n,"near"===i?a="right":"center"===i?(a="center",h-=r/2):(a="left",h-=r)):(h=this.left+o,"near"===i?a="left":"center"===i?(a="center",h+=r/2):(a="right",h=this.right)):a="right",{textAlign:a,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:s,width:n,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,n,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const i=this.ticks.findIndex((e=>e.value===t));if(i>=0){return e.setContext(this.getContext(i)).lineWidth}return 0}drawGrid(t){const e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let n,o;const r=(t,e,s)=>{s.width&&s.color&&(i.save(),i.lineWidth=s.width,i.strokeStyle=s.color,i.setLineDash(s.borderDash||[]),i.lineDashOffset=s.borderDashOffset,i.beginPath(),i.moveTo(t.x,t.y),i.lineTo(e.x,e.y),i.stroke(),i.restore())};if(e.display)for(n=0,o=s.length;n<o;++n){const t=s[n];e.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:s}}=this,n=i.setContext(this.getContext()),o=i.display?n.width:0;if(!o)return;const r=s.setContext(this.getContext(0)).lineWidth,a=this._borderValue;let h,l,c,d;this.isHorizontal()?(h=le(t,this.left,o)-o/2,l=le(t,this.right,r)+r/2,c=d=a):(c=le(t,this.top,o)-o/2,d=le(t,this.bottom,r)+r/2,h=l=a),e.save(),e.lineWidth=n.width,e.strokeStyle=n.color,e.beginPath(),e.moveTo(h,c),e.lineTo(l,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const e=this.ctx,i=this._computeLabelArea();i&&ge(e,i);const s=this.getLabelItems(t);for(const n of s){const t=n.options,i=n.font;_e(e,n.label,0,n.textOffset,i,t)}i&&pe(e)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;const n=Ae(i.font),o=De(i.padding),r=i.align;let a=n.lineHeight/2;"bottom"===e||"center"===e||H(e)?(a+=o.bottom,B(i.text)&&(a+=n.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:h,titleY:l,maxWidth:c,rotation:d}=function(t,e,i,s){const{top:n,left:o,bottom:r,right:a,chart:h}=t,{chartArea:l,scales:c}=h;let d,u,f,g=0;const p=r-n,m=a-o;if(t.isHorizontal()){if(u=Bt(s,o,a),H(i)){const t=Object.keys(i)[0],s=i[t];f=c[t].getPixelForValue(s)+p-e}else f="center"===i?(l.bottom+l.top)/2+p-e:Es(t,i,e);d=a-o}else{if(H(i)){const t=Object.keys(i)[0],s=i[t];u=c[t].getPixelForValue(s)-m+e}else u="center"===i?(l.left+l.right)/2-m+e:Es(t,i,e);f=Bt(s,r,n),g="left"===i?-dt:dt}return{titleX:u,titleY:f,maxWidth:d,rotation:g}}(this,a,e,r);_e(t,i.text,0,0,n,{color:i.color,maxWidth:c,rotation:d,textAlign:Vs(r,e,s),textBaseline:"middle",translation:[h,l]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=N(t.grid&&t.grid.z,-1),s=N(t.border&&t.border.z,0);return this._isVisible()&&this.draw===js.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let n,o;for(n=0,o=e.length;n<o;++n){const o=e[n];o[i]!==this.id||t&&o.type!==t||s.push(o)}return s}_resolveTickFontOptions(t){return Ae(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Ns{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;(function(t){return"id"in t&&"defaults"in t})(e)&&(i=this.register(e));const s=this.items,n=t.id,o=this.scope+"."+n;if(!n)throw new Error("class does not have id: "+t);return n in s||(s[n]=t,function(t,e,i){const s=G(Object.create(null),[i?re.get(i):{},re.get(e),t.defaults]);re.set(e,s),t.defaultRoutes&&function(t,e){Object.keys(e).forEach((i=>{const s=i.split("."),n=s.pop(),o=[t].concat(s).join("."),r=e[i].split("."),a=r.pop(),h=r.join(".");re.route(o,n,h,a)}))}(e,t.defaultRoutes);t.descriptors&&re.describe(e,t.descriptors)}(t,o,i),this.override&&re.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in re[s]&&(delete re[s][i],this.override&&delete ee[i])}}class $s{constructor(){this.controllers=new Ns(ji,"datasets",!0),this.elements=new Ns(Ls,"elements"),this.plugins=new Ns(Object,"plugins"),this.scales=new Ns(js,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("register",e)}remove(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("unregister",e)}addControllers(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("register",e,this.controllers)}addElements(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("register",e,this.elements)}addPlugins(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("register",e,this.plugins)}addScales(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("register",e,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("unregister",e,this.controllers)}removeElements(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("unregister",e,this.elements)}removePlugins(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("unregister",e,this.plugins)}removeScales(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._each("unregister",e,this.scales)}_each(t,e,i){[...e].forEach((e=>{const s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):U(e,(e=>{const s=i||this._getRegistryForType(e);this._exec(t,s,e)}))}))}_exec(t,e,i){const s=it(t);Y(i["before"+s],[],i),e[t](i),Y(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const s=e.get(t);if(void 0===s)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var Ys=new $s;class Us{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const n=s?this._descriptors(t).filter(s):this._descriptors(t),o=this._notify(n,t,e,i);return"afterDestroy"===e&&(this._notify(n,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,s){s=s||{};for(const n of t){const t=n.plugin;if(!1===Y(t[i],[e,s,n.options],t)&&s.cancelable)return!1}return!0}invalidate(){W(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,s=N(i.options&&i.options.plugins,{}),n=function(t){const e={},i=[],s=Object.keys(Ys.plugins.items);for(let o=0;o<s.length;o++)i.push(Ys.getPlugin(s[o]));const n=t.plugins||[];for(let o=0;o<n.length;o++){const t=n[o];-1===i.indexOf(t)&&(i.push(t),e[t.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,e,i,s){let{plugins:n,localIds:o}=e;const r=[],a=t.getContext();for(const h of n){const e=h.id,n=Xs(i[e],s);null!==n&&r.push({plugin:h,options:qs(t.config,{plugin:h,local:o[e]},n,a)})}return r}(t,n,s,e):[]}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter((t=>!e.some((e=>t.plugin.id===e.plugin.id))));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function Xs(t,e){return e||!1!==t?!0===t?{}:t:null}function qs(t,e,i,s){let{plugin:n,local:o}=e;const r=t.pluginScopeKeys(n),a=t.getOptionScopes(i,r);return o&&n.defaults&&a.push(n.defaults),t.createResolver(a,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ks(t,e){const i=re.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function Zs(t){if("x"===t||"y"===t||"r"===t)return t}function Gs(t){if(Zs(t))return t;for(var e=arguments.length,i=new Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];for(const o of i){const e=o.axis||("top"===(n=o.position)||"bottom"===n?"x":"left"===n||"right"===n?"y":void 0)||t.length>1&&Zs(t[0].toLowerCase());if(e)return e}var n;throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Js(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function Qs(t,e){const i=ee[t.type]||{scales:{}},s=e.scales||{},n=Ks(t.type,e),o=Object.create(null);return Object.keys(s).forEach((e=>{const r=s[e];if(!H(r))return console.error(`Invalid scale configuration for scale: ${e}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);const a=Gs(e,r,function(t,e){if(e.data&&e.data.datasets){const i=e.data.datasets.filter((e=>e.xAxisID===t||e.yAxisID===t));if(i.length)return Js(t,"x",i[0])||Js(t,"y",i[0])}return{}}(e,t),re.scales[r.type]),h=function(t,e){return t===e?"_index_":"_value_"}(a,n),l=i.scales||{};o[e]=J(Object.create(null),[{axis:a},r,l[a],l[h]])})),t.data.datasets.forEach((i=>{const n=i.type||t.type,r=i.indexAxis||Ks(n,e),a=(ee[n]||{}).scales||{};Object.keys(a).forEach((t=>{const e=function(t,e){let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i}(t,r),n=i[e+"AxisID"]||e;o[n]=o[n]||Object.create(null),J(o[n],[{axis:e},s[n],a[t]])}))})),Object.keys(o).forEach((t=>{const e=o[t];J(e,[re.scales[e.type],re.scale])})),o}function tn(t){const e=t.options||(t.options={});e.plugins=N(e.plugins,{}),e.scales=Qs(t,e)}function en(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}const sn=new Map,nn=new Set;function on(t,e){let i=sn.get(t);return i||(i=e(),sn.set(t,i),nn.add(i)),i}const rn=(t,e,i)=>{const s=et(e,i);void 0!==s&&t.add(s)};class an{constructor(t){this._config=function(t){return(t=t||{}).data=en(t.data),tn(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=en(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),tn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return on(t,(()=>[[`datasets.${t}`,""]]))}datasetAnimationScopeKeys(t,e){return on(`${t}.transition.${e}`,(()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]]))}datasetElementScopeKeys(t,e){return on(`${t}-${e}`,(()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]]))}pluginScopeKeys(t){const e=t.id;return on(`${this.type}-plugin-${e}`,(()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]]))}_cachedScopes(t,e){const i=this._scopeCache;let s=i.get(t);return s&&!e||(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){const{options:s,type:n}=this,o=this._cachedScopes(t,i),r=o.get(e);if(r)return r;const a=new Set;e.forEach((e=>{t&&(a.add(t),e.forEach((e=>rn(a,t,e)))),e.forEach((t=>rn(a,s,t))),e.forEach((t=>rn(a,ee[n]||{},t))),e.forEach((t=>rn(a,re,t))),e.forEach((t=>rn(a,ie,t)))}));const h=Array.from(a);return 0===h.length&&h.push(Object.create(null)),nn.has(e)&&o.set(e,h),h}chartOptionScopes(){const{options:t,type:e}=this;return[t,ee[e]||{},re.datasets[e]||{},{type:e},re,ie]}resolveNamedOptions(t,e,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[""];const n={$shared:!0},{resolver:o,subPrefixes:r}=hn(this._resolverCache,t,s);let a=o;if(function(t,e){const{isScriptable:i,isIndexable:s}=Ie(t);for(const n of e){const e=i(n),o=s(n),r=(o||e)&&t[n];if(e&&(nt(r)||ln(r))||o&&B(r))return!0}return!1}(o,e)){n.$shared=!1;a=Re(o,i=nt(i)?i():i,this.createResolver(t,i,r))}for(const h of e)n[h]=a[h];return n}createResolver(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[""],s=arguments.length>3?arguments[3]:void 0;const{resolver:n}=hn(this._resolverCache,t,i);return H(e)?Re(n,e,void 0,s):n}}function hn(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));const n=i.join();let o=s.get(n);if(!o){o={resolver:Le(e,i),subPrefixes:i.filter((t=>!t.toLowerCase().includes("hover")))},s.set(n,o)}return o}const ln=t=>H(t)&&Object.getOwnPropertyNames(t).some((e=>nt(t[e])));const cn=["top","bottom","left","right","chartArea"];function dn(t,e){return"top"===t||"bottom"===t||-1===cn.indexOf(t)&&"x"===e}function un(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function fn(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),Y(i&&i.onComplete,[t],e)}function gn(t){const e=t.chart,i=e.options.animation;Y(i&&i.onProgress,[t],e)}function pn(t){return Je()&&"string"===typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const mn={},xn=t=>{const e=pn(t);return Object.values(mn).filter((t=>t.canvas===e)).pop()};function bn(t,e,i){const s=Object.keys(t);for(const n of s){const s=+n;if(s>=e){const o=t[n];delete t[n],(i>0||s>e)&&(t[s+i]=o)}}}class yn{static defaults=(()=>re)();static instances=(()=>mn)();static overrides=(()=>ee)();static registry=(()=>Ys)();static version="4.4.9";static getChart=(()=>xn)();static register(){Ys.add(...arguments),_n()}static unregister(){Ys.remove(...arguments),_n()}constructor(t,e){const i=this.config=new an(e),s=pn(t),n=xn(s);if(n)throw new Error("Canvas is already in use. Chart with ID '"+n.id+"' must be destroyed before the canvas with ID '"+n.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||function(t){return!Je()||"undefined"!==typeof OffscreenCanvas&&t instanceof OffscreenCanvas?ms:Ts}(s)),this.platform.updateConfig(i);const r=this.platform.acquireContext(s,o.aspectRatio),a=r&&r.canvas,h=a&&a.height,l=a&&a.width;this.id=F(),this.ctx=r,this.canvas=a,this.width=l,this.height=h,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Us,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(){for(var s=arguments.length,n=new Array(s),o=0;o<s;o++)n[o]=arguments[o];return e?(clearTimeout(i),i=setTimeout(t,e,n)):t.apply(this,n),e}}((t=>this.update(t)),o.resizeDelay||0),this._dataChanges=[],mn[this.id]=this,r&&a?(Pi.listen(this,"complete",fn),Pi.listen(this,"progress",gn),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:n}=this;return W(t)?e&&n?n:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Ys}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ai(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ce(this.canvas,this.ctx),this}stop(){return Pi.stop(this),this}resize(t,e){Pi.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,s=this.canvas,n=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,t,e,n),r=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,ai(this,r,!0)&&(this.notifyPlugins("resize",{size:o}),Y(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){U(this.options.scales||{},((t,e)=>{t.id=e}))}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce(((t,e)=>(t[e]=!1,t)),{});let n=[];e&&(n=n.concat(Object.keys(e).map((t=>{const i=e[t],s=Gs(t,i),n="r"===s,o="x"===s;return{options:i,dposition:n?"chartArea":o?"bottom":"left",dtype:n?"radialLinear":o?"category":"linear"}})))),U(n,(e=>{const n=e.options,o=n.id,r=Gs(o,n),a=N(n.type,e.dtype);void 0!==n.position&&dn(n.position,r)===dn(e.dposition)||(n.position=e.dposition),s[o]=!0;let h=null;if(o in i&&i[o].type===a)h=i[o];else{h=new(Ys.getScale(a))({id:o,type:a,ctx:this.ctx,chart:this}),i[h.id]=h}h.init(n,t)})),U(s,((t,e)=>{t||delete i[e]})),U(i,(t=>{gs.configure(this,t,t.options),gs.addBox(this,t)}))}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort(((t,e)=>t.index-e.index)),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(un("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach(((t,i)=>{0===e.filter((e=>e===t._dataset)).length&&this._destroyDatasetMeta(i)}))}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){const s=e[i];let n=this.getDatasetMeta(i);const o=s.type||this.config.type;if(n.type&&n.type!==o&&(this._destroyDatasetMeta(i),n=this.getDatasetMeta(i)),n.type=o,n.indexAxis=s.indexAxis||Ks(o,this.options),n.order=s.order||0,n.index=i,n.label=""+s.label,n.visible=this.isDatasetVisible(i),n.controller)n.controller.updateIndex(i),n.controller.linkScales();else{const e=Ys.getController(o),{datasetElementType:s,dataElementType:r}=re.datasets[o];Object.assign(e,{dataElementType:Ys.getElement(r),datasetElementType:s&&Ys.getElement(s)}),n.controller=new e(this,i),t.push(n.controller)}}return this._updateMetasets(),t}_resetElements(){U(this.data.datasets,((t,e)=>{this.getDatasetMeta(e).controller.reset()}),this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const n=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let h=0,l=this.data.datasets.length;h<l;h++){const{controller:t}=this.getDatasetMeta(h),e=!s&&-1===n.indexOf(t);t.buildOrUpdateElements(e),o=Math.max(+t.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||U(n,(t=>{t.reset()})),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(un("z","_idx"));const{_active:r,_lastEvent:a}=this;a?this._eventHandler(a,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){U(this.scales,(t=>{gs.removeBox(this,t)})),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);ot(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:n}of e){bn(t,s,"_removeElements"===i?-n:n)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=e=>new Set(t.filter((t=>t[0]===e)).map(((t,e)=>e+","+t.splice(1).join(",")))),s=i(0);for(let n=1;n<e;n++)if(!ot(s,i(n)))return;return Array.from(s).map((t=>t.split(","))).map((t=>({method:t[1],start:+t[2],count:+t[3]})))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;gs.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],U(this.boxes,(t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))}),this),this._layers.forEach(((t,e)=>{t._idx=e})),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,nt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(Pi.has(this)?this.attached&&!Pi.running(this)&&Pi.start(this):(this.draw(),fn({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let s,n;for(s=0,n=e.length;s<n;++s){const n=e[s];t&&!n.visible||i.push(n)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=ki(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&ge(e,s),t.controller.draw(),s&&pe(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return fe(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){const n=es.modes[e];return"function"===typeof n?n(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter((t=>t&&t._dataset===e)).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=Te(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"===typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",n=this.getDatasetMeta(t),o=n.controller._resolveAnimations(void 0,s);st(e)?(n.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(n,{visible:i}),this.update((e=>e.datasetIndex===t?s:void 0)))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),Pi.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ce(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete mn[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(){return this.canvas.toDataURL(...arguments)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};U(this.options.events,(t=>i(t,s)))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(i,s)=>{t[i]&&(e.removeEventListener(this,i,s),delete t[i])},n=(t,e)=>{this.canvas&&this.resize(t,e)};let o;const r=()=>{s("attach",r),this.attached=!0,this.resize(),i("resize",n),i("detach",o)};o=()=>{this.attached=!1,s("resize",n),this._stop(),this._resize(0,0),i("attach",r)},e.isAttached(this.canvas)?r():o()}unbindEvents(){U(this._listeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._listeners={},U(this._responsiveListeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const s=i?"set":"remove";let n,o,r,a;for("dataset"===e&&(n=this.getDatasetMeta(t[0].datasetIndex),n.controller["_"+s+"DatasetHoverStyle"]()),r=0,a=t.length;r<a;++r){o=t[r];const e=o&&this.getDatasetMeta(o.datasetIndex).controller;e&&e[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map((t=>{let{datasetIndex:e,index:i}=t;const s=this.getDatasetMeta(e);if(!s)throw new Error("No dataset found at index "+e);return{datasetIndex:e,element:s.data[i],index:i}}));!X(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter((e=>e.plugin.id===t)).length}_updateHoverStyles(t,e,i){const s=this.options.hover,n=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)))),o=n(e,t),r=i?t:n(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),r.length&&s.mode&&this.updateHoverStyle(r,s.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;const n=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(n||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:s=[],options:n}=this,o=e,r=this._getActiveElements(t,s,i,o),a=function(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}(t),h=function(t,e,i,s){return i&&"mouseout"!==t.type?s?e:t:null}(t,this._lastEvent,i,a);i&&(this._lastEvent=null,Y(n.onHover,[t,r,this],this),a&&Y(n.onClick,[t,r,this],this));const l=!X(r,s);return(l||e)&&(this._active=r,this._updateHoverStyles(r,s,e)),this._lastEvent=h,l}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;const n=this.options.hover;return this.getElementsAtEventForMode(t,n.mode,n,s)}}function _n(){return U(yn.instances,(t=>t._plugins.invalidate()))}function vn(t,e,i,s){const n=Se(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]);const o=(i-e)/2,r=Math.min(o,s*e/2),a=t=>{const e=(i-Math.min(o,t))*s/2;return Dt(t,0,Math.min(o,e))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:Dt(n.innerStart,0,r),innerEnd:Dt(n.innerEnd,0,r)}}function wn(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function Mn(t,e,i,s,n,o){const{x:r,y:a,startAngle:h,pixelMargin:l,innerRadius:c}=e,d=Math.max(e.outerRadius+s+i-l,0),u=c>0?c+s+i+l:0;let f=0;const g=n-h;if(s){const t=((c>0?c-s:0)+(d>0?d-s:0))/2;f=(g-(0!==t?g*t/(t+s):g))/2}const p=(g-Math.max(.001,g*d-i/rt)/d)/2,m=h+p+f,x=n-p-f,{outerStart:b,outerEnd:y,innerStart:_,innerEnd:v}=vn(e,u,d,x-m),w=d-b,M=d-y,k=m+b/w,S=x-y/M,P=u+_,C=u+v,D=m+_/P,A=x-v/C;if(t.beginPath(),o){const e=(k+S)/2;if(t.arc(r,a,d,k,e),t.arc(r,a,d,e,S),y>0){const e=wn(M,S,r,a);t.arc(e.x,e.y,y,S,x+dt)}const i=wn(C,x,r,a);if(t.lineTo(i.x,i.y),v>0){const e=wn(C,A,r,a);t.arc(e.x,e.y,v,x+dt,A+Math.PI)}const s=(x-v/u+(m+_/u))/2;if(t.arc(r,a,u,x-v/u,s,!0),t.arc(r,a,u,s,m+_/u,!0),_>0){const e=wn(P,D,r,a);t.arc(e.x,e.y,_,D+Math.PI,m-dt)}const n=wn(w,m,r,a);if(t.lineTo(n.x,n.y),b>0){const e=wn(w,k,r,a);t.arc(e.x,e.y,b,m-dt,k)}}else{t.moveTo(r,a);const e=Math.cos(k)*d+r,i=Math.sin(k)*d+a;t.lineTo(e,i);const s=Math.cos(S)*d+r,n=Math.sin(S)*d+a;t.lineTo(s,n)}t.closePath()}function kn(t,e,i,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:h}=e,{borderWidth:l,borderJoinStyle:c,borderDash:d,borderDashOffset:u}=h,f="inner"===h.borderAlign;if(!l)return;t.setLineDash(d||[]),t.lineDashOffset=u,f?(t.lineWidth=2*l,t.lineJoin=c||"round"):(t.lineWidth=l,t.lineJoin=c||"bevel");let g=e.endAngle;if(o){Mn(t,e,i,s,g,n);for(let e=0;e<o;++e)t.stroke();isNaN(a)||(g=r+(a%at||at))}f&&function(t,e,i){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:h}=e;let l=n/a;t.beginPath(),t.arc(o,r,a,s-l,i+l),h>n?(l=n/h,t.arc(o,r,h,i+l,s-l,!0)):t.arc(o,r,n,i+dt,s-dt),t.closePath(),t.clip()}(t,e,g),o||(Mn(t,e,i,s,g,n),t.stroke())}class Sn extends Ls{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){const s=this.getProps(["x","y"],i),{angle:n,distance:o}=Mt(s,{x:t,y:e}),{startAngle:r,endAngle:a,innerRadius:h,outerRadius:l,circumference:c}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,u=N(c,a-r),f=Ct(n,r,a)&&r!==a,g=u>=at||f,p=At(o,h+d,l+d);return g&&p}getCenterPoint(t){const{x:e,y:i,startAngle:s,endAngle:n,innerRadius:o,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:a,spacing:h}=this.options,l=(s+n)/2,c=(o+r+h+a)/2;return{x:e+Math.cos(l)*c,y:i+Math.sin(l)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:i}=this,s=(e.offset||0)/4,n=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>at?Math.floor(i/at):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();const r=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(r)*s,Math.sin(r)*s);const a=s*(1-Math.sin(Math.min(rt,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,n){const{fullCircles:o,startAngle:r,circumference:a}=e;let h=e.endAngle;if(o){Mn(t,e,i,s,h,n);for(let e=0;e<o;++e)t.fill();isNaN(a)||(h=r+(a%at||at))}Mn(t,e,i,s,h,n),t.fill()}(t,this,a,n,o),kn(t,this,a,n,o),t.restore()}}function Pn(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;t.lineCap=N(i.borderCapStyle,e.borderCapStyle),t.setLineDash(N(i.borderDash,e.borderDash)),t.lineDashOffset=N(i.borderDashOffset,e.borderDashOffset),t.lineJoin=N(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=N(i.borderWidth,e.borderWidth),t.strokeStyle=N(i.borderColor,e.borderColor)}function Cn(t,e,i){t.lineTo(i.x,i.y)}function Dn(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=t.length,{start:n=0,end:o=s-1}=i,{start:r,end:a}=e,h=Math.max(n,r),l=Math.min(o,a),c=n<r&&o<r||n>a&&o>a;return{count:s,start:h,loop:e.loop,ilen:l<h&&!c?s+l-h:l-h}}function An(t,e,i,s){const{points:n,options:o}=e,{count:r,start:a,loop:h,ilen:l}=Dn(n,i,s),c=function(t){return t.stepped?me:t.tension||"monotone"===t.cubicInterpolationMode?xe:Cn}(o);let d,u,f,{move:g=!0,reverse:p}=s||{};for(d=0;d<=l;++d)u=n[(a+(p?l-d:d))%r],u.skip||(g?(t.moveTo(u.x,u.y),g=!1):c(t,f,u,p,o.stepped),f=u);return h&&(u=n[(a+(p?l:0))%r],c(t,f,u,p,o.stepped)),!!h}function On(t,e,i,s){const n=e.points,{count:o,start:r,ilen:a}=Dn(n,i,s),{move:h=!0,reverse:l}=s||{};let c,d,u,f,g,p,m=0,x=0;const b=t=>(r+(l?a-t:t))%o,y=()=>{f!==g&&(t.lineTo(m,g),t.lineTo(m,f),t.lineTo(m,p))};for(h&&(d=n[b(0)],t.moveTo(d.x,d.y)),c=0;c<=a;++c){if(d=n[b(c)],d.skip)continue;const e=d.x,i=d.y,s=0|e;s===u?(i<f?f=i:i>g&&(g=i),m=(x*m+e)/++x):(y(),t.lineTo(e,i),u=s,x=0,f=g=i),p=i}y()}function Tn(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return!t._decimated&&!t._loop&&!e.tension&&"monotone"!==e.cubicInterpolationMode&&!e.stepped&&!i?On:An}const Ln="function"===typeof Path2D;function Rn(t,e,i,s){Ln&&!e.options.segment?function(t,e,i,s){let n=e._path;n||(n=e._path=new Path2D,e.path(n,i,s)&&n.closePath()),Pn(t,e.options),t.stroke(n)}(t,e,i,s):function(t,e,i,s){const{segments:n,options:o}=e,r=Tn(e);for(const a of n)Pn(t,o,a.style),t.beginPath(),r(t,e,a,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}(t,e,i,s)}class In extends Ls{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;Ge(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){const i=t.points,s=t.options.spanGaps,n=i.length;if(!n)return[];const o=!!t._loop,{start:r,end:a}=function(t,e,i,s){let n=0,o=e-1;if(i&&!s)for(;n<e&&!t[n].skip;)n++;for(;n<e&&t[n].skip;)n++;for(n%=e,i&&(o+=n);o>n&&t[o%e].skip;)o--;return o%=e,{start:n,end:o}}(i,n,o,s);return _i(t,!0===s?[{start:r,end:a,loop:o}]:function(t,e,i,s){const n=t.length,o=[];let r,a=e,h=t[e];for(r=e+1;r<=i;++r){const i=t[r%n];i.skip||i.stop?h.skip||(s=!1,o.push({start:e%n,end:(r-1)%n,loop:s}),e=a=i.stop?r:null):(a=r,h.skip&&(e=r)),h=i}return null!==a&&o.push({start:e%n,end:a%n,loop:s}),o}(i,r,a<r?a+n:a,!!t._fullLoop&&0===r&&a===n-1),i,e)}(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,s=t[e],n=this.points,o=yi(this,{property:e,start:s,end:s});if(!o.length)return;const r=[],a=function(t){return t.stepped?di:t.tension||"monotone"===t.cubicInterpolationMode?ui:ci}(i);let h,l;for(h=0,l=o.length;h<l;++h){const{start:l,end:c}=o[h],d=n[l],u=n[c];if(d===u){r.push(d);continue}const f=a(d,u,Math.abs((s-d[e])/(u[e]-d[e])),i.stepped);f[e]=t[e],r.push(f)}return 1===r.length?r[0]:r}pathSegment(t,e,i){return Tn(this)(t,this,e,i)}path(t,e,i){const s=this.segments,n=Tn(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const r of s)o&=n(t,this,r,{start:e,end:e+i-1});return!!o}draw(t,e,i,s){const n=this.options||{};(this.points||[]).length&&n.borderWidth&&(t.save(),Rn(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function En(t,e,i,s){const n=t.options,{[i]:o}=t.getProps([i],s);return Math.abs(e-o)<n.radius+n.hitRadius}class zn extends Ls{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const s=this.options,{x:n,y:o}=this.getProps(["x","y"],i);return Math.pow(t-n,2)+Math.pow(e-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return En(this,t,"x",e)}inYRange(t,e){return En(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0;e=Math.max(e,e&&t.hoverRadius||0);return 2*(e+(e&&t.borderWidth||0))}draw(t,e){const i=this.options;this.skip||i.radius<.1||!fe(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,de(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function Fn(t,e){const{x:i,y:s,base:n,width:o,height:r}=t.getProps(["x","y","base","width","height"],e);let a,h,l,c,d;return t.horizontal?(d=r/2,a=Math.min(i,n),h=Math.max(i,n),l=s-d,c=s+d):(d=o/2,a=i-d,h=i+d,l=Math.min(s,n),c=Math.max(s,n)),{left:a,top:l,right:h,bottom:c}}function Wn(t,e,i,s){return t?0:Dt(e,i,s)}function Bn(t){const e=Fn(t),i=e.right-e.left,s=e.bottom-e.top,n=function(t,e,i){const s=t.options.borderWidth,n=t.borderSkipped,o=Pe(s);return{t:Wn(n.top,o.top,0,i),r:Wn(n.right,o.right,0,e),b:Wn(n.bottom,o.bottom,0,i),l:Wn(n.left,o.left,0,e)}}(t,i/2,s/2),o=function(t,e,i){const{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),n=t.options.borderRadius,o=Ce(n),r=Math.min(e,i),a=t.borderSkipped,h=s||H(n);return{topLeft:Wn(!h||a.top||a.left,o.topLeft,0,r),topRight:Wn(!h||a.top||a.right,o.topRight,0,r),bottomLeft:Wn(!h||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:Wn(!h||a.bottom||a.right,o.bottomRight,0,r)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:o},inner:{x:e.left+n.l,y:e.top+n.t,w:i-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Hn(t,e,i,s){const n=null===e,o=null===i,r=t&&!(n&&o)&&Fn(t,s);return r&&(n||At(e,r.left,r.right))&&(o||At(i,r.top,r.bottom))}function Vn(t,e){t.rect(e.x,e.y,e.w,e.h)}function jn(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=t.x!==i.x?-e:0,n=t.y!==i.y?-e:0,o=(t.x+t.w!==i.x+i.w?e:0)-s,r=(t.y+t.h!==i.y+i.h?e:0)-n;return{x:t.x+s,y:t.y+n,w:t.w+o,h:t.h+r,radius:t.radius}}class Nn extends Ls{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:s}}=this,{inner:n,outer:o}=Bn(this),r=(a=o.radius).topLeft||a.topRight||a.bottomLeft||a.bottomRight?ve:Vn;var a;t.save(),o.w===n.w&&o.h===n.h||(t.beginPath(),r(t,jn(o,e,n)),t.clip(),r(t,jn(n,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),r(t,jn(n,e)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,e,i){return Hn(this,t,e,i)}inXRange(t,e){return Hn(this,t,null,e)}inYRange(t,e){return Hn(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:s,horizontal:n}=this.getProps(["x","y","base","horizontal"],t);return{x:n?(e+s)/2:e,y:n?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}function $n(t,e,i,s){if(s)return;let n=e[t],o=i[t];return"angle"===t&&(n=Pt(n),o=Pt(o)),{property:t,start:n,end:o}}function Yn(t,e,i){for(;e>t;e--){const t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function Un(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function Xn(t,e){let i=[],s=!1;return B(t)?(s=!0,i=t):i=function(t,e){const{x:i=null,y:s=null}=t||{},n=e.points,o=[];return e.segments.forEach((t=>{let{start:e,end:r}=t;r=Yn(e,r,n);const a=n[e],h=n[r];null!==s?(o.push({x:a.x,y:s}),o.push({x:h.x,y:s})):null!==i&&(o.push({x:i,y:a.y}),o.push({x:i,y:h.y}))})),o}(t,e),i.length?new In({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}function qn(t){return t&&!1!==t.fill}function Kn(t,e,i){let s=t[e].fill;const n=[e];let o;if(!i)return s;for(;!1!==s&&-1===n.indexOf(s);){if(!V(s))return s;if(o=t[s],!o)return!1;if(o.visible)return s;n.push(s),s=o.fill}return!1}function Zn(t,e,i){const s=function(t){const e=t.options,i=e.fill;let s=N(i&&i.target,i);void 0===s&&(s=!!e.backgroundColor);if(!1===s||null===s)return!1;if(!0===s)return"origin";return s}(t);if(H(s))return!isNaN(s.value)&&s;let n=parseFloat(s);return V(n)&&Math.floor(n)===n?function(t,e,i,s){"-"!==t&&"+"!==t||(i=e+i);if(i===e||i<0||i>=s)return!1;return i}(s[0],e,n,i):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Gn(t,e,i){const s=[];for(let n=0;n<i.length;n++){const o=i[n],{first:r,last:a,point:h}=Jn(o,e,"x");if(!(!h||r&&a))if(r)s.unshift(h);else if(t.push(h),!a)break}t.push(...s)}function Jn(t,e,i){const s=t.interpolate(e,i);if(!s)return{};const n=s[i],o=t.segments,r=t.points;let a=!1,h=!1;for(let l=0;l<o.length;l++){const t=o[l],e=r[t.start][i],s=r[t.end][i];if(At(n,e,s)){a=n===e,h=n===s;break}}return{first:a,last:h,point:s}}class Qn{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:s,y:n,radius:o}=this;return e=e||{start:0,end:at},t.arc(s,n,o,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:s}=this,n=t.angle;return{x:e+Math.cos(n)*s,y:i+Math.sin(n)*s,angle:n}}}function to(t){const{chart:e,fill:i,line:s}=t;if(V(i))return function(t,e){const i=t.getDatasetMeta(e),s=i&&t.isDatasetVisible(e);return s?i.dataset:null}(e,i);if("stack"===i)return function(t){const{scale:e,index:i,line:s}=t,n=[],o=s.segments,r=s.points,a=function(t,e){const i=[],s=t.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const t=s[n];if(t.index===e)break;t.hidden||i.unshift(t.dataset)}return i}(e,i);a.push(Xn({x:null,y:e.bottom},s));for(let h=0;h<o.length;h++){const t=o[h];for(let e=t.start;e<=t.end;e++)Gn(n,r[e],a)}return new In({points:n,options:{}})}(t);if("shape"===i)return!0;const n=function(t){const e=t.scale||{};if(e.getPointPositionForValue)return function(t){const{scale:e,fill:i}=t,s=e.options,n=e.getLabels().length,o=s.reverse?e.max:e.min,r=function(t,e,i){let s;return s="start"===t?i:"end"===t?e.options.reverse?e.min:e.max:H(t)?t.value:e.getBaseValue(),s}(i,e,o),a=[];if(s.grid.circular){const t=e.getPointPositionForValue(0,o);return new Qn({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(r)})}for(let h=0;h<n;++h)a.push(e.getPointPositionForValue(h,r));return a}(t);return function(t){const{scale:e={},fill:i}=t,s=function(t,e){let i=null;return"start"===t?i=e.bottom:"end"===t?i=e.top:H(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i}(i,e);if(V(s)){const t=e.isHorizontal();return{x:t?s:null,y:t?null:s}}return null}(t)}(t);return n instanceof Qn?n:Xn(n,s)}function eo(t,e,i){const s=to(e),{chart:n,index:o,line:r,scale:a,axis:h}=e,l=r.options,c=l.fill,d=l.backgroundColor,{above:u=d,below:f=d}=c||{},g=n.getDatasetMeta(o),p=ki(n,g);s&&r.points.length&&(ge(t,i),function(t,e){const{line:i,target:s,above:n,below:o,area:r,scale:a,clip:h}=e,l=i._loop?"angle":e.axis;t.save(),"x"===l&&o!==n&&(io(t,s,r.top),so(t,{line:i,target:s,color:n,scale:a,property:l,clip:h}),t.restore(),t.save(),io(t,s,r.bottom));so(t,{line:i,target:s,color:o,scale:a,property:l,clip:h}),t.restore()}(t,{line:r,target:s,above:u,below:f,area:i,scale:a,axis:h,clip:p}),pe(t))}function io(t,e,i){const{segments:s,points:n}=e;let o=!0,r=!1;t.beginPath();for(const a of s){const{start:s,end:h}=a,l=n[s],c=n[Yn(s,h,n)];o?(t.moveTo(l.x,l.y),o=!1):(t.lineTo(l.x,i),t.lineTo(l.x,l.y)),r=!!e.pathSegment(t,a,{move:r}),r?t.closePath():t.lineTo(c.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function so(t,e){const{line:i,target:s,property:n,color:o,scale:r,clip:a}=e,h=function(t,e,i){const s=t.segments,n=t.points,o=e.points,r=[];for(const a of s){let{start:t,end:s}=a;s=Yn(t,s,n);const h=$n(i,n[t],n[s],a.loop);if(!e.segments){r.push({source:a,target:h,start:n[t],end:n[s]});continue}const l=yi(e,h);for(const e of l){const t=$n(i,o[e.start],o[e.end],e.loop),s=bi(a,n,t);for(const n of s)r.push({source:n,target:e,start:{[i]:Un(h,t,"start",Math.max)},end:{[i]:Un(h,t,"end",Math.min)}})}}return r}(i,s,n);for(const{source:l,target:c,start:d,end:u}of h){const{style:{backgroundColor:e=o}={}}=l,h=!0!==s;t.save(),t.fillStyle=e,no(t,r,a,h&&$n(n,d,u)),t.beginPath();const f=!!i.pathSegment(t,l);let g;if(h){f?t.closePath():oo(t,s,u,n);const e=!!s.pathSegment(t,c,{move:f,reverse:!0});g=f&&e,g||oo(t,s,d,n)}t.closePath(),t.fill(g?"evenodd":"nonzero"),t.restore()}}function no(t,e,i,s){const n=e.chart.chartArea,{property:o,start:r,end:a}=s||{};if("x"===o||"y"===o){let e,s,h,l;"x"===o?(e=r,s=n.top,h=a,l=n.bottom):(e=n.left,s=r,h=n.right,l=a),t.beginPath(),i&&(e=Math.max(e,i.left),h=Math.min(h,i.right),s=Math.max(s,i.top),l=Math.min(l,i.bottom)),t.rect(e,s,h-e,l-s),t.clip()}}function oo(t,e,i,s){const n=e.interpolate(i,s);n&&t.lineTo(n.x,n.y)}var ro={id:"filler",afterDatasetsUpdate(t,e,i){const s=(t.data.datasets||[]).length,n=[];let o,r,a,h;for(r=0;r<s;++r)o=t.getDatasetMeta(r),a=o.dataset,h=null,a&&a.options&&a instanceof In&&(h={visible:t.isDatasetVisible(r),index:r,fill:Zn(a,r,s),chart:t,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=h,n.push(h);for(r=0;r<s;++r)h=n[r],h&&!1!==h.fill&&(h.fill=Kn(n,r,i.propagate))},beforeDraw(t,e,i){const s="beforeDraw"===i.drawTime,n=t.getSortedVisibleDatasetMetas(),o=t.chartArea;for(let r=n.length-1;r>=0;--r){const e=n[r].$filler;e&&(e.line.updateControlPoints(o,e.axis),s&&e.fill&&eo(t.ctx,e,o))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;const s=t.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const e=s[n].$filler;qn(e)&&eo(t.ctx,e,t.chartArea)}},beforeDatasetDraw(t,e,i){const s=e.meta.$filler;qn(s)&&"beforeDatasetDraw"===i.drawTime&&eo(t.ctx,s,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ao=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}};class ho extends Ls{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=Y(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter((e=>t.filter(e,this.chart.data)))),t.sort&&(e=e.sort(((e,i)=>t.sort(e,i,this.chart.data)))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display)return void(this.width=this.height=0);const i=t.labels,s=Ae(i.font),n=s.size,o=this._computeTitleHeight(),{boxWidth:r,itemHeight:a}=ao(i,n);let h,l;e.font=s.string,this.isHorizontal()?(h=this.maxWidth,l=this._fitRows(o,n,r,a)+10):(l=this.maxHeight,h=this._fitCols(o,s,r,a)+10),this.width=Math.min(h,t.maxWidth||this.maxWidth),this.height=Math.min(l,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){const{ctx:n,maxWidth:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],h=this.lineWidths=[0],l=s+r;let c=t;n.textAlign="left",n.textBaseline="middle";let d=-1,u=-l;return this.legendItems.forEach(((t,f)=>{const g=i+e/2+n.measureText(t.text).width;(0===f||h[h.length-1]+g+2*r>o)&&(c+=l,h[h.length-(f>0?0:1)]=0,u+=l,d++),a[f]={left:0,top:u,row:d,width:g,height:s},h[h.length-1]+=g+r})),c}_fitCols(t,e,i,s){const{ctx:n,maxHeight:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],h=this.columnSizes=[],l=o-t;let c=r,d=0,u=0,f=0,g=0;return this.legendItems.forEach(((t,o)=>{const{itemWidth:p,itemHeight:m}=function(t,e,i,s,n){const o=function(t,e,i,s){let n=t.text;n&&"string"!==typeof n&&(n=n.reduce(((t,e)=>t.length>e.length?t:e)));return e+i.size/2+s.measureText(n).width}(s,t,e,i),r=function(t,e,i){let s=t;"string"!==typeof e.text&&(s=lo(e,i));return s}(n,s,e.lineHeight);return{itemWidth:o,itemHeight:r}}(i,e,n,t,s);o>0&&u+m+2*r>l&&(c+=d+r,h.push({width:d,height:u}),f+=d+r,g++,d=u=0),a[o]={left:f,top:u,col:g,width:p,height:m},d=Math.max(d,p),u+=m+r})),c+=d,h.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:n}}=this,o=fi(n,this.left,this.width);if(this.isHorizontal()){let n=0,r=Bt(i,this.left+s,this.right-this.lineWidths[n]);for(const a of e)n!==a.row&&(n=a.row,r=Bt(i,this.left+s,this.right-this.lineWidths[n])),a.top+=this.top+t+s,a.left=o.leftForLtr(o.x(r),a.width),r+=a.width+s}else{let n=0,r=Bt(i,this.top+t+s,this.bottom-this.columnSizes[n].height);for(const a of e)a.col!==n&&(n=a.col,r=Bt(i,this.top+t+s,this.bottom-this.columnSizes[n].height)),a.top=r,a.left+=this.left+s,a.left=o.leftForLtr(o.x(a.left),a.width),r+=a.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const t=this.ctx;ge(t,this),this._draw(),pe(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:n,labels:o}=t,r=re.color,a=fi(t.rtl,this.left,this.width),h=Ae(o.font),{padding:l}=o,c=h.size,d=c/2;let u;this.drawTitle(),s.textAlign=a.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=h.string;const{boxWidth:f,boxHeight:g,itemHeight:p}=ao(o,c),m=this.isHorizontal(),x=this._computeTitleHeight();u=m?{x:Bt(n,this.left+l,this.right-i[0]),y:this.top+l+x,line:0}:{x:this.left+l,y:Bt(n,this.top+x+l,this.bottom-e[0].height),line:0},gi(this.ctx,t.textDirection);const b=p+l;this.legendItems.forEach(((y,_)=>{s.strokeStyle=y.fontColor,s.fillStyle=y.fontColor;const v=s.measureText(y.text).width,w=a.textAlign(y.textAlign||(y.textAlign=o.textAlign)),M=f+d+v;let k=u.x,S=u.y;a.setWidth(this.width),m?_>0&&k+M+l>this.right&&(S=u.y+=b,u.line++,k=u.x=Bt(n,this.left+l,this.right-i[u.line])):_>0&&S+b>this.bottom&&(k=u.x=k+e[u.line].width+l,u.line++,S=u.y=Bt(n,this.top+x+l,this.bottom-e[u.line].height));if(function(t,e,i){if(isNaN(f)||f<=0||isNaN(g)||g<0)return;s.save();const n=N(i.lineWidth,1);if(s.fillStyle=N(i.fillStyle,r),s.lineCap=N(i.lineCap,"butt"),s.lineDashOffset=N(i.lineDashOffset,0),s.lineJoin=N(i.lineJoin,"miter"),s.lineWidth=n,s.strokeStyle=N(i.strokeStyle,r),s.setLineDash(N(i.lineDash,[])),o.usePointStyle){const r={radius:g*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:n},h=a.xPlus(t,f/2);ue(s,r,h,e+d,o.pointStyleWidth&&f)}else{const o=e+Math.max((c-g)/2,0),r=a.leftForLtr(t,f),h=Ce(i.borderRadius);s.beginPath(),Object.values(h).some((t=>0!==t))?ve(s,{x:r,y:o,w:f,h:g,radius:h}):s.rect(r,o,f,g),s.fill(),0!==n&&s.stroke()}s.restore()}(a.x(k),S,y),k=((t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e)(w,k+f+d,m?k+M:this.right,t.rtl),function(t,e,i){_e(s,i.text,t,e+p/2,h,{strikethrough:i.hidden,textAlign:a.textAlign(i.textAlign)})}(a.x(k),S,y),m)u.x+=M+l;else if("string"!==typeof y.text){const t=h.lineHeight;u.y+=lo(y,t)+l}else u.y+=b})),pi(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=Ae(e.font),s=De(e.padding);if(!e.display)return;const n=fi(t.rtl,this.left,this.width),o=this.ctx,r=e.position,a=i.size/2,h=s.top+a;let l,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),l=this.top+h,c=Bt(t.align,c,this.right-d);else{const e=this.columnSizes.reduce(((t,e)=>Math.max(t,e.height)),0);l=h+Bt(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const u=Bt(r,c,c+d);o.textAlign=n.textAlign(Wt(r)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,_e(o,e.text,u,l,i)}_computeTitleHeight(){const t=this.options.title,e=Ae(t.font),i=De(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,n;if(At(t,this.left,this.right)&&At(e,this.top,this.bottom))for(n=this.legendHitBoxes,i=0;i<n.length;++i)if(s=n[i],At(t,s.left,s.left+s.width)&&At(e,s.top,s.top+s.height))return this.legendItems[i];return null}handleEvent(t){const e=this.options;if(!function(t,e){if(("mousemove"===t||"mouseout"===t)&&(e.onHover||e.onLeave))return!0;if(e.onClick&&("click"===t||"mouseup"===t))return!0;return!1}(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){const o=this._hoveredItem,r=(n=i,null!==(s=o)&&null!==n&&s.datasetIndex===n.datasetIndex&&s.index===n.index);o&&!r&&Y(e.onLeave,[t,o,this],this),this._hoveredItem=i,i&&!r&&Y(e.onHover,[t,i,this],this)}else i&&Y(e.onClick,[t,i,this],this);var s,n}}function lo(t,e){return e*(t.text?t.text.length:0)}var co={id:"legend",_element:ho,start(t,e,i){const s=t.legend=new ho({ctx:t.ctx,options:i,chart:t});gs.configure(t,s,i),gs.addBox(t,s)},stop(t){gs.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const s=t.legend;gs.configure(t,s,i),s.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const s=e.datasetIndex,n=i.chart;n.isDatasetVisible(s)?(n.hide(s),e.hidden=!0):(n.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map((t=>{const h=t.controller.getStyle(i?0:void 0),l=De(h.borderWidth);return{text:e[t.index].label,fillStyle:h.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:h.borderCapStyle,lineDash:h.borderDash,lineDashOffset:h.borderDashOffset,lineJoin:h.borderJoinStyle,lineWidth:(l.width+l.height)/4,strokeStyle:h.borderColor,pointStyle:s||h.pointStyle,rotation:h.rotation,textAlign:n||h.textAlign,borderRadius:r&&(a||h.borderRadius),datasetIndex:t.index}}),this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class uo extends Ls{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=t,this.height=this.bottom=e;const s=B(i.text)?i.text.length:1;this._padding=De(i.padding);const n=s*Ae(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=n:this.width=n}isHorizontal(){const t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){const{top:e,left:i,bottom:s,right:n,options:o}=this,r=o.align;let a,h,l,c=0;return this.isHorizontal()?(h=Bt(r,i,n),l=e+t,a=n-i):("left"===o.position?(h=i+t,l=Bt(r,s,e),c=-.5*rt):(h=n-t,l=Bt(r,e,s),c=.5*rt),a=s-e),{titleX:h,titleY:l,maxWidth:a,rotation:c}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=Ae(e.font),s=i.lineHeight/2+this._padding.top,{titleX:n,titleY:o,maxWidth:r,rotation:a}=this._drawArgs(s);_e(t,e.text,0,0,i,{color:e.color,maxWidth:r,rotation:a,textAlign:Wt(e.align),textBaseline:"middle",translation:[n,o]})}}var fo={id:"title",_element:uo,start(t,e,i){!function(t,e){const i=new uo({ctx:t.ctx,options:e,chart:t});gs.configure(t,i,e),gs.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){const e=t.titleBlock;gs.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const s=t.titleBlock;gs.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};new WeakMap;const go={average(t){if(!t.length)return!1;let e,i,s=new Set,n=0,o=0;for(e=0,i=t.length;e<i;++e){const i=t[e].element;if(i&&i.hasValue()){const t=i.tooltipPosition();s.add(t.x),n+=t.y,++o}}if(0===o||0===s.size)return!1;return{x:[...s].reduce(((t,e)=>t+e))/s.size,y:n/o}},nearest(t,e){if(!t.length)return!1;let i,s,n,o=e.x,r=e.y,a=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){const s=t[i].element;if(s&&s.hasValue()){const t=kt(e,s.getCenterPoint());t<a&&(a=t,n=s)}}if(n){const t=n.tooltipPosition();o=t.x,r=t.y}return{x:o,y:r}}};function po(t,e){return e&&(B(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function mo(t){return("string"===typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function xo(t,e){const{element:i,datasetIndex:s,index:n}=e,o=t.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:t,label:r,parsed:o.getParsed(n),raw:t.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:i}}function bo(t,e){const i=t.chart.ctx,{body:s,footer:n,title:o}=t,{boxWidth:r,boxHeight:a}=e,h=Ae(e.bodyFont),l=Ae(e.titleFont),c=Ae(e.footerFont),d=o.length,u=n.length,f=s.length,g=De(e.padding);let p=g.height,m=0,x=s.reduce(((t,e)=>t+e.before.length+e.lines.length+e.after.length),0);if(x+=t.beforeBody.length+t.afterBody.length,d&&(p+=d*l.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),x){p+=f*(e.displayColors?Math.max(a,h.lineHeight):h.lineHeight)+(x-f)*h.lineHeight+(x-1)*e.bodySpacing}u&&(p+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let b=0;const y=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=l.string,U(t.title,y),i.font=h.string,U(t.beforeBody.concat(t.afterBody),y),b=e.displayColors?r+2+e.boxPadding:0,U(s,(t=>{U(t.before,y),U(t.lines,y),U(t.after,y)})),b=0,i.font=c.string,U(t.footer,y),i.restore(),m+=g.width,{width:m,height:p}}function yo(t,e,i,s){const{x:n,width:o}=i,{width:r,chartArea:{left:a,right:h}}=t;let l="center";return"center"===s?l=n<=(a+h)/2?"left":"right":n<=o/2?l="left":n>=r-o/2&&(l="right"),function(t,e,i,s){const{x:n,width:o}=s,r=i.caretSize+i.caretPadding;return"left"===t&&n+o+r>e.width||"right"===t&&n-o-r<0||void 0}(l,t,e,i)&&(l="center"),l}function _o(t,e,i){const s=i.yAlign||e.yAlign||function(t,e){const{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||yo(t,e,i,s),yAlign:s}}function vo(t,e,i,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=t,{xAlign:a,yAlign:h}=i,l=n+o,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=Ce(r);let g=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,a);const p=function(t,e,i){let{y:s,height:n}=t;return"top"===e?s+=i:s-="bottom"===e?n+i:n/2,s}(e,h,l);return"center"===h?"left"===a?g+=l:"right"===a&&(g-=l):"left"===a?g-=Math.max(c,u)+n:"right"===a&&(g+=Math.max(d,f)+n),{x:Dt(g,0,s.width-e.width),y:Dt(p,0,s.height-e.height)}}function wo(t,e,i){const s=De(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function Mo(t){return po([],mo(t))}function ko(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}const So={beforeTitle:z,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:z,beforeBody:z,beforeLabel:z,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return W(i)||(e+=i),e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:z,afterBody:z,beforeFooter:z,footer:z,afterFooter:z};function Po(t,e,i,s){const n=t[e].call(i,s);return"undefined"===typeof n?So[e].call(i,s):n}class Co extends Ls{static positioners=(()=>go)();constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,n=new Oi(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(n)),n}getContext(){return this.$context||(this.$context=(t=this.chart.getContext(),e=this,i=this._tooltipItems,Te(t,{tooltip:e,tooltipItems:i,type:"tooltip"})));var t,e,i}getTitle(t,e){const{callbacks:i}=e,s=Po(i,"beforeTitle",this,t),n=Po(i,"title",this,t),o=Po(i,"afterTitle",this,t);let r=[];return r=po(r,mo(s)),r=po(r,mo(n)),r=po(r,mo(o)),r}getBeforeBody(t,e){return Mo(Po(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,s=[];return U(t,(t=>{const e={before:[],lines:[],after:[]},n=ko(i,t);po(e.before,mo(Po(n,"beforeLabel",this,t))),po(e.lines,Po(n,"label",this,t)),po(e.after,mo(Po(n,"afterLabel",this,t))),s.push(e)})),s}getAfterBody(t,e){return Mo(Po(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,s=Po(i,"beforeFooter",this,t),n=Po(i,"footer",this,t),o=Po(i,"afterFooter",this,t);let r=[];return r=po(r,mo(s)),r=po(r,mo(n)),r=po(r,mo(o)),r}_createItems(t){const e=this._active,i=this.chart.data,s=[],n=[],o=[];let r,a,h=[];for(r=0,a=e.length;r<a;++r)h.push(xo(this.chart,e[r]));return t.filter&&(h=h.filter(((e,s,n)=>t.filter(e,s,n,i)))),t.itemSort&&(h=h.sort(((e,s)=>t.itemSort(e,s,i)))),U(h,(e=>{const i=ko(t.callbacks,e);s.push(Po(i,"labelColor",this,e)),n.push(Po(i,"labelPointStyle",this,e)),o.push(Po(i,"labelTextColor",this,e))})),this.labelColors=s,this.labelPointStyles=n,this.labelTextColors=o,this.dataPoints=h,h}update(t,e){const i=this.options.setContext(this.getContext()),s=this._active;let n,o=[];if(s.length){const t=go[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const e=this._size=bo(this,i),r=Object.assign({},t,e),a=_o(this.chart,i,r),h=vo(i,r,a,this.chart);this.xAlign=a.xAlign,this.yAlign=a.yAlign,n={opacity:1,x:h.x,y:h.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(n={opacity:0});this._tooltipItems=o,this.$context=void 0,n&&this._resolveAnimations().update(this,n),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){const n=this.getCaretPosition(t,i,s);e.lineTo(n.x1,n.y1),e.lineTo(n.x2,n.y2),e.lineTo(n.x3,n.y3)}getCaretPosition(t,e,i){const{xAlign:s,yAlign:n}=this,{caretSize:o,cornerRadius:r}=i,{topLeft:a,topRight:h,bottomLeft:l,bottomRight:c}=Ce(r),{x:d,y:u}=t,{width:f,height:g}=e;let p,m,x,b,y,_;return"center"===n?(y=u+g/2,"left"===s?(p=d,m=p-o,b=y+o,_=y-o):(p=d+f,m=p+o,b=y-o,_=y+o),x=p):(m="left"===s?d+Math.max(a,l)+o:"right"===s?d+f-Math.max(h,c)-o:this.caretX,"top"===n?(b=u,y=b-o,p=m-o,x=m+o):(b=u+g,y=b+o,p=m+o,x=m-o),_=b),{x1:p,x2:m,x3:x,y1:b,y2:y,y3:_}}drawTitle(t,e,i){const s=this.title,n=s.length;let o,r,a;if(n){const h=fi(i.rtl,this.x,this.width);for(t.x=wo(this,i.titleAlign,i),e.textAlign=h.textAlign(i.titleAlign),e.textBaseline="middle",o=Ae(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,a=0;a<n;++a)e.fillText(s[a],h.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+r,a+1===n&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,s,n){const o=this.labelColors[i],r=this.labelPointStyles[i],{boxHeight:a,boxWidth:h}=n,l=Ae(n.bodyFont),c=wo(this,"left",n),d=s.x(c),u=a<l.lineHeight?(l.lineHeight-a)/2:0,f=e.y+u;if(n.usePointStyle){const e={radius:Math.min(h,a)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},i=s.leftForLtr(d,h)+h/2,l=f+a/2;t.strokeStyle=n.multiKeyBackground,t.fillStyle=n.multiKeyBackground,de(t,e,i,l),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,de(t,e,i,l)}else{t.lineWidth=H(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const e=s.leftForLtr(d,h),i=s.leftForLtr(s.xPlus(d,1),h-2),r=Ce(o.borderRadius);Object.values(r).some((t=>0!==t))?(t.beginPath(),t.fillStyle=n.multiKeyBackground,ve(t,{x:e,y:f,w:h,h:a,radius:r}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),ve(t,{x:i,y:f+1,w:h-2,h:a-2,radius:r}),t.fill()):(t.fillStyle=n.multiKeyBackground,t.fillRect(e,f,h,a),t.strokeRect(e,f,h,a),t.fillStyle=o.backgroundColor,t.fillRect(i,f+1,h-2,a-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:s}=this,{bodySpacing:n,bodyAlign:o,displayColors:r,boxHeight:a,boxWidth:h,boxPadding:l}=i,c=Ae(i.bodyFont);let d=c.lineHeight,u=0;const f=fi(i.rtl,this.x,this.width),g=function(i){e.fillText(i,f.x(t.x+u),t.y+d/2),t.y+=d+n},p=f.textAlign(o);let m,x,b,y,_,v,w;for(e.textAlign=o,e.textBaseline="middle",e.font=c.string,t.x=wo(this,p,i),e.fillStyle=i.bodyColor,U(this.beforeBody,g),u=r&&"right"!==p?"center"===o?h/2+l:h+2+l:0,y=0,v=s.length;y<v;++y){for(m=s[y],x=this.labelTextColors[y],e.fillStyle=x,U(m.before,g),b=m.lines,r&&b.length&&(this._drawColorBox(e,t,y,f,i),d=Math.max(c.lineHeight,a)),_=0,w=b.length;_<w;++_)g(b[_]),d=c.lineHeight;U(m.after,g)}u=0,d=c.lineHeight,U(this.afterBody,g),t.y-=n}drawFooter(t,e,i){const s=this.footer,n=s.length;let o,r;if(n){const a=fi(i.rtl,this.x,this.width);for(t.x=wo(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=a.textAlign(i.footerAlign),e.textBaseline="middle",o=Ae(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,r=0;r<n;++r)e.fillText(s[r],a.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){const{xAlign:n,yAlign:o}=this,{x:r,y:a}=t,{width:h,height:l}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=Ce(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(r+c,a),"top"===o&&this.drawCaret(t,e,i,s),e.lineTo(r+h-d,a),e.quadraticCurveTo(r+h,a,r+h,a+d),"center"===o&&"right"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+h,a+l-f),e.quadraticCurveTo(r+h,a+l,r+h-f,a+l),"bottom"===o&&this.drawCaret(t,e,i,s),e.lineTo(r+u,a+l),e.quadraticCurveTo(r,a+l,r,a+l-u),"center"===o&&"left"===n&&this.drawCaret(t,e,i,s),e.lineTo(r,a+c),e.quadraticCurveTo(r,a,r+c,a),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,s=i&&i.x,n=i&&i.y;if(s||n){const i=go[t.position].call(this,this._active,this._eventPosition);if(!i)return;const o=this._size=bo(this,t),r=Object.assign({},i,this._size),a=_o(e,t,r),h=vo(t,r,a,e);s._to===h.x&&n._to===h.y||(this.xAlign=a.xAlign,this.yAlign=a.yAlign,this.width=o.width,this.height=o.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},n={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=De(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=i,this.drawBackground(n,t,s,e),gi(t,e.textDirection),n.y+=o.top,this.drawTitle(n,t,e),this.drawBody(n,t,e),this.drawFooter(n,t,e),pi(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,s=t.map((t=>{let{datasetIndex:e,index:i}=t;const s=this.chart.getDatasetMeta(e);if(!s)throw new Error("Cannot find a dataset at index "+e);return{datasetIndex:e,element:s.data[i],index:i}})),n=!X(i,s),o=this._positionChanged(s,e);(n||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,n=this._active||[],o=this._getActiveElements(t,n,e,i),r=this._positionChanged(o,t),a=e||!X(o,n)||r;return a&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),a}_getActiveElements(t,e,i,s){const n=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter((t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index)));const o=this.chart.getElementsAtEventForMode(t,n.mode,n,i);return n.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:s,options:n}=this,o=go[n.position].call(this,t,e);return!1!==o&&(i!==o.x||s!==o.y)}}var Do={id:"tooltip",_element:Co,positioners:go,afterInit(t,e,i){i&&(t.tooltip=new Co({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:So},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};function Ao(t,e,i,s){const n=t.indexOf(e);if(-1===n)return((t,e,i,s)=>("string"===typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i))(t,e,i,s);return n!==t.lastIndexOf(e)?i:n}function Oo(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class To extends js{static id="category";static defaults=(()=>({ticks:{callback:Oo}}))();constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(W(t))return null;const i=this.getLabels();return((t,e)=>null===t?null:Dt(Math.round(t),0,e))(e=isFinite(e)&&i[e]===t?e:Ao(i,t,N(e,t),this._addedLabels),i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,s=[];let n=this.getLabels();n=0===t&&e===n.length-1?n:n.slice(t,e+1),this._valueRange=Math.max(n.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){return Oo.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!==typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function Lo(t,e){const i=[],{bounds:s,step:n,min:o,max:r,precision:a,count:h,maxTicks:l,maxDigits:c,includeBounds:d}=t,u=n||1,f=l-1,{min:g,max:p}=e,m=!W(o),x=!W(r),b=!W(h),y=(p-g)/(c+1);let _,v,w,M,k=xt((p-g)/f/u)*u;if(k<1e-14&&!m&&!x)return[{value:g},{value:p}];M=Math.ceil(p/k)-Math.floor(g/k),M>f&&(k=xt(M*k/f/u)*u),W(a)||(_=Math.pow(10,a),k=Math.ceil(k*_)/_),"ticks"===s?(v=Math.floor(g/k)*k,w=Math.ceil(p/k)*k):(v=g,w=p),m&&x&&n&&function(t,e){const i=Math.round(t);return i-e<=t&&i+e>=t}((r-o)/n,k/1e3)?(M=Math.round(Math.min((r-o)/k,l)),k=(r-o)/M,v=o,w=r):b?(v=m?o:v,w=x?r:w,M=h-1,k=(w-v)/M):(M=(w-v)/k,M=mt(M,Math.round(M),k/1e3)?Math.round(M):Math.ceil(M));const S=Math.max(wt(k),wt(v));_=Math.pow(10,W(a)?S:a),v=Math.round(v*_)/_,w=Math.round(w*_)/_;let P=0;for(m&&(d&&v!==o?(i.push({value:o}),v<o&&P++,mt(Math.round((v+P*k)*_)/_,o,Ro(o,y,t))&&P++):v<o&&P++);P<M;++P){const t=Math.round((v+P*k)*_)/_;if(x&&t>r)break;i.push({value:t})}return x&&d&&w!==r?i.length&&mt(i[i.length-1].value,r,Ro(r,y,t))?i[i.length-1].value=r:i.push({value:r}):x&&w!==r||i.push({value:w}),i}function Ro(t,e,i){let{horizontal:s,minRotation:n}=i;const o=_t(n),r=(s?Math.sin(o):Math.cos(o))||.001,a=.75*e*(""+t).length;return Math.min(e/r,a)}class Io extends js{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return W(t)||("number"===typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:s,max:n}=this;const o=t=>s=e?s:t,r=t=>n=i?n:t;if(t){const t=pt(s),e=pt(n);t<0&&e<0?r(0):t>0&&e>0&&o(0)}if(s===n){let e=0===n?1:Math.abs(.05*n);r(n+e),t||o(s-e)}this.min=s,this.max=n}getTickLimit(){const t=this.options.ticks;let e,{maxTicksLimit:i,stepSize:s}=t;return s?(e=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,e>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${e} ticks. Limiting to 1000.`),e=1e3)):(e=this.computeTickLimit(),i=i||11),i&&(e=Math.min(i,e)),e}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s=Lo({maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&yt(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Jt(t,this.chart.options.locale,this.options.ticks.format)}}class Eo extends Io{static id="linear";static defaults=(()=>({ticks:{callback:te.formatters.numeric}}))();determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=V(t)?t:0,this.max=V(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=_t(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,n=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,n.lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}te.formatters.logarithmic;function zo(t){const e=t.ticks;if(e.display&&t.display){const t=De(e.backdropPadding);return N(e.font&&e.font.size,re.font.size)+t.height}return 0}function Fo(t,e,i,s,n){return t===s||t===n?{start:e-i/2,end:e+i/2}:t<s||t>n?{start:e-i,end:e}:{start:e,end:e+i}}function Wo(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],n=[],o=t._pointLabels.length,r=t.options.pointLabels,a=r.centerPointLabels?rt/o:0;for(let d=0;d<o;d++){const o=r.setContext(t.getPointLabelContext(d));n[d]=o.padding;const u=t.getPointPosition(d,t.drawingArea+n[d],a),f=Ae(o.font),g=(h=t.ctx,l=f,c=B(c=t._pointLabels[d])?c:[c],{w:he(h,l.string,c),h:c.length*l.lineHeight});s[d]=g;const p=Pt(t.getIndexAngle(d)+a),m=Math.round(vt(p));Bo(i,e,p,Fo(m,u.x,g.w,0,180),Fo(m,u.y,g.h,90,270))}var h,l,c;t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){const s=[],n=t._pointLabels.length,o=t.options,{centerPointLabels:r,display:a}=o.pointLabels,h={extra:zo(o)/2,additionalAngle:r?rt/n:0};let l;for(let c=0;c<n;c++){h.padding=i[c],h.size=e[c];const n=Ho(t,c,h);s.push(n),"auto"===a&&(n.visible=Vo(n,l),n.visible&&(l=n))}return s}(t,s,n)}function Bo(t,e,i,s,n){const o=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i));let a=0,h=0;s.start<e.l?(a=(e.l-s.start)/o,t.l=Math.min(t.l,e.l-a)):s.end>e.r&&(a=(s.end-e.r)/o,t.r=Math.max(t.r,e.r+a)),n.start<e.t?(h=(e.t-n.start)/r,t.t=Math.min(t.t,e.t-h)):n.end>e.b&&(h=(n.end-e.b)/r,t.b=Math.max(t.b,e.b+h))}function Ho(t,e,i){const s=t.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=i,h=t.getPointPosition(e,s+n+r,o),l=Math.round(vt(Pt(h.angle+dt))),c=function(t,e,i){90===i||270===i?t-=e/2:(i>270||i<90)&&(t-=e);return t}(h.y,a.h,l),d=function(t){if(0===t||180===t)return"center";if(t<180)return"left";return"right"}(l),u=function(t,e,i){"right"===i?t-=e:"center"===i&&(t-=e/2);return t}(h.x,a.w,d);return{visible:!0,x:h.x,y:c,textAlign:d,left:u,top:c,right:u+a.w,bottom:c+a.h}}function Vo(t,e){if(!e)return!0;const{left:i,top:s,right:n,bottom:o}=t;return!(fe({x:i,y:s},e)||fe({x:i,y:o},e)||fe({x:n,y:s},e)||fe({x:n,y:o},e))}function jo(t,e,i){const{left:s,top:n,right:o,bottom:r}=i,{backdropColor:a}=e;if(!W(a)){const i=Ce(e.borderRadius),h=De(e.backdropPadding);t.fillStyle=a;const l=s-h.left,c=n-h.top,d=o-s+h.width,u=r-n+h.height;Object.values(i).some((t=>0!==t))?(t.beginPath(),ve(t,{x:l,y:c,w:d,h:u,radius:i}),t.fill()):t.fillRect(l,c,d,u)}}function No(t,e,i,s){const{ctx:n}=t;if(i)n.arc(t.xCenter,t.yCenter,e,0,at);else{let i=t.getPointPosition(0,e);n.moveTo(i.x,i.y);for(let o=1;o<s;o++)i=t.getPointPosition(o,e),n.lineTo(i.x,i.y)}}class $o extends Io{static id="radialLinear";static defaults=(()=>({display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:te.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}}))();static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=De(zo(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=V(t)&&!isNaN(t)?t:0,this.max=V(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/zo(this.options))}generateTickLabels(t){Io.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map(((t,e)=>{const i=Y(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""})).filter(((t,e)=>this.chart.getDataVisibility(e)))}fit(){const t=this.options;t.display&&t.pointLabels.display?Wo(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return Pt(t*(at/(this._pointLabels.length||1))+_t(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(W(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(W(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return function(t,e,i){return Te(t,{label:i,index:e,type:"pointLabel"})}(this.getContext(),t,i)}}getPointPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const s=this.getIndexAngle(t)-dt+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:s,bottom:n}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:n}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),No(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:s,border:n}=e,o=this._pointLabels.length;let r,a,h;if(e.pointLabels.display&&function(t,e){const{ctx:i,options:{pointLabels:s}}=t;for(let n=e-1;n>=0;n--){const e=t._pointLabelItems[n];if(!e.visible)continue;const o=s.setContext(t.getPointLabelContext(n));jo(i,o,e);const r=Ae(o.font),{x:a,y:h,textAlign:l}=e;_e(i,t._pointLabels[n],a,h+r.lineHeight/2,r,{color:o.color,textAlign:l,textBaseline:"middle"})}}(this,o),s.display&&this.ticks.forEach(((t,e)=>{if(0!==e||0===e&&this.min<0){a=this.getDistanceFromCenterForValue(t.value);const i=this.getContext(e),r=s.setContext(i),h=n.setContext(i);!function(t,e,i,s,n){const o=t.ctx,r=e.circular,{color:a,lineWidth:h}=e;!r&&!s||!a||!h||i<0||(o.save(),o.strokeStyle=a,o.lineWidth=h,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),No(t,i,r,s),o.closePath(),o.stroke(),o.restore())}(this,r,a,o,h)}})),i.display){for(t.save(),r=o-1;r>=0;r--){const s=i.setContext(this.getPointLabelContext(r)),{color:n,lineWidth:o}=s;o&&n&&(t.lineWidth=o,t.strokeStyle=n,t.setLineDash(s.borderDash),t.lineDashOffset=s.borderDashOffset,a=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),h=this.getPointPosition(r,a),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(h.x,h.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let n,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach(((s,r)=>{if(0===r&&this.min>=0&&!e.reverse)return;const a=i.setContext(this.getContext(r)),h=Ae(a.font);if(n=this.getDistanceFromCenterForValue(this.ticks[r].value),a.showLabelBackdrop){t.font=h.string,o=t.measureText(s.label).width,t.fillStyle=a.backdropColor;const e=De(a.backdropPadding);t.fillRect(-o/2-e.left,-n-h.size/2-e.top,o+e.width,h.size+e.height)}_e(t,s.label,0,-n,h,{color:a.color,strokeColor:a.textStrokeColor,strokeWidth:a.textStrokeWidth})})),t.restore()}drawTitle(){}}const Yo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Uo=Object.keys(Yo);function Xo(t,e){return t-e}function qo(t,e){if(W(e))return null;const i=t._adapter,{parser:s,round:n,isoWeekday:o}=t._parseOpts;let r=e;return"function"===typeof s&&(r=s(r)),V(r)||(r="string"===typeof s?i.parse(r,s):i.parse(r)),null===r?null:(n&&(r="week"!==n||!bt(o)&&!0!==o?i.startOf(r,n):i.startOf(r,"isoWeek",o)),+r)}function Ko(t,e,i,s){const n=Uo.length;for(let o=Uo.indexOf(t);o<n-1;++o){const t=Yo[Uo[o]],n=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(n*t.size))<=s)return Uo[o]}return Uo[n-1]}function Zo(t,e,i){if(i){if(i.length){const{lo:s,hi:n}=Ot(i,e);t[i[s]>=e?i[s]:i[n]]=!0}}else t[e]=!0}function Go(t,e,i){const s=[],n={},o=e.length;let r,a;for(r=0;r<o;++r)a=e[r],n[a]=r,s.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){const n=t._adapter,o=+n.startOf(e[0].value,s),r=e[e.length-1].value;let a,h;for(a=o;a<=r;a=+n.add(a,1,s))h=i[a],h>=0&&(e[h].major=!0);return e}(t,s,n,i):s}class Jo extends js{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=t.time||(t.time={}),s=this._adapter=new qi(t.adapters.date);s.init(e),J(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:qo(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:s,max:n,minDefined:o,maxDefined:r}=this.getUserBounds();function a(t){o||isNaN(t.min)||(s=Math.min(s,t.min)),r||isNaN(t.max)||(n=Math.max(n,t.max))}o&&r||(a(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source||a(this.getMinMax(!1))),s=V(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),n=V(n)&&!isNaN(n)?n:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,n-1),this.max=Math.max(s+1,n)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const n=this.min,o=function(t,e,i){let s=0,n=t.length;for(;s<n&&t[s]<e;)s++;for(;n>s&&t[n-1]>i;)n--;return s>0||n<t.length?t.slice(s,n):t}(s,n,this.max);return this._unit=e.unit||(i.autoSkip?Ko(e.minUnit,this.min,this.max,this._getLabelCapacity(n)):function(t,e,i,s,n){for(let o=Uo.length-1;o>=Uo.indexOf(i);o--){const i=Uo[o];if(Yo[i].common&&t._adapter.diff(n,s,i)>=e-1)return i}return Uo[i?Uo.indexOf(i):0]}(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=Uo.indexOf(t)+1,i=Uo.length;e<i;++e)if(Yo[Uo[e]].common)return Uo[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&o.reverse(),Go(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map((t=>+t.value)))}initOffsets(){let t,e,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],s=0,n=0;this.options.offset&&i.length&&(t=this.getDecimalForValue(i[0]),s=1===i.length?1-t:(this.getDecimalForValue(i[1])-t)/2,e=this.getDecimalForValue(i[i.length-1]),n=1===i.length?e:(e-this.getDecimalForValue(i[i.length-2]))/2);const o=i.length<3?.5:.25;s=Dt(s,0,o),n=Dt(n,0,o),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){const t=this._adapter,e=this.min,i=this.max,s=this.options,n=s.time,o=n.unit||Ko(n.minUnit,e,i,this._getLabelCapacity(e)),r=N(s.ticks.stepSize,1),a="week"===o&&n.isoWeekday,h=bt(a)||!0===a,l={};let c,d,u=e;if(h&&(u=+t.startOf(u,"isoWeek",a)),u=+t.startOf(u,h?"day":o),t.diff(i,e,o)>1e5*r)throw new Error(e+" and "+i+" are too far apart with stepSize of "+r+" "+o);const f="data"===s.ticks.source&&this.getDataTimestamps();for(c=u,d=0;c<i;c=+t.add(c,r,o),d++)Zo(l,c,f);return c!==i&&"ticks"!==s.bounds&&1!==d||Zo(l,c,f),Object.keys(l).sort(Xo).map((t=>+t))}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const i=this.options.time.displayFormats,s=this._unit,n=e||i[s];return this._adapter.format(t,n)}_tickFormatFunction(t,e,i,s){const n=this.options,o=n.ticks.callback;if(o)return Y(o,[t,e,i],this);const r=n.time.displayFormats,a=this._unit,h=this._majorUnit,l=a&&r[a],c=h&&r[h],d=i[e],u=h&&c&&d&&d.major;return this._adapter.format(t,s||(u?c:l))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,s=_t(this.isHorizontal()?e.maxRotation:e.minRotation),n=Math.cos(s),o=Math.sin(s),r=this._resolveTickFontOptions(0).size;return{w:i*n+r*o,h:i*o+r*n}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,n=this._tickFormatFunction(t,0,Go(this,[t],this._majorUnit),s),o=this._getLabelSize(n),r=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(qo(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Et(t.sort(Xo))}}},9531:(t,e,i)=>{i.d(e,{A:()=>o});var s=i(5043);function n(t,e){let{title:i,titleId:n,...o}=t;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":n},o),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const o=s.forwardRef(n)}}]);
//# sourceMappingURL=182.e00b4816.chunk.js.map