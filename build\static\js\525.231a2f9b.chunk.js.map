{"version": 3, "file": "static/js/525.231a2f9b.chunk.js", "mappings": "6MAYA,MAAMA,EAA+BC,IAC5B,CACLC,GAAID,EAASC,GACbC,KAAMF,EAASG,YACfC,SAAUJ,EAASI,SACnBC,UAAWL,EAASK,UACpBC,YAAa,eAAeN,EAASO,YACrCC,IAAKR,EAASO,YAOLE,EAAuBC,IAClC,MAAMC,EAAcC,GACbA,EACE,IAAIC,KAAKD,GAAYE,eADJ,IAAID,MAAOC,cAIrC,MAAO,CACLb,GAAIS,EAAUT,GACdc,aAAcL,EAAUK,aACxBC,aAAcN,EAAUM,aACxBC,YAAaP,EAAUO,YACvBC,OAA6B,cAArBR,EAAUQ,OAAyB,WAAaR,EAAUQ,OAClEC,UAAWR,EAAWD,EAAUS,WAChCC,aAAcT,EAAWD,EAAUU,cACnCC,MAAOX,EAAUW,MAAQX,EAAUW,MAAMC,IAAIvB,GAA+B,GAC5EwB,MAAOb,EAAUa,SACbb,EAAUc,iBAAmB,CAC/BA,gBAAiB,CACfC,OAAQf,EAAUc,gBAAgBC,OAClCC,KAAMhB,EAAUc,gBAAgBE,KAChCC,MAAOjB,EAAUc,gBAAgBG,MACjCC,WAAYlB,EAAUc,gBAAgBK,QACtCC,QAASpB,EAAUc,gBAAgBM,UAGxC,EAMUC,EAAgBA,IACpBC,EAAAA,EAAWV,IAAIb,GAMXwB,EAAoBhC,IAE/B,MAAMiC,EAAWC,OAAOlC,GAGlBS,EAAYsB,EAAAA,EAAWI,MAAKC,GAChCA,EAAEC,cAAgBJ,GAClBG,EAAEpC,KAAOiC,GACTC,OAAOE,EAAEpC,MAAQiC,IAEnB,GAAKxB,EACL,OAAOD,EAAoBC,EAAU,C", "sources": ["features/orders/utils/orderMappers.ts"], "sourcesContent": ["/**\r\n * Order Mappers\r\n *\r\n * Utility functions to map between different order data formats\r\n */\r\n\r\nimport type{ Order, OrderItem } from '../types/index';\r\nimport { orders as mockOrders, type OrderItem as MockOrderItem } from '../../../mockData/entities/orders';\r\n\r\n/**\r\n * Maps a mock order item to the application order item format\r\n */\r\nconst mapMockOrderItemToOrderItem = (mockItem: MockOrderItem): OrderItem => {\r\n  return {\r\n    id: mockItem.id,\r\n    name: mockItem.productName,\r\n    quantity: mockItem.quantity,\r\n    unitPrice: mockItem.unitPrice,\r\n    description: `Product ID: ${mockItem.productId}`,\r\n    sku: mockItem.productId\r\n  };\r\n};\r\n\r\n/**\r\n * Maps a mock order to the application order format\r\n */\r\nexport const mapMockOrderToOrder = (mockOrder: any): Order => {\r\n  const formatDate = (dateString?: string): string => {\r\n    if (!dateString) return new Date().toISOString();\r\n    return new Date(dateString).toISOString();\r\n  };\r\n\r\n  return {\r\n    id: mockOrder.id,\r\n    customerName: mockOrder.customerName,\r\n    supplierName: mockOrder.supplierName,\r\n    totalAmount: mockOrder.totalAmount,\r\n    status: mockOrder.status === 'cancelled' ? 'rejected' : mockOrder.status,\r\n    orderDate: formatDate(mockOrder.orderDate),\r\n    deliveryDate: formatDate(mockOrder.deliveryDate),\r\n    items: mockOrder.items ? mockOrder.items.map(mapMockOrderItemToOrderItem) : [],\r\n    notes: mockOrder.notes,\r\n    ...(mockOrder.shippingAddress && {\r\n      shippingAddress: {\r\n        street: mockOrder.shippingAddress.street,\r\n        city: mockOrder.shippingAddress.city,\r\n        state: mockOrder.shippingAddress.state,\r\n        postalCode: mockOrder.shippingAddress.zipCode,\r\n        country: mockOrder.shippingAddress.country\r\n      }\r\n    })\r\n  };\r\n};\r\n\r\n/**\r\n * Get all orders from mock data\r\n */\r\nexport const getMockOrders = (): Order[] => {\r\n  return mockOrders.map(mapMockOrderToOrder);\r\n};\r\n\r\n/**\r\n * Get an order by ID from mock data\r\n */\r\nexport const getMockOrderById = (id: string | number): Order | undefined => {\r\n  // Convert id to string for consistent comparison\r\n  const searchId = String(id);\r\n\r\n  // Try to find by orderNumber first, then by id\r\n  const mockOrder = mockOrders.find(o =>\r\n    o.orderNumber === searchId ||\r\n    o.id === searchId ||\r\n    String(o.id) === searchId\r\n  );\r\n  if (!mockOrder) return undefined;\r\n  return mapMockOrderToOrder(mockOrder);\r\n};\r\n\r\nexport default {\r\n  mapMockOrderToOrder,\r\n  getMockOrders,\r\n  getMockOrderById\r\n};\r\n"], "names": ["mapMockOrderItemToOrderItem", "mockItem", "id", "name", "productName", "quantity", "unitPrice", "description", "productId", "sku", "mapMockOrderToOrder", "mockOrder", "formatDate", "dateString", "Date", "toISOString", "customerName", "supplierName", "totalAmount", "status", "orderDate", "deliveryDate", "items", "map", "notes", "shippingAddress", "street", "city", "state", "postalCode", "zipCode", "country", "getMockOrders", "mockOrders", "getMockOrderById", "searchId", "String", "find", "o", "orderNumber"], "sourceRoot": ""}