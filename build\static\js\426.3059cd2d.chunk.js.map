{"version": 3, "file": "static/js/426.3059cd2d.chunk.js", "mappings": "4MAoBA,MAAMA,EAAsCC,IAKrC,IALsC,OAC3CC,EAAM,aACNC,EAAY,MACZC,EAAQ,SAAQ,QAChBC,GAAU,GACXJ,EAEC,MAAMK,GAA2BC,EAAAA,EAAAA,UAAQ,IAAM,CAC7C,CACEC,IAAK,KACLC,MAAO,WACPC,UAAU,EACVC,OAASC,IACPC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yBAAwBC,SAAEH,KAG9C,CAAEJ,IAAK,eAAgBC,MAAO,WAAYC,UAAU,GACpD,CAAEF,IAAK,eAAgBC,MAAO,WAAYC,UAAU,GACpD,CACEF,IAAK,cACLC,MAAO,eACPC,UAAU,EACVC,OAASC,IAAkBI,EAAAA,EAAAA,IAAeJ,IAE5C,CACEJ,IAAK,SACLC,MAAO,SACPC,UAAU,EACVC,OAASC,IACPC,EAAAA,EAAAA,KAACI,EAAAA,EAAW,CAACC,OAAQN,GAAS,UAAWO,KAAK,WAGlD,CAAEX,IAAK,YAAaC,MAAO,aAAcC,UAAU,GACnD,CAAEF,IAAK,eAAgBC,MAAO,gBAAiBC,UAAU,KACxD,IAEH,OACEG,EAAAA,EAAAA,KAACO,EAAAA,EAAc,CACbC,KAAMnB,EACNI,QAASA,EACTgB,WAAYnB,EACZC,MAAOA,EACPmB,YAAY,EACZlB,QAASA,EACTmB,aAAa,mBACb,EAIN,GAAeC,EAAAA,EAAAA,MAAKzB,G,cCvDpB,MA6CA,EA7CgDC,IAGzC,IAH0C,aAC/CyB,EAAY,eACZC,GACD1B,EACC,OACE2B,EAAAA,EAAAA,MAAA,OAAKd,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CACLC,QAA0B,QAAjBJ,EAAyB,UAAY,UAC9CK,KAAK,KACLC,QAASA,IAAML,EAAe,OAAOZ,SACtC,gBAGDF,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CACLC,QAA0B,YAAjBJ,EAA6B,UAAY,UAClDK,KAAK,KACLC,QAASA,IAAML,EAAe,WAAWZ,SAC1C,aAGDF,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CACLC,QAA0B,aAAjBJ,EAA8B,UAAY,UACnDK,KAAK,KACLC,QAASA,IAAML,EAAe,YAAYZ,SAC3C,cAGDF,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CACLC,QAA0B,cAAjBJ,EAA+B,UAAY,UACpDK,KAAK,KACLC,QAASA,IAAML,EAAe,aAAaZ,SAC5C,eAGDF,EAAAA,EAAAA,KAACgB,EAAAA,EAAM,CACLC,QAA0B,aAAjBJ,EAA8B,UAAY,UACnDK,KAAK,KACLC,QAASA,IAAML,EAAe,YAAYZ,SAC3C,eAGG,E,kCC5CH,MAkGP,EAlGyB,CAIvBkB,UAAWC,UACT,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAa,UAAW,CAAEC,WAC3D,OAAOC,EAAAA,GAAmBC,QAAQL,EAAU,SAC9C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFE,aAAcT,UACZ,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAW,WAAWO,KACvD,OAAOL,EAAAA,GAAmBM,QAAQV,EAAU,QAASS,EACvD,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFK,YAAaZ,UACX,IACE,MAAMC,QAAiBC,EAAAA,EAAUW,KAAY,UAAWC,GACxD,OAAOT,EAAAA,GAAmBU,OAAOd,EAAU,QAC7C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFS,YAAahB,MAAOU,EAAYI,KAC9B,IACE,MAAMb,QAAiBC,EAAAA,EAAUe,IAAW,WAAWP,IAAMI,GAC7D,OAAOT,EAAAA,GAAmBa,OAAOjB,EAAU,QAASS,EACtD,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFY,YAAanB,UACX,IACE,MAAMC,QAAiBC,EAAAA,EAAUkB,OAAO,WAAWV,KACnD,OAAOL,EAAAA,GAAmBe,OAAOnB,EAAU,QAASS,EACtD,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFc,kBAAmBrB,MAAOU,EAAY1B,KACpC,IACE,MAAMiB,QAAiBC,EAAAA,EAAUe,IAAW,WAAWP,WAAa,CAAE1B,WACtE,OAAOqB,EAAAA,GAAmBa,OAAOjB,EAAU,QAASS,EACtD,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFe,kBAAmBtB,UACjB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAa,UAAW,CAAEC,OAAQ,CAAEpB,YACrE,OAAOqB,EAAAA,GAAmBC,QAAQL,EAAU,UAAU,EACxD,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFgB,oBAAqBvB,UACnB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAa,UAAW,CAAEC,OAAQ,CAAEoB,gBACrE,OAAOnB,EAAAA,GAAmBC,QAAQL,EAAU,UAAU,EACxD,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,I,cC9FG,MAkMP,EAlMyBkB,KACvB,MAAOzD,EAAQ0D,IAAaC,EAAAA,EAAAA,UAAkB,KACvCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCpB,EAAOuB,IAAYH,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEI,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAsBC,EAAAA,EAAAA,QAAOH,IAGnCI,EAAAA,EAAAA,YAAU,KACRF,EAAoBG,QAAUL,CAAgB,IAIhD,MAAMM,GAAcC,EAAAA,EAAAA,cAAYtC,UAC9B6B,GAAa,GACbC,EAAS,MACT,IACE,MAAM3C,QAAaoD,EAAUxC,YAC7B2B,EAAUvC,EACZ,CAAE,MAAOqD,GACPV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,0BAEb,CAAC,QACCZ,GAAa,EACf,IACC,IAGGpB,GAAe6B,EAAAA,EAAAA,cAAYtC,UAC/B6B,GAAa,GACbC,EAAS,MACT,IAEE,aADoBS,EAAU9B,aAAaC,EAE7C,CAAE,MAAO8B,GAOP,MANAV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,yBAAyB/B,MAE9B8B,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAGGb,GAAcsB,EAAAA,EAAAA,cAAYtC,MAAOU,EAAYI,KACjDe,GAAa,GACbC,EAAS,MACT,IACE,MAAMY,QAAqBH,EAAUvB,YAAYN,EAAII,GASrD,OARAY,GAAUiB,GACRA,EAAWC,KAAIC,GAASA,EAAMnC,KAAOA,EAAKgC,EAAeG,MAE3DZ,EAAoBG,QAAQ,CAC1BnD,KAAM,UACNf,MAAO,UACPuE,QAAS,+BAEJC,CACT,CAAE,MAAOF,GAOP,MANAV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,2BAELD,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAGGiB,GAAcR,EAAAA,EAAAA,cAAYtC,UAC9B6B,GAAa,GACbC,EAAS,MACT,IACE,MAAMiB,QAAuBR,EAAUlB,kBAAkBX,EAAI,YAS7D,OARAgB,GAAUiB,GACRA,EAAWC,KAAIC,GAASA,EAAMnC,KAAOA,EAAKqC,EAAiBF,MAE7DZ,EAAoBG,QAAQ,CAC1BnD,KAAM,UACNf,MAAO,UACPuE,QAAS,iCAEJM,CACT,CAAE,MAAOP,GAOP,MANAV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,2BAELD,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAGGP,GAAoBgB,EAAAA,EAAAA,cAAYtC,UACpC6B,GAAa,GACbC,EAAS,MACT,IAEE,aAD6BS,EAAUjB,kBAAkBtC,EAE3D,CAAE,MAAOwD,GAOP,MANAV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,sCAAsCzD,MAE3CwD,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAGGN,GAAsBe,EAAAA,EAAAA,cAAYtC,UACtC6B,GAAa,GACbC,EAAS,MACT,IAEE,aAD6BS,EAAUhB,oBAAoBC,EAE7D,CAAE,MAAOgB,GAOP,MANAV,EAASU,GACTP,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAAS,uCAAuCjB,MAE5CgB,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAGGV,GAAcmB,EAAAA,EAAAA,cAAYtC,UAC9B6B,GAAa,GACbC,EAAS,MACT,UACQS,EAAUpB,YAAYT,GAC5BgB,GAAUiB,GAAcA,EAAWK,QAAOH,GAASA,EAAMnC,KAAOA,MAChEuB,EAAoBG,QAAQ,CAC1BnD,KAAM,UACNf,MAAO,UACPuE,QAAS,8BAEb,CAAE,MAAOD,GACPV,EAASU,GACT,MAAMS,EAAeT,aAAeU,MAAQV,EAAIC,QAAU,yBAM1D,MALAR,EAAoBG,QAAQ,CAC1BnD,KAAM,QACNf,MAAO,QACPuE,QAASQ,IAELT,CACR,CAAC,QACCX,GAAa,EACf,IACC,IAOH,OAJAM,EAAAA,EAAAA,YAAU,KACRE,GAAa,GACZ,CAACA,IAEG,CACLrE,SACA4D,YACArB,QACA8B,cACA5B,eACAO,cACA8B,cACA3B,cACAG,oBACAC,sBACD,E,8FCjLH,MAAM4B,EAAwCpF,IAOvC,IAPwC,MAC7CG,EAAK,YACLkF,EAAW,QACXC,EAAO,YACPC,EAAW,UACX1E,EAAY,GAAE,OACd2E,GACDxF,EACC,OACE2B,EAAAA,EAAAA,MAAA,OACEd,UAAW,QAAQA,IACnB,cAAa2E,EAAO1E,SAAA,CAGnByE,GAAeA,EAAYE,OAAS,IACnC7E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAY,aAAW,aAAYC,UAChDa,EAAAA,EAAAA,MAAA,MAAId,UAAU,oDAAmDC,SAAA,EAC/DF,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAC8E,EAAAA,GAAI,CACHC,GAAG,IACH9E,UAAU,uCACV,aAAW,OAAMC,UAEjBF,EAAAA,EAAAA,KAACgF,EAAAA,EAAQ,CAAC/E,UAAU,gBAIvB0E,EAAYV,KAAI,CAACgB,EAAMC,KACtBnE,EAAAA,EAAAA,MAAA,MAAgBd,UAAU,oBAAmBC,SAAA,EAC3CF,EAAAA,EAAAA,KAACmF,EAAAA,EAAgB,CAAClF,UAAU,+BAC3BgF,EAAKG,MAAQF,EAAQP,EAAYE,OAAS,GACzC7E,EAAAA,EAAAA,KAAC8E,EAAAA,GAAI,CACHC,GAAIE,EAAKG,KACTnF,UAAU,qBAAoBC,SAE7B+E,EAAKrF,SAGRI,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4BAA2BC,SAAE+E,EAAKrF,UAV7CsF,WAmBjBnE,EAAAA,EAAAA,MAAA,OAAKd,UAAU,8EAA6EC,SAAA,EAC1Fa,EAAAA,EAAAA,MAAA,OAAAb,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAAEX,IACjDkF,GAAsC,kBAAhBA,GACrBzE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAEuE,IAE3CA,KAIHC,IACC1E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,SAC/CwE,SAIH,EAIV,GAAe9D,EAAAA,EAAAA,MAAK4D,E,qDCnFb,MAAMa,EAAa,SAACC,GAA0E,IAAtDC,EAAmCC,UAAAX,OAAA,QAAAY,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKF,EAAY,MAAO,IAExB,IACE,MAAMI,EAAO,IAAIC,KAAKL,GAGhBM,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACFR,GAGL,OAAO,IAAIS,KAAKC,eAAe,QAASL,GAAgBM,OAAOR,EACjE,CAAE,MAAO9D,GAEP,OADAuE,QAAQvE,MAAM,yBAA0BA,GACjC0D,CACT,CACF,EAkBanF,EAAiB,SAC5BiG,GAGY,IAFZC,EAAgBb,UAAAX,OAAA,QAAAY,IAAAD,UAAA,GAAAA,UAAA,GAAG,MACnBc,EAAcd,UAAAX,OAAA,QAAAY,IAAAD,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAIQ,KAAKO,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBR,OAAOE,EACZ,CAAE,MAAOxE,GAEP,OADAuE,QAAQvE,MAAM,6BAA8BA,GACrC,GAAGyE,KAAYD,EAAOO,QAAQ,IACvC,CACF,EAkDaC,EAAkBC,IAC7B,GAAc,IAAVA,EAAa,MAAO,UAExB,MAEMC,EAAIC,KAAKC,MAAMD,KAAKE,IAAIJ,GAASE,KAAKE,IAFlC,OAIV,MAAO,GAAGC,YAAYL,EAAQE,KAAKI,IAJzB,KAIgCL,IAAIH,QAAQ,OAHxC,CAAC,QAAS,KAAM,KAAM,KAAM,MAGyBG,IAAI,C,yGCtGzE,MAiDA,EAjDgD1H,IAIzC,IAJ0C,OAC/CiB,EACAC,KAAM8G,EAAQ,OAAM,UACpBnH,EAAY,IACbb,EAEC,IAAKiB,EACH,OACEL,EAAAA,EAAAA,KAAA,QAAMC,UAAW,qGAAqGA,IAAYC,SAAC,YAMvI,MAAMmH,EAAYhH,EAAOiH,cACzB,IAAIC,EAAa,GACbC,EAAO,KAGO,WAAdH,GAAwC,aAAdA,GAA0C,cAAdA,GACxDE,EAAa,8BACbC,GAAOxH,EAAAA,EAAAA,KAACyH,EAAAA,EAAe,CAACxH,UAAU,kBACX,YAAdoH,GAAyC,eAAdA,GACpCE,EAAa,4BACbC,GAAOxH,EAAAA,EAAAA,KAAC0H,EAAAA,EAAS,CAACzH,UAAU,kBACL,WAAdoH,GAAwC,aAAdA,GACnCE,EAAa,0BACbC,GAAOxH,EAAAA,EAAAA,KAAC2H,EAAAA,EAAW,CAAC1H,UAAU,kBACP,YAAdoH,GACTE,EAAa,gCACbC,GAAOxH,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAC3H,UAAU,kBACL,YAAdoH,GACTE,EAAa,gCACbC,GAAOxH,EAAAA,EAAAA,KAAC6H,EAAAA,EAAqB,CAAC5H,UAAU,kBAExCsH,EAAa,4BAIf,MAAMO,EAAkBzH,EAASA,EAAO0H,OAAO,GAAGC,cAAgB3H,EAAO4H,MAAM,GAAK,UAEpF,OACElH,EAAAA,EAAAA,MAAA,QAAMd,UAAW,2EAA2EsH,KAActH,IAAYC,SAAA,CACnHsH,EACAM,IACI,C,yDCzCJ,MAwBP,EAxB8B1I,IASC,IAT+B,KAC5DoB,EAAI,QACJf,EAAO,WACPgB,EAAU,MACVlB,EAAK,WACLmB,GAAa,EAAI,QACjBlB,GAAU,EAAK,aACfmB,EAAe,oBAAmB,UAClCV,EAAY,IACWb,EACvB,OACEY,EAAAA,EAAAA,KAACkI,EAAAA,EAAS,CACRzI,QAASA,EACTe,KAAMA,EACNC,WAAYA,EACZlB,MAAOA,EACPmB,WAAYA,EACZlB,QAASA,EACTmB,aAAcA,EACdV,UAAWA,GACX,C,gDCxCN,SAASkF,EAAgB/F,EAItB+I,GAAQ,IAJe,MACxB5I,EAAK,QACL6I,KACGC,GACJjJ,EACC,OAAoBkJ,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ9I,EAAqB+I,EAAAA,cAAoB,QAAS,CAC3DvG,GAAIqG,GACH7I,GAAS,KAAmB+I,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBnD,E,6GCZlD,MAAM+D,EAA+BC,IAC5B,CACLpH,GAAIoH,EAASpH,GACbqH,KAAMD,EAASE,YACfC,SAAUH,EAASG,SACnBC,UAAWJ,EAASI,UACpB9E,YAAa,eAAe0E,EAASK,YACrCC,IAAKN,EAASK,YAOLE,EAAuBC,IAClC,MAAMtE,EAAcC,GACbA,EACE,IAAIK,KAAKL,GAAYsE,eADJ,IAAIjE,MAAOiE,cAIrC,MAAO,CACL7H,GAAI4H,EAAU5H,GACd8H,aAAcF,EAAUE,aACxBC,aAAcH,EAAUG,aACxBC,YAAaJ,EAAUI,YACvB1J,OAA6B,cAArBsJ,EAAUtJ,OAAyB,WAAasJ,EAAUtJ,OAClE2J,UAAW3E,EAAWsE,EAAUK,WAChCC,aAAc5E,EAAWsE,EAAUM,cACnCC,MAAOP,EAAUO,MAAQP,EAAUO,MAAMjG,IAAIiF,GAA+B,GAC5EiB,MAAOR,EAAUQ,SACbR,EAAUS,iBAAmB,CAC/BA,gBAAiB,CACfC,OAAQV,EAAUS,gBAAgBC,OAClCC,KAAMX,EAAUS,gBAAgBE,KAChCC,MAAOZ,EAAUS,gBAAgBG,MACjCC,WAAYb,EAAUS,gBAAgBK,QACtCC,QAASf,EAAUS,gBAAgBM,UAGxC,EAMUC,EAAgBA,IACpBC,EAAAA,EAAW3G,IAAIyF,GAMXmB,EAAoB9I,IAE/B,MAAM+I,EAAWC,OAAOhJ,GAGlB4H,EAAYiB,EAAAA,EAAWI,MAAKC,GAChCA,EAAEC,cAAgBJ,GAClBG,EAAElJ,KAAO+I,GACTC,OAAOE,EAAElJ,MAAQ+I,IAEnB,GAAKnB,EACL,OAAOD,EAAoBC,EAAU,C", "sources": ["features/orders/components/OrderList.tsx", "features/orders/components/OrderFilter.tsx", "features/orders/api/ordersApi.ts", "features/orders/hooks/useOrders.ts", "components/layout/PageHeader.tsx", "utils/formatters.ts", "components/common/StatusBadge.tsx", "components/common/EntityList/BaseEntityList.tsx", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "features/orders/utils/orderMappers.ts"], "sourcesContent": ["/**\r\n * Order List Component\r\n * \r\n * This component displays a list of orders in a data table.\r\n */\r\n\r\nimport React, { useMemo, memo } from 'react';\r\nimport BaseEntityList from '../../../components/common/EntityList/BaseEntityList';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport type { Column } from '../../../components/common/DataTable';\r\nimport type { Order } from '../types';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\n\r\ninterface OrderListProps {\r\n  orders: Order[];\r\n  onOrderClick: (order: Order) => void;\r\n  title?: string;\r\n  loading?: boolean;\r\n}\r\n\r\nconst OrderList: React.FC<OrderListProps> = ({\r\n  orders,\r\n  onOrderClick,\r\n  title = 'Orders',\r\n  loading = false\r\n}) => {\r\n  // Memoize columns to prevent unnecessary re-renders\r\n  const columns: Column<Order>[] = useMemo(() => [\r\n    {\r\n      key: 'id',\r\n      label: 'Order ID',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"font-medium text-black\">{value}</span>\r\n      )\r\n    },\r\n    { key: 'customerName', label: 'Customer', sortable: true },\r\n    { key: 'supplierName', label: 'Supplier', sortable: true },\r\n    {\r\n      key: 'totalAmount',\r\n      label: 'Total Amount',\r\n      sortable: true,\r\n      render: (value: number) => formatCurrency(value)\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <StatusBadge status={value || 'pending'} type=\"order\" />\r\n      )\r\n    },\r\n    { key: 'orderDate', label: 'Order Date', sortable: true },\r\n    { key: 'deliveryDate', label: 'Delivery Date', sortable: true },\r\n  ], []);\r\n\r\n  return (\r\n    <BaseEntityList<Order>\r\n      data={orders}\r\n      columns={columns}\r\n      onRowClick={onOrderClick}\r\n      title={title}\r\n      pagination={true}\r\n      loading={loading}\r\n      emptyMessage=\"No orders found\"\r\n    />\r\n  );\r\n};\r\n\r\nexport default memo(OrderList);\r\n\r\n", "/**\r\n * Order Filter Component\r\n * \r\n * This component provides filtering options for orders.\r\n */\r\n\r\nimport React from 'react';\r\nimport Button from '../../../components/common/Button';\r\n\r\ninterface OrderFilterProps {\r\n  activeFilter: 'all' | 'pending' | 'approved' | 'completed' | 'rejected';\r\n  onFilterChange: (filter: 'all' | 'pending' | 'approved' | 'completed' | 'rejected') => void;\r\n}\r\n\r\nconst OrderFilter: React.FC<OrderFilterProps> = ({\r\n  activeFilter,\r\n  onFilterChange\r\n}) => {\r\n  return (\r\n    <div className=\"flex flex-wrap gap-3 mb-6\">\r\n      <Button\r\n        variant={activeFilter === 'all' ? 'primary' : 'outline'}\r\n        size=\"sm\"\r\n        onClick={() => onFilterChange('all')}\r\n      >\r\n        All Orders\r\n      </Button>\r\n      <Button\r\n        variant={activeFilter === 'pending' ? 'primary' : 'outline'}\r\n        size=\"sm\"\r\n        onClick={() => onFilterChange('pending')}\r\n      >\r\n        Pending\r\n      </Button>\r\n      <Button\r\n        variant={activeFilter === 'approved' ? 'primary' : 'outline'}\r\n        size=\"sm\"\r\n        onClick={() => onFilterChange('approved')}\r\n      >\r\n        Approved\r\n      </Button>\r\n      <Button\r\n        variant={activeFilter === 'completed' ? 'primary' : 'outline'}\r\n        size=\"sm\"\r\n        onClick={() => onFilterChange('completed')}\r\n      >\r\n        Completed\r\n      </Button>\r\n      <Button\r\n        variant={activeFilter === 'rejected' ? 'primary' : 'outline'}\r\n        size=\"sm\"\r\n        onClick={() => onFilterChange('rejected')}\r\n      >\r\n        Rejected\r\n      </Button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrderFilter;\r\n", "/**\r\n * Orders API Service\r\n * \r\n * This file provides methods for interacting with the orders API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { Order, OrderUpdateData } from '../types';\r\n\r\nexport const ordersApi = {\r\n  /**\r\n   * Get all orders\r\n   */\r\n  getOrders: async (params?: Record<string, any>): Promise<Order[]> => {\r\n    try {\r\n      const response = await apiClient.get<Order[]>('/orders', { params });\r\n      return responseValidators.getList(response, 'orders');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get an order by ID\r\n   */\r\n  getOrderById: async (id: string): Promise<Order> => {\r\n    try {\r\n      const response = await apiClient.get<Order>(`/orders/${id}`);\r\n      return responseValidators.getById(response, 'order', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new order\r\n   */\r\n  createOrder: async (orderData: OrderUpdateData): Promise<Order> => {\r\n    try {\r\n      const response = await apiClient.post<Order>('/orders', orderData);\r\n      return responseValidators.create(response, 'order');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an order\r\n   */\r\n  updateOrder: async (id: string, orderData: Partial<OrderUpdateData>): Promise<Order> => {\r\n    try {\r\n      const response = await apiClient.put<Order>(`/orders/${id}`, orderData);\r\n      return responseValidators.update(response, 'order', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete an order\r\n   */\r\n  deleteOrder: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(`/orders/${id}`);\r\n      return responseValidators.delete(response, 'order', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update order status\r\n   */\r\n  updateOrderStatus: async (id: string, status: Order['status']): Promise<Order> => {\r\n    try {\r\n      const response = await apiClient.put<Order>(`/orders/${id}/status`, { status });\r\n      return responseValidators.update(response, 'order', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get orders by status\r\n   */\r\n  getOrdersByStatus: async (status: Order['status']): Promise<Order[]> => {\r\n    try {\r\n      const response = await apiClient.get<Order[]>('/orders', { params: { status } });\r\n      return responseValidators.getList(response, 'orders', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get orders by customer\r\n   */\r\n  getOrdersByCustomer: async (customerId: string): Promise<Order[]> => {\r\n    try {\r\n      const response = await apiClient.get<Order[]>('/orders', { params: { customerId } });\r\n      return responseValidators.getList(response, 'orders', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default ordersApi;\r\n\r\n", "/**\r\n * Orders Hook\r\n *\r\n * This hook provides methods and state for working with orders.\r\n */\r\n\r\nimport { useState, useCallback, useEffect, useRef } from 'react';\r\nimport type { Order, OrderUpdateData } from '../types/index';\r\nimport ordersApi from '../api/ordersApi';\r\nimport useNotification from '../../../hooks/useNotification';\r\n\r\nexport const useOrders = () => {\r\n  const [orders, setOrders] = useState<Order[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues\r\n  const showNotificationRef = useRef(showNotification);\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n\r\n  // Fetch all orders\r\n  const fetchOrders = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await ordersApi.getOrders();\r\n      setOrders(data);\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch orders'\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get an order by ID\r\n  const getOrderById = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const order = await ordersApi.getOrderById(id);\r\n      return order;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch order ${id}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Update an order\r\n  const updateOrder = useCallback(async (id: string, orderData: OrderUpdateData) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedOrder = await ordersApi.updateOrder(id, orderData);\r\n      setOrders(prevOrders =>\r\n        prevOrders.map(order => order.id === id ? updatedOrder : order)\r\n      );\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Order updated successfully'\r\n      });\r\n      return updatedOrder;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update order'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Cancel an order\r\n  const cancelOrder = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const cancelledOrder = await ordersApi.updateOrderStatus(id, 'rejected');\r\n      setOrders(prevOrders =>\r\n        prevOrders.map(order => order.id === id ? cancelledOrder : order)\r\n      );\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Order cancelled successfully'\r\n      });\r\n      return cancelledOrder;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to cancel order'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get orders by status\r\n  const getOrdersByStatus = useCallback(async (status: Order['status']) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const filteredOrders = await ordersApi.getOrdersByStatus(status);\r\n      return filteredOrders;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch orders with status ${status}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get orders by customer\r\n  const getOrdersByCustomer = useCallback(async (customerId: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const customerOrders = await ordersApi.getOrdersByCustomer(customerId);\r\n      return customerOrders;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch orders for customer ${customerId}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Delete an order\r\n  const deleteOrder = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      await ordersApi.deleteOrder(id);\r\n      setOrders(prevOrders => prevOrders.filter(order => order.id !== id));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Order deleted successfully'\r\n      });\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to delete order';\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorMessage\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Load orders on mount\r\n  useEffect(() => {\r\n    fetchOrders();\r\n  }, [fetchOrders]);\r\n\r\n  return {\r\n    orders,\r\n    isLoading,\r\n    error,\r\n    fetchOrders,\r\n    getOrderById,\r\n    updateOrder,\r\n    cancelOrder,\r\n    deleteOrder,\r\n    getOrdersByStatus,\r\n    getOrdersByCustomer\r\n  };\r\n};\r\n\r\nexport default useOrders;\r\n\r\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "/**\r\n * BaseEntityList Component\r\n *\r\n * A reusable list component for displaying entities in a data table.\r\n * This component is generic and can be used for any entity type.\r\n */\r\n\r\nimport DataTable from '../DataTable';\r\nimport type { Column } from '../DataTable';\r\n\r\nexport interface BaseEntityListProps<T> {\r\n  data: T[];\r\n  columns: Column<T>[];\r\n  onRowClick?: (entity: T) => void;\r\n  title?: string;\r\n  pagination?: boolean;\r\n  loading?: boolean;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nexport const BaseEntityList = <T extends Record<string, any>>({\r\n  data,\r\n  columns,\r\n  onRowClick,\r\n  title,\r\n  pagination = true,\r\n  loading = false,\r\n  emptyMessage = 'No data available',\r\n  className = ''\r\n}: BaseEntityListProps<T>) => {\r\n  return (\r\n    <DataTable<T>\r\n      columns={columns}\r\n      data={data}\r\n      onRowClick={onRowClick}\r\n      title={title}\r\n      pagination={pagination}\r\n      loading={loading}\r\n      emptyMessage={emptyMessage}\r\n      className={className}\r\n    />\r\n  );\r\n};\r\n\r\nexport default BaseEntityList;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "/**\r\n * Order Mappers\r\n *\r\n * Utility functions to map between different order data formats\r\n */\r\n\r\nimport type{ Order, OrderItem } from '../types/index';\r\nimport { orders as mockOrders, type OrderItem as MockOrderItem } from '../../../mockData/entities/orders';\r\n\r\n/**\r\n * Maps a mock order item to the application order item format\r\n */\r\nconst mapMockOrderItemToOrderItem = (mockItem: MockOrderItem): OrderItem => {\r\n  return {\r\n    id: mockItem.id,\r\n    name: mockItem.productName,\r\n    quantity: mockItem.quantity,\r\n    unitPrice: mockItem.unitPrice,\r\n    description: `Product ID: ${mockItem.productId}`,\r\n    sku: mockItem.productId\r\n  };\r\n};\r\n\r\n/**\r\n * Maps a mock order to the application order format\r\n */\r\nexport const mapMockOrderToOrder = (mockOrder: any): Order => {\r\n  const formatDate = (dateString?: string): string => {\r\n    if (!dateString) return new Date().toISOString();\r\n    return new Date(dateString).toISOString();\r\n  };\r\n\r\n  return {\r\n    id: mockOrder.id,\r\n    customerName: mockOrder.customerName,\r\n    supplierName: mockOrder.supplierName,\r\n    totalAmount: mockOrder.totalAmount,\r\n    status: mockOrder.status === 'cancelled' ? 'rejected' : mockOrder.status,\r\n    orderDate: formatDate(mockOrder.orderDate),\r\n    deliveryDate: formatDate(mockOrder.deliveryDate),\r\n    items: mockOrder.items ? mockOrder.items.map(mapMockOrderItemToOrderItem) : [],\r\n    notes: mockOrder.notes,\r\n    ...(mockOrder.shippingAddress && {\r\n      shippingAddress: {\r\n        street: mockOrder.shippingAddress.street,\r\n        city: mockOrder.shippingAddress.city,\r\n        state: mockOrder.shippingAddress.state,\r\n        postalCode: mockOrder.shippingAddress.zipCode,\r\n        country: mockOrder.shippingAddress.country\r\n      }\r\n    })\r\n  };\r\n};\r\n\r\n/**\r\n * Get all orders from mock data\r\n */\r\nexport const getMockOrders = (): Order[] => {\r\n  return mockOrders.map(mapMockOrderToOrder);\r\n};\r\n\r\n/**\r\n * Get an order by ID from mock data\r\n */\r\nexport const getMockOrderById = (id: string | number): Order | undefined => {\r\n  // Convert id to string for consistent comparison\r\n  const searchId = String(id);\r\n\r\n  // Try to find by orderNumber first, then by id\r\n  const mockOrder = mockOrders.find(o =>\r\n    o.orderNumber === searchId ||\r\n    o.id === searchId ||\r\n    String(o.id) === searchId\r\n  );\r\n  if (!mockOrder) return undefined;\r\n  return mapMockOrderToOrder(mockOrder);\r\n};\r\n\r\nexport default {\r\n  mapMockOrderToOrder,\r\n  getMockOrders,\r\n  getMockOrderById\r\n};\r\n"], "names": ["OrderList", "_ref", "orders", "onOrderClick", "title", "loading", "columns", "useMemo", "key", "label", "sortable", "render", "value", "_jsx", "className", "children", "formatCurrency", "StatusBadge", "status", "type", "BaseEntityList", "data", "onRowClick", "pagination", "emptyMessage", "memo", "activeFilter", "onFilterChange", "_jsxs", "<PERSON><PERSON>", "variant", "size", "onClick", "getOrders", "async", "response", "apiClient", "get", "params", "responseValidators", "getList", "error", "handleApiError", "getOrderById", "id", "getById", "createOrder", "post", "orderData", "create", "updateOrder", "put", "update", "deleteOrder", "delete", "updateOrderStatus", "getOrdersByStatus", "getOrdersByCustomer", "customerId", "useOrders", "setOrders", "useState", "isLoading", "setIsLoading", "setError", "showNotification", "useNotification", "showNotificationRef", "useRef", "useEffect", "current", "fetchOrders", "useCallback", "ordersApi", "err", "message", "updatedOrder", "prevOrders", "map", "order", "cancelOrder", "cancelledOrder", "filter", "errorMessage", "Error", "<PERSON><PERSON><PERSON><PERSON>", "description", "actions", "breadcrumbs", "testId", "length", "Link", "to", "HomeIcon", "item", "index", "ChevronRightIcon", "path", "formatDate", "dateString", "options", "arguments", "undefined", "date", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "console", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "formatFileSize", "bytes", "i", "Math", "floor", "log", "parseFloat", "pow", "_type", "statusKey", "toLowerCase", "colorClass", "icon", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "char<PERSON>t", "toUpperCase", "slice", "DataTable", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "mapMockOrderItemToOrderItem", "mockItem", "name", "productName", "quantity", "unitPrice", "productId", "sku", "mapMockOrderToOrder", "mockOrder", "toISOString", "customerName", "supplierName", "totalAmount", "orderDate", "deliveryDate", "items", "notes", "shippingAddress", "street", "city", "state", "postalCode", "zipCode", "country", "getMockOrders", "mockOrders", "getMockOrderById", "searchId", "String", "find", "o", "orderNumber"], "sourceRoot": ""}