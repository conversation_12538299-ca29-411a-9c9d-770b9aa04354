{"version": 3, "file": "static/js/814.40416520.chunk.js", "mappings": "oKA+BO,MAuMP,EAvM+B,WAA2C,IAA1CA,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjE,MAAM,oBAAEG,GAAsB,EAAI,gBAAEC,GAAkB,EAAI,QAAEC,GAAYN,GAClE,iBAAEO,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAqB,CACvDC,UAAU,EACVC,MAAO,KACPC,UAAW,OAIPC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BN,EAAc,CACZE,UAAU,EACVC,MAAO,KACPC,UAAW,MACX,GACD,IAGGG,GAA0BD,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KACvD,MAAMC,GAAWC,EAAAA,EAAAA,IACfP,EACAT,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAkBN,OAfAO,EAAc,CACZE,UAAU,EACVC,MAAOM,EACPL,UAAW,SACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,GAGVC,CAAQ,GACd,CAACf,EAAqBC,EAAiBE,EAAkBD,IAGtDqB,GAAiCX,EAAAA,EAAAA,cAAY,CACjDY,EACAJ,EACAK,EACAX,KAEA,MAAMY,GAAkBC,EAAAA,EAAAA,IAAsBH,EAAOJ,EAASK,GAqB9D,OAnBAnB,EAAc,CACZE,UAAU,EACVC,MAAOiB,EACPhB,UAAW,gBACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,mBACPC,QAASM,EAAgBN,UAIzBlB,GACFA,EAAQwB,EAAiBZ,GAGpBY,CAAe,GACrB,CAAC1B,EAAqBG,EAAkBD,IAGrC0B,GAA2BhB,EAAAA,EAAAA,cAAY,CAC3CH,EACAoB,EACAf,MAEAgB,EAAAA,EAAAA,IACErB,EACAoB,EACA7B,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAGNO,EAAc,CACZE,UAAU,EACVC,QACAC,UAAW,UACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,EACjB,GACC,CAACd,EAAqBC,EAAiBE,EAAkBD,IAGtD6B,GAAqBnB,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KAClD,MAAMkB,EAAWvB,aAAiBY,MAAQZ,EAAQ,IAAIY,MAAMY,OAAOxB,IA2BnE,OAzBAH,EAAc,CACZE,UAAU,EACVC,MAAOuB,EACPtB,UAAW,aACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,QACPC,QAASY,EAASZ,UAIlBnB,IACFqB,EAAAA,EAAAA,IAAYU,EAAUlB,IAGxBoB,EAAAA,EAAAA,IAASF,EAAUlB,GAEfZ,GACFA,EAAQO,EAAOK,GAGVkB,CAAQ,GACd,CAAChC,EAAqBC,EAAiBE,EAAkBD,IAGtDiC,GAAoBvB,EAAAA,EAAAA,cAAYwB,MACpCC,EACAvB,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAI,EAAwBJ,EAAOK,GACxB,IACT,IACC,CAACH,EAAYE,IAGVyB,GAAwB1B,EAAAA,EAAAA,cAAYwB,MACxCC,EACAR,EACAf,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAmB,EAAyBnB,EAAOoB,EAAef,GACxC,IACT,IACC,CAACH,EAAYiB,IAEhB,MAAO,IAEFvB,EAGHW,eAAgBH,EAChBc,sBAAuBJ,EACvBO,gBAAiBF,EACjBG,qBACApB,aAGAwB,oBACAG,wBAGAC,WAAa9B,GACXA,GAA0B,kBAAVA,GAAsB,WAAYA,EACpD+B,kBAAoB/B,GAClBA,GAA0B,kBAAVA,GAAsB,UAAWA,EAEvD,C,gDCnOA,SAASgC,EAASC,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBL,E,uDCPlD,MA+FA,EA/F4CC,IAarC,IAbsC,MAC3CiB,EAAK,KACLC,EAAI,KACJ1C,EAAO,OAAM,MACb2C,EAAK,SACLC,EAAQ,MACRrD,EAAK,SACLsD,GAAW,EAAK,YAChBC,EAAc,GAAE,QAChBpE,EAAU,GAAE,UACZqE,EAAY,GAAE,SACdC,GAAW,EAAK,QAChBC,GAAU,GACXzB,EACC,MAAM0B,EAAe,sDACnB3D,EAAQ,yDAA2D,2DAqErE,OACE4D,EAAAA,EAAAA,MAAA,OAAKJ,UAAW,GAAGA,IAAYK,SAAA,EAC7BD,EAAAA,EAAAA,MAAA,SAAOE,QAASX,EAAMK,UAAU,0CAAyCK,SAAA,CACtEX,EAAM,IAAEI,IAAYS,EAAAA,EAAAA,KAAA,QAAMP,UAAU,eAAcK,SAAC,SArEtCG,MAClB,OAAQvD,GACN,IAAK,WACH,OACEsD,EAAAA,EAAAA,KAAA,YACEjB,GAAIK,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVG,UAAWG,EACXJ,YAAaA,EACbE,SAAUA,IAIhB,IAAK,SACH,OACEM,EAAAA,EAAAA,KAAA,UACEjB,GAAIK,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVG,UAAWG,EACXF,SAAUA,GAAYC,EAAQG,SAE7BH,GACCK,EAAAA,EAAAA,KAAA,UAAQX,MAAM,GAAES,SAAC,eAEjB1E,EAAQ8E,KAAIC,IACVH,EAAAA,EAAAA,KAAA,UAA2BX,MAAOc,EAAOd,MAAMS,SAC5CK,EAAOhB,OADGgB,EAAOd,WAQ9B,IAAK,WACH,OACEW,EAAAA,EAAAA,KAAA,SACEtD,KAAK,WACLqC,GAAIK,EACJA,KAAMA,EACNgB,QAASf,EACTC,SAAUA,EACVG,UAAU,kEACVC,SAAUA,IAIhB,QACE,OACEM,EAAAA,EAAAA,KAAA,SACEtD,KAAMA,EACNqC,GAAIK,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVG,UAAWG,EACXJ,YAAaA,EACbE,SAAUA,IAGlB,EAQGO,GACAhE,IAAS+D,EAAAA,EAAAA,KAAA,KAAGP,UAAU,4BAA2BK,SAAE7D,MAChD,C,2CC5FH,MAAMoE,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAO1E,GACP,OAAO,CACT,GAGW4E,EAAcxB,GACX,OAAVA,QAA4B9D,IAAV8D,IACD,kBAAVA,EAA2BA,EAAMyB,OAAOxF,OAAS,GACxDyF,MAAMC,QAAQ3B,IAAeA,EAAM/D,OAAS,GAYrC2F,EAAa5B,GACjB,WAAWkB,KAAKlB,GAGZ6B,EAAa7B,GACjB,sBAAsBkB,KAAKlB,GAGvB8B,EAAkB9B,GACtB,iBAAiBkB,KAAKlB,GAGlB+B,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAASrG,OAAS,OAGjB,QAAQiF,KAAKoB,OAGb,QAAQpB,KAAKoB,OAGb,QAAQpB,KAAKoB,MAGb,sCAAsCpB,KAAKoB,MAwBrCG,EAAeA,CAC1BC,EACAC,KAEA,MAAMC,EAA2C,CAAC,EAUlD,OARA1D,OAAO2D,QAAQF,GAAiBG,SAAQjE,IAAyB,IAAvBkE,EAAWC,GAAMnE,EACzD,MAAMoE,EAAMF,EACNnG,EA1BmBsG,EAC3BC,EACAnD,EACAgD,EACAI,KAEA,MAAMC,EAAY3B,MAAMC,QAAQqB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMM,KAAQD,EACjB,IAAKC,EAAKC,UAAUvD,EAAOoD,GACzB,OAAOE,EAAK/F,QAIhB,MAAO,EAAE,EAYO2F,CAAcH,EAAWL,EAAOO,GAAMD,EAAON,GACvD9F,IACFgG,EAAOK,GAAOrG,EAChB,IAGKgG,CAAM,EAIFD,EAAkB,CAC7BzC,SAAU,WAA2C,MAAsB,CACzEqD,UAAW/B,EACXjE,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAEDiF,MAAO,WAAuD,MAAsB,CAClFsC,UAAWvC,EACXzD,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDoF,MAAO,WAAsD,MAAsB,CACjFmC,UAAWpC,EACX5D,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAEDsF,IAAK,WAA6C,MAAsB,CACtEiC,UAAWlC,EACX9D,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDwH,UAAWA,CAACC,EAAalG,KAAgB,CACvCgG,UAAYvD,GA3GSwD,EAACxD,EAAeyD,IAChCzD,EAAM/D,QAAUwH,EA0GSD,CAAUxD,EAAOyD,GAC/ClG,QAASA,GAAW,oBAAoBkG,iBAG1CC,UAAWA,CAACC,EAAapG,KAAgB,CACvCgG,UAAYvD,GA5GS0D,EAAC1D,EAAe2D,IAChC3D,EAAM/D,QAAU0H,EA2GSD,CAAU1D,EAAO2D,GAC/CpG,QAASA,GAAW,wBAAwBoG,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EL,UAAW3B,EACXrE,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAED6H,QAAS,WAAwD,MAAsB,CACrFN,UAAW1B,EACXtE,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAED8H,aAAc,WAAwD,MAAsB,CAC1FP,UAAWzB,EACXvE,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDiG,KAAM,WAA8C,MAAsB,CACxEsB,UAAWxB,EACXxE,QAFoBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDsG,SAAU,WAA2H,MAAsB,CACzJiB,UAAWf,EACXjF,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAED+H,cAAe,WAA2C,MAAsB,CAC9ER,UAAWA,CAACvD,EAAeoD,IAAmBf,EAAiBrC,EAAe,OAARoD,QAAQ,IAARA,OAAQ,EAARA,EAAUb,iBAChFhF,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAEDgI,qBAAsB,WAA2C,MAAsB,CACrFT,UAAWA,CAACvD,EAAeoD,IAAmBf,EAAiBrC,EAAe,OAARoD,QAAQ,IAARA,OAAQ,EAARA,EAAUd,UAChF/E,QAFoCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDiI,IAAK,WAA6C,MAAsB,CACtEV,UAAYvD,GAAkB,sBAAsBkB,KAAKlB,GACzDzC,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDkI,MAAO,WAA+C,MAAsB,CAC1EX,UAAYvD,GAAkBA,EAAQ,GAAKA,GAAS,OACpDzC,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDmI,MAAO,WAAwD,MAAsB,CACnFZ,UAAYvD,GAAkBoE,OAAOC,UAAUrE,IAAUA,GAAS,EAClEzC,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDsI,aAAc,WAA6D,MAAsB,CAC/Ff,UAAYvD,GAAkBoE,OAAOC,UAAUrE,IAAUA,GAAS,EAClEzC,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDuI,iBAAkB,WAAuE,MAAsB,CAC7GhB,UAAWA,CAACe,EAAsBlB,KAC3BA,IAAaA,EAASe,OACpBG,GAAgBlB,EAASe,MAElC5G,QALgCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDwI,cAAe,WAAkD,MAAsB,CACrFjB,UAAYvD,GAAiB0B,MAAMC,QAAQ3B,IAAUA,EAAM/D,OAAS,EACpEsB,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAEDyI,WAAY,eAACC,EAAgB1I,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEuH,UAAYvD,KACL0B,MAAMC,QAAQ3B,IACZA,EAAM/D,QAAUyI,EAEzBnH,SALkDvB,UAAAC,OAAA,EAAAD,UAAA,QAAAE,IAK9B,WAAWwI,mBAChC,E,uFClNH,MA6JA,EA7JgD7F,IAWzC,IAX0C,MAC/CiB,EAAK,KACLC,EAAI,MACJC,EAAK,SACLC,EAAQ,MACRrD,EAAK,SACLsD,GAAW,EAAK,SAChBG,GAAW,EAAK,QAChBsE,EAAU,QAAe,aACzBC,EAAe,CAAC,aAAc,YAAa,YAAa,cAAa,UACrExE,EAAY,IACbvB,EACC,MAAOgG,EAAYC,IAAiBpI,EAAAA,EAAAA,WAAS,IACtCqI,EAASC,IAActI,EAAAA,EAAAA,UAAwB,MAChDuI,GAAeC,EAAAA,EAAAA,QAAyB,MAG9CjG,EAAAA,WAAgB,KACd,GAAIe,aAAiBmF,KAAM,CACzB,MAAM7D,EAAMC,IAAI6D,gBAAgBpF,GAEhC,OADAgF,EAAW1D,GACJ,IAAMC,IAAI8D,gBAAgB/D,EACnC,CAAO,MAAqB,kBAAVtB,GAAsBA,OACtCgF,EAAWhF,QAGXgF,EAAW,KAEb,GACC,CAAChF,IAEJ,MAAMsF,GAAmBvI,EAAAA,EAAAA,cAAawI,IACpC,MAAMC,GAAaC,EAAAA,EAAAA,IAAaF,EAAM,CACpCZ,UACAC,iBAGEY,EAAWE,MACbzF,EAASsF,GAGTI,QAAQ/I,MAAM,0BAA2B4I,EAAW5I,MACtD,GACC,CAAC+H,EAASC,EAAc3E,IA8C3B,OACEO,EAAAA,EAAAA,MAAA,OAAKJ,UAAWA,EAAUK,SAAA,EACxBD,EAAAA,EAAAA,MAAA,SAAOJ,UAAU,+CAA8CK,SAAA,CAC5DX,EAAM,IAAEI,IAAYS,EAAAA,EAAAA,KAAA,QAAMP,UAAU,eAAcK,SAAC,UAGtDD,EAAAA,EAAAA,MAAA,OACEJ,UAAW,sHAEPyE,EAAa,yCAA2C,gCACxDjI,EAAQ,iBAAmB,iBAC3ByD,EAAW,gCAAkC,oDAEjDuF,WAlDkBC,IACtBA,EAAEC,iBACGzF,GACHyE,GAAc,EAChB,EA+CIiB,YA5CmBF,IACvBA,EAAEC,iBACFhB,GAAc,EAAM,EA2ChBkB,OAxCcH,IAAwB,IAADI,EAIzC,GAHAJ,EAAEC,iBACFhB,GAAc,GAEVzE,EAAU,OAEd,MAAMkF,EAA2B,QAAvBU,EAAGJ,EAAEK,aAAaC,aAAK,IAAAF,OAAA,EAApBA,EAAuB,GAChCV,GACFD,EAAiBC,EACnB,EAgCIa,QAtBcC,MACbhG,GAAY4E,EAAaqB,SAC5BrB,EAAaqB,QAAQC,OACvB,EAmByB9F,SAAA,EAErBE,EAAAA,EAAAA,KAAA,SACElB,IAAKwF,EACL5H,KAAK,OACL0C,KAAMA,EACNyG,OAAQ5B,EAAa6B,KAAK,KAC1BxG,SAnEuB4F,IAA4C,IAADa,EACxE,MAAMnB,EAAqB,QAAjBmB,EAAGb,EAAEc,OAAOR,aAAK,IAAAO,OAAA,EAAdA,EAAiB,GAC1BnB,GACFD,EAAiBC,EACnB,EAgEMnF,UAAU,SACVC,SAAUA,IAGX0E,GACCvE,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,WAAUK,SAAA,EACvBE,EAAAA,EAAAA,KAAA,OACEiG,IAAK7B,EACL8B,IAAI,UACJzG,UAAU,+CAEVC,IACAM,EAAAA,EAAAA,KAAA,UACEtD,KAAK,SACL+I,QAAUP,IACRA,EAAEiB,kBAnDhB7G,EAAS,MACLgF,EAAaqB,UACfrB,EAAaqB,QAAQtG,MAAQ,GAkDH,EAEhBI,UAAU,qGAAoGK,UAE9GE,EAAAA,EAAAA,KAACoG,EAAAA,EAAS,CAAC3G,UAAU,kBAK3BI,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAC/B,EAAAA,EAAS,CAACwB,UAAU,qCACrBI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,OAAMK,SAAA,EACnBE,EAAAA,EAAAA,KAAA,KAAGP,UAAU,wBAAuBK,SACjCoE,EAAa,kBAAoB,sCAEpCrE,EAAAA,EAAAA,MAAA,KAAGJ,UAAU,6BAA4BK,SAAA,CAAC,uBACnBuG,KAAKC,MAAMtC,EAAU,KAAO,MAAM,iBAOhE/H,IAAS+D,EAAAA,EAAAA,KAAA,KAAGP,UAAU,4BAA2BK,SAAE7D,MAChD,C,6EClJV,MAAMsK,EAA8BrI,IAiB7B,IAjB8B,OACnCsI,EAAM,QACNC,EAAO,MACP9J,EAAK,SACLmD,EAAQ,KACR4G,EAAO,KAAI,OACXC,EAAM,WACNC,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACftH,EAAY,GAAE,cACduH,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtBC,GACDlJ,EACC,MAAMmJ,GAAW9C,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDA+C,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAgBrC,IAChB0B,GAAwB,WAAV1B,EAAE5C,KAClBmE,GACF,EASF,OANID,IACFgB,SAASC,iBAAiB,UAAWF,GAErCC,SAASE,KAAKC,MAAMC,SAAW,UAG1B,KACLJ,SAASK,oBAAoB,UAAWN,GACxCC,SAASE,KAAKC,MAAMC,SAAW,MAAM,CACtC,GACA,CAACpB,EAAQC,EAASG,KAGrBU,EAAAA,EAAAA,YAAU,KACR,IAAKd,IAAWa,EAAS1B,QAAS,OAElC,MAAMmC,EAAoBT,EAAS1B,QAAQoC,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBxM,OAAc,OAEpC,MAAM0M,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBxM,OAAS,GAE3D4M,EAAgBhD,IACN,QAAVA,EAAE5C,MAEF4C,EAAEiD,SACAX,SAASY,gBAAkBJ,IAC7BC,EAAYI,QACZnD,EAAEC,kBAGAqC,SAASY,gBAAkBH,IAC7BD,EAAaK,QACbnD,EAAEC,kBAEN,EAMF,OAHAqC,SAASC,iBAAiB,UAAWS,GACrCF,EAAaK,QAEN,KACLb,SAASK,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAAC1B,KAECA,EAAQ,OAAO,KAGpB,MAUM8B,GACJzI,EAAAA,EAAAA,MAAC0I,EAAAA,SAAQ,CAAAzI,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACEP,UAAW,gEAAgE0H,IAC3E1B,QAASoB,EAAuBJ,OAAUlL,EAC1C,cAAa,GAAG6L,gBAIlBpH,EAAAA,EAAAA,KAAA,OAAKP,UAAU,qCAAoCK,UACjDE,EAAAA,EAAAA,KAAA,OAAKP,UAAW,yBAAyBsH,EAAW,SAAW,yCAAyCjH,UACtGD,EAAAA,EAAAA,MAAA,OACEf,IAAKuI,EACL5H,UAAW,GAxBD,CAClB+I,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BnC,2GAA8GjH,IACxIgG,QAAUP,GAAMA,EAAEiB,kBAClB,cAAaiB,EAAOtH,SAAA,EAGpBD,EAAAA,EAAAA,MAAA,OAAKJ,UAAW,wEAAwEwH,IAAkBnH,SAAA,CACtF,kBAAVnD,GACNqD,EAAAA,EAAAA,KAAA,MAAIP,UAAU,sCAAqCK,SAAEnD,IAErDA,EAEDmK,IACC9G,EAAAA,EAAAA,KAAA,UACEtD,KAAK,SACL+C,UAAU,wGACVgG,QAASgB,EACT,aAAW,cACX,cAAa,GAAGW,iBAAsBtH,UAEtCE,EAAAA,EAAAA,KAACoG,EAAAA,EAAS,CAAC3G,UAAU,kBAM3BO,EAAAA,EAAAA,KAAA,OAAKP,UAAW,aAAauH,IAAgBlH,SAC1CA,IAIF6G,IACC3G,EAAAA,EAAAA,KAAA,OAAKP,UAAW,4EAA4EyH,IAAkBpH,SAC3G6G,cAUf,OAAOmC,EAAAA,EAAAA,cAAaR,EAAcd,SAASE,KAAK,EAGlD,GAAeqB,EAAAA,EAAAA,MAAKxC,E,gDClLpB,SAASyC,EAAY9K,EAIlBC,GAAQ,IAJW,MACpBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB0K,E,gDCvBlD,SAASC,EAAS/K,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2K,E,gDCvBlD,SAASC,EAAOhL,EAIbC,GAAQ,IAJM,MACfxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYZ,EAAAA,cAAoB,OAAQ,CAC3CU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB4K,E,gDC3BlD,SAASC,EAASjL,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,sWAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB6K,E,qGCvBlD,SAASC,EAAmBlL,EAIzBC,GAAQ,IAJkB,MAC3BxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,+SAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB8K,G,0HCMlD,MAwIA,EAxIkDlL,IAM3C,IAN4C,UACjDmL,EAAS,eACTC,EAAc,iBACdC,EACAC,gBAAiBC,EAAgB,MACjC9M,EAAQ,aACTuB,EACC,MAAMwL,GAAWC,EAAAA,EAAAA,MAMXC,EAAU,CACd,CACEtH,IAAK,OACLnD,MAAO,gBACP0K,UAAU,EACVC,OAAQA,CAACC,EAAgBC,KACvBnK,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBK,SAAA,EAChCE,EAAAA,EAAAA,KAAA,OAAKP,UAAU,OAAMK,UACnBE,EAAAA,EAAAA,KAACiK,EAAAA,EAAM,IACAD,EAASE,MAAQ,CAAEjE,IAAK+D,EAASE,MACtChE,IAAK8D,EAAS5K,KACdA,KAAM4K,EAAS5K,KACfsH,KAAK,UAGT7G,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKP,UAAU,4BAA2BK,SAAEkK,EAAS5K,QACrDS,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,wBAAuBK,SAAA,CAAC,OAAKkK,EAASjL,aAK7D,CACEuD,IAAK,QACLnD,MAAO,QACP0K,UAAU,EACVC,OAASzK,IACPQ,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBK,SAAA,EAChCE,EAAAA,EAAAA,KAACgJ,EAAAA,EAAY,CAACvJ,UAAU,gCACxBO,EAAAA,EAAAA,KAAA,QAAAF,SAAOT,QAIb,CACEiD,IAAK,QACLnD,MAAO,QACP0K,UAAU,EACVC,OAASzK,IACPQ,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBK,SAAA,EAChCE,EAAAA,EAAAA,KAACmJ,EAAAA,EAAS,CAAC1J,UAAU,gCACrBO,EAAAA,EAAAA,KAAA,QAAAF,SAAOT,QAIb,CACEiD,IAAK,qBACLnD,MAAO,SACP0K,UAAU,EACVC,OAASzK,IAEP,IAAKA,EACH,OACEQ,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBK,SAAA,EAChCE,EAAAA,EAAAA,KAACmK,EAAAA,EAAS,CAAC1K,UAAU,gCACrBO,EAAAA,EAAAA,KAAA,QAAAF,SAAM,eAKZ,IAAIsK,EACJ,OAAO/K,GACL,IAAK,WACH+K,GAAOpK,EAAAA,EAAAA,KAACqK,EAAAA,EAAe,CAAC5K,UAAU,gCAClC,MACF,IAAK,UACH2K,GAAOpK,EAAAA,EAAAA,KAACmK,EAAAA,EAAS,CAAC1K,UAAU,iCAC5B,MACF,IAAK,WACH2K,GAAOpK,EAAAA,EAAAA,KAACsK,EAAAA,EAAW,CAAC7K,UAAU,8BAC9B,MACF,QACE2K,GAAOpK,EAAAA,EAAAA,KAACmK,EAAAA,EAAS,CAAC1K,UAAU,+BAEhC,OACEI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBK,SAAA,CAC/BsK,GACDpK,EAAAA,EAAAA,KAAA,QAAAF,SAAOT,EAAQA,EAAMkL,OAAO,GAAGC,cAAgBnL,EAAMoL,MAAM,GAAK,cAC5D,GAKZ,CACEnI,IAAK,UACLnD,MAAO,UACP2K,OAAQA,CAACY,EAAQV,KACfnK,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8BAA6BK,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,UACEP,UAAU,sEACVgG,QAAUP,IACRA,EAAEiB,kBACFmD,EAAeU,EAAS,EAE1BrN,MAAM,wBAAuBmD,UAE7BE,EAAAA,EAAAA,KAACkJ,EAAAA,EAAO,CAACzJ,UAAU,eAErBO,EAAAA,EAAAA,KAAA,UACEP,UAAU,sEACVgG,QAAUP,IACRA,EAAEiB,kBACFoD,EAAiBS,EAAS,EAE5BrN,MAAM,kBAAiBmD,UAEvBE,EAAAA,EAAAA,KAACiJ,EAAAA,EAAS,CAACxJ,UAAU,mBAO/B,OACEO,EAAAA,EAAAA,KAAC2K,EAAAA,EAAS,CACRf,QAASA,EACTgB,KAAMvB,EACNwB,WAvHoBb,IACtBN,EAASoB,EAAAA,EAAOC,wBAAwBf,EAASjL,IAAI,EAuHnDpC,MAAOA,EACPqO,YAAY,GACZ,ECjCN,EA9GwD9M,IAAmB,IAAlB,SAAE8L,GAAU9L,EACnE,OACE2B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,YAAWK,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oCAAmCK,SAAA,EAChDD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8BAA6BK,SAAA,EAC1CE,EAAAA,EAAAA,KAACiK,EAAAA,EAAM,IACAD,EAASE,MAAQ,CAAEjE,IAAK+D,EAASE,MACtChE,IAAK8D,EAAS5K,KACdA,KAAM4K,EAAS5K,KACfsH,KAAK,QAEP7G,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,oCAAmCK,SAAEkK,EAAS5K,QAC5DS,EAAAA,EAAAA,MAAA,KAAGJ,UAAU,wBAAuBK,SAAA,CAAC,OAAKkK,EAASjL,OACnDiB,EAAAA,EAAAA,KAAA,OAAKP,UAAU,OAAMK,UACnBE,EAAAA,EAAAA,KAAA,QAAMP,UAAW,4EACiB,aAAhCuK,EAASiB,mBACL,8BACgC,YAAhCjB,EAASiB,mBACP,gCACA,2BACLnL,SACAkK,EAASiB,mBACRjB,EAASiB,mBAAmBV,OAAO,GAAGC,cAAgBR,EAASiB,mBAAmBR,MAAM,GACxF,oBAMTT,EAASkB,UACRlL,EAAAA,EAAAA,KAAA,KACEmL,KAAMnB,EAASkB,QACflF,OAAO,SACPoF,IAAI,sBACJ3L,UAAU,uCAAsCK,SACjD,sBAMLD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,sEAAqEK,SAAA,EAClFD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,yCAAwCK,SAAC,yBACvDD,EAAAA,EAAAA,MAAA,MAAIJ,UAAU,YAAWK,SAAA,EACvBD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAC,oBACtCE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAEkK,EAASqB,oBAElDxL,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAEkK,EAAS1J,YAElDT,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,wBAAuBK,SAAEkK,EAASvJ,kBAKtDZ,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,yCAAwCK,SAAC,aACvDE,EAAAA,EAAAA,KAAA,WAASP,UAAU,mCAAkCK,SAClDkK,EAASsB,iBAKhBzL,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gCAA+BK,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,yCAAwCK,SAAC,gBACvDE,EAAAA,EAAAA,KAAA,OAAKP,UAAU,uBAAsBK,SAClCkK,EAASuB,YAAcvB,EAASuB,WAAWjQ,OAAS,EACnD0O,EAASuB,WAAWrL,KAAI,CAACsL,EAAUC,KACjCzL,EAAAA,EAAAA,KAAA,QAEEP,UAAU,oGAAmGK,SAE5G0L,GAHIC,MAOTzL,EAAAA,EAAAA,KAAA,QAAMP,UAAU,wBAAuBK,SAAC,iCAK9CD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gCAA+BK,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,yCAAwCK,SAAC,yBACvDD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8BAA6BK,SAAA,CACT,aAAhCkK,EAASiB,oBACRjL,EAAAA,EAAAA,KAACqK,EAAAA,EAAe,CAAC5K,UAAU,2BACO,YAAhCuK,EAASiB,oBACXjL,EAAAA,EAAAA,KAACmK,EAAAA,EAAS,CAAC1K,UAAU,6BAErBO,EAAAA,EAAAA,KAACsK,EAAAA,EAAW,CAAC7K,UAAU,0BAEzBO,EAAAA,EAAAA,KAAA,QAAMP,UAAU,wBAAuBK,SACJ,aAAhCkK,EAASiB,mBACN,WACgC,YAAhCjB,EAASiB,mBACP,uBACA,qBAIR,E,kCCzGV,MA2RA,EA3RwD/M,IAIjD,IAJkD,SACvDwN,EAAQ,SACRC,EAAQ,UACRC,GAAY,GACb1N,EACC,MAAOuE,EAAUoJ,IAAe9P,EAAAA,EAAAA,UAA2B,CACzD+P,aAAc,GACdxL,MAAO,GACPG,MAAO,GACP6K,QAAS,GACTS,aAAc,GACdpK,SAAU,GACVC,gBAAiB,GACjByJ,cAAe,GACfW,MAAO,QAGF/J,EAAQgK,IAAalQ,EAAAA,EAAAA,UAAiC,CAAC,IACvDwP,EAAYW,IAAiBnQ,EAAAA,EAAAA,UAAqB,KAClDoQ,EAAmBC,IAAwBrQ,EAAAA,EAAAA,WAAS,IAG3DuL,EAAAA,EAAAA,YAAU,KACgB1J,WACtB,IACEwO,GAAqB,GAErB,MAAMC,EAA6B,CACjC,CACEtN,GAAI,IACJK,KAAM,SACNkN,YAAa,kBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,YACNkN,YAAa,qBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,gBACNkN,YAAa,yBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,aACNkN,YAAa,sBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,aACNkN,YAAa,sBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,kBACNkN,YAAa,2BACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE/N,GAAI,IACJK,KAAM,aACNkN,YAAa,sBACbC,aAAc,EACdC,iBAAkB,EAClBC,OAAQ,SACRC,WAAW,IAAInL,MAAOoL,cACtBC,WAAW,IAAIrL,MAAOoL,cACtBE,sBAAsB,EACtBC,sBAAsB,IAG1BZ,EAAcG,EAChB,CAAE,MAAOpQ,GACP+I,QAAQ/I,MAAM,6BAA8BA,EAC9C,CAAC,QACCmQ,GAAqB,EACvB,GAGFW,EAAiB,GAChB,IAEH,MAAMC,EAAgB9H,IACpB,MAAM,KAAE9F,EAAI,MAAEC,GAAU6F,EAAEc,OAC1B6F,GAAYoB,IAAI,IAAUA,EAAM,CAAC7N,GAAOC,MAGpC4C,EAAO7C,IACT6M,GAAUgB,IAAI,IAAUA,EAAM,CAAC7N,GAAO,MACxC,EA4BI/B,EAAgBA,CAACL,EAAeJ,KACpCqP,GAAUgB,IAAI,IAAUA,EAAM,CAACjQ,GAAQJ,KAAW,EAWpD,OACEiD,EAAAA,EAAAA,MAAA,QAAM6L,SATcxG,IACpBA,EAAEC,iBArBqB+H,MACvB,MAAMC,EAAsB,CAC1BrB,aAAc,CAAC9J,EAAAA,GAAgBzC,SAAS,8BACxCe,MAAO,CAAC0B,EAAAA,GAAgBzC,SAAS,qBAAsByC,EAAAA,GAAgB1B,SACvEG,MAAO,CAACuB,EAAAA,GAAgBzC,SAAS,4BAA6ByC,EAAAA,GAAgBvB,SAC9E6K,QAAS,CAACtJ,EAAAA,GAAgBzC,SAAS,wBACnCwM,aAAc,CAAC/J,EAAAA,GAAgBzC,SAAS,8BACxCoC,SAAU,CAACK,EAAAA,GAAgBzC,SAAS,wBAAyByC,EAAAA,GAAgBL,YAC7EC,gBAAiB,CAACI,EAAAA,GAAgBzC,SAAS,gCAAiCyC,EAAAA,GAAgBoB,kBAGxFgK,GAAmBtL,EAAAA,EAAAA,GAAaW,EAAU0K,GAEhD,OADAlB,EAAUmB,GACsC,IAAzC7O,OAAO8O,KAAKD,GAAkB9R,MAAY,EAU7C4R,IACFxB,EAASjJ,EAAUpF,EACrB,EAI8BoC,UAAU,YAAWK,SAAA,EACjDD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,wCAAuCK,SAAA,EACpDE,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,gBACNC,KAAK,eACLC,MAAOoD,EAASqJ,aAChBxM,SAAU0N,EACV/Q,MAAOgG,EAAO6J,aACdvM,UAAQ,KAGVS,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,gBACNC,KAAK,eACL1C,KAAK,SACL2C,MAAOoD,EAASsJ,aAChBzM,SAAU0N,EACV/Q,MAAOgG,EAAO8J,aACdxM,UAAQ,EACRI,QAASwM,EACT/Q,QAAS,CACP,CAAEiE,MAAO,GAAIF,MAAO,6BACjBoM,EAAWrL,KAAKsL,IAAQ,CACzBnM,MAAOmM,EAASpM,KAChBD,MAAOqM,EAASpM,aAKtBY,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,gBACNC,KAAK,QACL1C,KAAK,QACL2C,MAAOoD,EAASnC,MAChBhB,SAAU0N,EACV/Q,MAAOgG,EAAO3B,MACdf,UAAQ,KAGVS,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,eACNC,KAAK,QACL1C,KAAK,MACL2C,MAAOoD,EAAShC,MAChBnB,SAAU0N,EACV/Q,MAAOgG,EAAOxB,MACdlB,UAAQ,KAGVS,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,WACNC,KAAK,WACL1C,KAAK,WACL2C,MAAOoD,EAASd,SAChBrC,SAAU0N,EACV/Q,MAAOgG,EAAON,SACdpC,UAAQ,KAGVS,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,mBACNC,KAAK,kBACL1C,KAAK,WACL2C,MAAOoD,EAASb,gBAChBtC,SAAU0N,EACV/Q,MAAOgG,EAAOL,gBACdrC,UAAQ,KAGVS,EAAAA,EAAAA,KAACsN,EAAAA,EAAS,CACRnO,MAAM,UACNC,KAAK,UACLC,MAAOoD,EAAS6I,QAChBhM,SAAU0N,EACV/Q,MAAOgG,EAAOqJ,QACd/L,UAAQ,EACRE,UAAU,sBAKdO,EAAAA,EAAAA,KAACuN,EAAAA,EAAW,CACVpO,MAAM,iBACNC,KAAK,QACLC,MAAOoD,EAASuJ,OAAS,KACzB1M,SA3HqBsF,IACzBiH,GAAYoB,IAAI,IAAUA,EAAMjB,MAAOpH,MAGnC3C,EAAO+J,OACTC,GAAUgB,IAAI,IAAUA,EAAMjB,MAAO,MACvC,EAsHI/P,MAAOgG,EAAO+J,MACdhI,QAAS,QACTC,aAAc,CAAC,aAAc,YAAa,YAAa,iBAGzDpE,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,6BAA4BK,SAAA,EACzCE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACL9Q,KAAK,SACL+Q,QAAQ,UACRhI,QAASkG,EACTjM,SAAUkM,EAAU9L,SACrB,YAGDE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACL9Q,KAAK,SACLiD,QAASiM,EAAU9L,SACpB,sBAIE,E,wBCxRX,MAoPA,EApPgC4N,KAC9B,MAAOC,EAAWC,IAAgB7R,EAAAA,EAAAA,UAAsD,QACjF6P,EAAWiC,IAAgB9R,EAAAA,EAAAA,WAAS,IACpC+R,EAAwBC,IAA6BhS,EAAAA,EAAAA,WAAS,IAC9DiS,EAAkBC,IAAuBlS,EAAAA,EAAAA,UAA0B,OACnEmS,EAA4BC,IAAiCpS,EAAAA,EAAAA,WAAS,IACtEqS,EAAmBC,IAAwBtS,EAAAA,EAAAA,WAAS,IACpDuS,EAAkBC,IAAuBxS,EAAAA,EAAAA,UAA0B,OACnEyS,EAAYC,IAAiB1S,EAAAA,EAAAA,WAAS,IAGvC,UACJsN,EACAuC,UAAW8C,EACXC,aAAcC,EACdC,aAAcC,EAAc,yBAC5BC,IACEC,EAAAA,EAAAA,MAGE,sBACJlR,EAAqB,WACrB3B,IACE8S,EAAAA,EAAAA,GAAgB,CAClBzT,qBAAqB,EACrBC,iBAAiB,IAIbyT,GAAoBC,EAAAA,EAAAA,UAAQ,IACd,QAAdxB,EAA4BtE,EACzBA,EAAU+F,QAAOpF,GAAYA,EAASiB,qBAAuB0C,KACnE,CAACtE,EAAWsE,IAMT0B,EAAsBrF,IAC1BiE,EAAoBjE,GACpBmE,GAA8B,EAAK,EAK/BmB,GAAuBlT,EAAAA,EAAAA,cAAa4N,IACxCuE,EAAoBvE,GACpBqE,GAAqB,EAAK,GACzB,IAEGkB,GAAwBnT,EAAAA,EAAAA,cAAYwB,UACxC,IAAK0Q,EAAkB,OAEvBG,GAAc,GACd,MAAMe,QAAe1R,GAAsBF,gBACnCkR,EAAeR,EAAiBvP,KAC/B,SACNxD,EAAW,mBAAmB+S,EAAiBlP,QAElDqP,GAAc,GACVe,GACFnB,GAAqB,GACrBE,EAAoB,OAEpBvJ,QAAQ/I,MAAM,4BAChB,GACC,CAACqS,EAAkBxQ,EAAuBgR,IAEvCW,GAAoBrT,EAAAA,EAAAA,cAAYwB,MAAO8R,EAAgCrS,KAC3EwQ,GAAa,GACb1R,IAEA,MAAMqT,QAAe1R,GAAsBF,UACzC,MAAM+R,QAAoBf,EAAec,GAAc,GAEvD,OADA3B,GAA0B,GACnB4B,CAAW,GACjBtS,EAAe,gBAElBwQ,GAAa,GAET2B,GACFxK,QAAQ4K,IAAI,+BAAgCJ,EAC9C,GACC,CAAC1R,EAAuB8Q,EAAgBzS,IAErC0T,EAAuBjS,MAAOkS,EAAoBC,KACtD,UACQhB,EAAyBe,EAAYC,GAC3C5B,GAA8B,EAChC,CAAE,MAAOlS,GACP+I,QAAQ/I,MAAM,sCAAuCA,EAEvD,GAGF,OACE4D,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,YAAWK,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8EAA6EK,SAAA,EAC1FD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIP,UAAU,mCAAkCK,SAAC,eACjDE,EAAAA,EAAAA,KAAA,KAAGP,UAAU,6BAA4BK,SAAC,sDAE5CE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLpD,MAAMpK,EAAAA,EAAAA,KAACoJ,EAAmB,CAAC3J,UAAU,YACrCgG,QAASA,IAAMsI,GAA0B,GAAMjO,SAChD,qBAKHD,EAAAA,EAAAA,MAACmQ,EAAAA,EAAI,CAAAlQ,SAAA,EACHD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,4BAA2BK,SAAA,EACxCE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAuB,QAAdE,EAAsB,UAAY,UAC3CjH,KAAK,KACLjB,QAASA,IAAMmI,EAAa,OAAO9N,SACpC,mBAGDE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAuB,YAAdE,EAA0B,UAAY,UAC/CjH,KAAK,KACLjB,QAASA,IAAMmI,EAAa,WAAW9N,SACxC,0BAGDE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAuB,aAAdE,EAA2B,UAAY,UAChDjH,KAAK,KACLjB,QAASA,IAAMmI,EAAa,YAAY9N,SACzC,cAGDE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAuB,aAAdE,EAA2B,UAAY,UAChDjH,KAAK,KACLjB,QAASA,IAAMmI,EAAa,YAAY9N,SACzC,gBAKF4O,GACC1O,EAAAA,EAAAA,KAAA,OAAKP,UAAU,2BAA0BK,UACvCE,EAAAA,EAAAA,KAACiQ,EAAAA,EAAc,OAGjBjQ,EAAAA,EAAAA,KAACkQ,EAAY,CACX7G,UAAW6F,EACX1F,gBAnHmBQ,IAC3BqF,EAAmBrF,EAAS,EAmHpBV,eAAgB+F,EAChB9F,iBAAkB+F,EAClB3S,MAAO,GAAGgR,EAAYA,EAAUpD,OAAO,GAAGC,cAAgBmD,EAAUlD,MAAM,GAAK,oBAAoByE,EAAkB5T,gBAM3H0E,EAAAA,EAAAA,KAACuG,EAAAA,EAAK,CACJC,OAAQsH,EACRrH,QAASA,IAAMsH,GAA0B,GACzCpR,MAAM,mBACN+J,KAAK,KAAI5G,UAETE,EAAAA,EAAAA,KAACmQ,EAAe,CACdzE,SAAU+D,EACV9D,SAAUA,IAAMoC,GAA0B,GAC1CnC,UAAWA,MAKdoC,IACChO,EAAAA,EAAAA,KAACuG,EAAAA,EAAK,CACJC,OAAQ0H,EACRzH,QAASA,IAAM0H,GAA8B,GAC7CxR,MAAM,mBACN+J,KAAK,KACLC,QACE9G,EAAAA,EAAAA,MAAAuQ,EAAAA,SAAA,CAAAtQ,SAAA,EACEE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAQ,UACRhI,QAASA,IAAM0I,GAA8B,GAAOrO,SACrD,UAGwC,YAAxCkO,EAAiB/C,qBAChBjL,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAQ,UACRhI,QAASA,IAAMoK,EAAqB7B,EAAiBjP,GAAI,YAAYe,SACtE,WAIsC,aAAxCkO,EAAiB/C,qBAChBjL,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAQ,UACRhI,QAASA,IAAMoK,EAAqB7B,EAAiBjP,GAAI,WAAWe,SACrE,sBAKNA,UAEDE,EAAAA,EAAAA,KAACqQ,EAAe,CAACrG,SAAUgE,MAK9BM,IACCtO,EAAAA,EAAAA,KAACuG,EAAAA,EAAK,CACJC,OAAQ4H,EACR3H,QAASA,IAAM4H,GAAqB,GACpC1R,MAAM,kBACN+J,KAAK,KACLC,QACE9G,EAAAA,EAAAA,MAAAuQ,EAAAA,SAAA,CAAAtQ,SAAA,EACEE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAQ,UACRhI,QAASA,IAAM4I,GAAqB,GACpC3O,SAAU8O,EAAW1O,SACtB,YAGDE,EAAAA,EAAAA,KAACwN,EAAAA,EAAM,CACLC,QAAQ,SACRhI,QAAS8J,EACT5P,QAAS6O,EAAW1O,SACrB,uBAIJA,UAEDD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,wBAAuBK,SAAA,CAAC,oCACHwO,EAAiBlP,KAAK,0CAI1D,C", "sources": ["hooks/useErrorHandler.ts", "../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/FormField.tsx", "utils/validation.ts", "components/common/ImageUpload.tsx", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "../node_modules/@heroicons/react/24/outline/esm/BuildingOffice2Icon.js", "features/suppliers/components/SupplierList.tsx", "features/suppliers/components/SupplierDetails.tsx", "features/suppliers/components/AddSupplierForm.tsx", "pages/SuppliersPage.tsx"], "sourcesContent": ["/**\r\n * Error <PERSON>ler Hook\r\n * \r\n * This hook provides React-specific error handling utilities and state management.\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { \r\n  handleApiError, \r\n  handleValidationError, \r\n  handleFormError,\r\n  logError,\r\n  reportError,\r\n  type ApiError,\r\n  type ValidationError \r\n} from '../utils/errorHandling';\r\nimport useNotification from './useNotification';\r\n\r\ninterface ErrorState {\r\n  hasError: boolean;\r\n  error: Error | ApiError | ValidationError | null;\r\n  errorType: 'api' | 'validation' | 'form' | 'general' | null;\r\n  context?: string;\r\n}\r\n\r\ninterface UseErrorHandlerOptions {\r\n  enableNotifications?: boolean;\r\n  enableReporting?: boolean;\r\n  onError?: (error: any, context?: string) => void;\r\n}\r\n\r\nexport const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {\r\n  const { enableNotifications = true, enableReporting = true, onError } = options;\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [errorState, setErrorState] = useState<ErrorState>({\r\n    hasError: false,\r\n    error: null,\r\n    errorType: null\r\n  });\r\n\r\n  // Clear error state\r\n  const clearError = useCallback(() => {\r\n    setErrorState({\r\n      hasError: false,\r\n      error: null,\r\n      errorType: null\r\n    });\r\n  }, []);\r\n\r\n  // Handle API errors\r\n  const handleApiErrorWithState = useCallback((error: any, context?: string) => {\r\n    const apiError = handleApiError(\r\n      error,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: apiError,\r\n      errorType: 'api',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return apiError;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle validation errors\r\n  const handleValidationErrorWithState = useCallback((\r\n    field: string,\r\n    message: string,\r\n    code?: string,\r\n    context?: string\r\n  ) => {\r\n    const validationError = handleValidationError(field, message, code);\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: validationError,\r\n      errorType: 'validation',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Validation Error',\r\n        message: validationError.message\r\n      });\r\n    }\r\n\r\n    if (onError) {\r\n      onError(validationError, context);\r\n    }\r\n\r\n    return validationError;\r\n  }, [enableNotifications, showNotification, onError]);\r\n\r\n  // Handle form errors\r\n  const handleFormErrorWithState = useCallback((\r\n    error: any,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ) => {\r\n    handleFormError(\r\n      error,\r\n      setFieldError,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error,\r\n      errorType: 'form',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle general errors\r\n  const handleGeneralError = useCallback((error: any, context?: string) => {\r\n    const errorObj = error instanceof Error ? error : new Error(String(error));\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: errorObj,\r\n      errorType: 'general',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorObj.message\r\n      });\r\n    }\r\n\r\n    if (enableReporting) {\r\n      reportError(errorObj, context);\r\n    }\r\n\r\n    logError(errorObj, context);\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return errorObj;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Async operation wrapper with error handling\r\n  const withErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleApiErrorWithState(error, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleApiErrorWithState]);\r\n\r\n  // Form submission wrapper with error handling\r\n  const withFormErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleFormErrorWithState(error, setFieldError, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleFormErrorWithState]);\r\n\r\n  return {\r\n    // Error state\r\n    ...errorState,\r\n    \r\n    // Error handlers\r\n    handleApiError: handleApiErrorWithState,\r\n    handleValidationError: handleValidationErrorWithState,\r\n    handleFormError: handleFormErrorWithState,\r\n    handleGeneralError,\r\n    clearError,\r\n    \r\n    // Wrapper functions\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    \r\n    // Utility functions\r\n    isApiError: (error: any): error is ApiError => \r\n      error && typeof error === 'object' && 'status' in error,\r\n    isValidationError: (error: any): error is ValidationError => \r\n      error && typeof error === 'object' && 'field' in error,\r\n  };\r\n};\r\n\r\nexport default useErrorHandler;\r\n", "import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "import React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  name: string;\r\n  type?: string;\r\n  value: any;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  className?: string;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  name,\r\n  type = 'text',\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  placeholder = '',\r\n  options = [],\r\n  className = '',\r\n  disabled = false,\r\n  loading = false\r\n}) => {\r\n  const inputClasses = `mt-1 block w-full rounded-md shadow-sm sm:text-sm ${\r\n    error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary focus:ring-primary'\r\n  }`;\r\n  \r\n  const renderField = () => {\r\n    switch (type) {\r\n      case 'textarea':\r\n        return (\r\n          <textarea\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      case 'select':\r\n        return (\r\n          <select\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            disabled={disabled || loading}\r\n          >\r\n            {loading ? (\r\n              <option value=\"\">Loading...</option>\r\n            ) : (\r\n              options.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </select>\r\n        );\r\n      \r\n      case 'checkbox':\r\n        return (\r\n          <input\r\n            type=\"checkbox\"\r\n            id={name}\r\n            name={name}\r\n            checked={value}\r\n            onChange={onChange}\r\n            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      default:\r\n        return (\r\n          <input\r\n            type={type}\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div className={`${className}`}>\r\n      <label htmlFor={name} className=\"block text-sm font-medium text-gray-700\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      {renderField()}\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "/**\r\n * Image Upload Component\r\n * \r\n * A reusable component for uploading and previewing images with drag and drop support.\r\n */\r\n\r\nimport React, { useState, useRef, useCallback } from 'react';\r\nimport { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { validateFile } from '../../utils/errorHandling';\r\n\r\ninterface ImageUploadProps {\r\n  label: string;\r\n  name: string;\r\n  value?: File | string | null;\r\n  onChange: (file: File | null) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  maxSize?: number; // in bytes\r\n  allowedTypes?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst ImageUpload: React.FC<ImageUploadProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  disabled = false,\r\n  maxSize = 5 * 1024 * 1024, // 5MB default\r\n  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n  className = ''\r\n}) => {\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const [preview, setPreview] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Generate preview URL when value changes\r\n  React.useEffect(() => {\r\n    if (value instanceof File) {\r\n      const url = URL.createObjectURL(value);\r\n      setPreview(url);\r\n      return () => URL.revokeObjectURL(url);\r\n    } else if (typeof value === 'string' && value) {\r\n      setPreview(value);\r\n      return;\r\n    } else {\r\n      setPreview(null);\r\n      return;\r\n    }\r\n  }, [value]);\r\n\r\n  const handleFileSelect = useCallback((file: File) => {\r\n    const validation = validateFile(file, {\r\n      maxSize,\r\n      allowedTypes\r\n    });\r\n\r\n    if (validation.valid) {\r\n      onChange(file);\r\n    } else {\r\n      // Handle validation error - you might want to show this error\r\n      console.error('File validation failed:', validation.error);\r\n    }\r\n  }, [maxSize, allowedTypes, onChange]);\r\n\r\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    if (!disabled) {\r\n      setIsDragOver(true);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    \r\n    if (disabled) return;\r\n\r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleClick = () => {\r\n    if (!disabled && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        className={`\r\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\r\n          ${isDragOver ? 'border-primary bg-primary bg-opacity-5' : 'border-gray-300'}\r\n          ${error ? 'border-red-300' : ''}\r\n          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-gray-50'}\r\n        `}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={handleClick}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          name={name}\r\n          accept={allowedTypes.join(',')}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n          disabled={disabled}\r\n        />\r\n\r\n        {preview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={preview}\r\n              alt=\"Preview\"\r\n              className=\"mx-auto h-32 w-32 object-cover rounded-lg\"\r\n            />\r\n            {!disabled && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  handleRemove();\r\n                }}\r\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\"\r\n              >\r\n                <XMarkIcon className=\"h-4 w-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <div className=\"mt-4\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isDragOver ? 'Drop image here' : 'Click to upload or drag and drop'}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                PNG, JPG, GIF up to {Math.round(maxSize / 1024 / 1024)}MB\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageUpload;\r\n", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction BuildingOffice2Icon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BuildingOffice2Icon);\nexport default ForwardRef;", "/**\r\n * Supplier List Component\r\n *\r\n * This component displays a list of suppliers in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ Supplier } from '../types/index';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TrashIcon,\r\n  EyeIcon,\r\n  EnvelopeIcon,\r\n  PhoneIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface SupplierListProps {\r\n  suppliers: Supplier[];\r\n  onViewSupplier: (supplier: Supplier) => void;\r\n  onDeleteSupplier: (supplier: Supplier) => void;\r\n  onSupplierClick: (supplier: Supplier) => void;\r\n  title?: string;\r\n}\r\n\r\nconst SupplierList: React.FC<SupplierListProps> = ({\r\n  suppliers,\r\n  onViewSupplier,\r\n  onDeleteSupplier,\r\n  onSupplierClick: _onSupplierClick, // Keep for interface compatibility but use navigation instead\r\n  title = 'Suppliers'\r\n}) => {\r\n  const navigate = useNavigate();\r\n\r\n  // Handle row click to navigate to supplier profile page\r\n  const handleRowClick = (supplier: Supplier) => {\r\n    navigate(ROUTES.getSupplierProfileRoute(supplier.id));\r\n  };\r\n  const columns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Supplier Name',\r\n      sortable: true,\r\n      render: (_value: string, supplier: Supplier) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"mr-3\">\r\n            <Avatar\r\n              {...(supplier.logo && { src: supplier.logo })}\r\n              alt={supplier.name}\r\n              name={supplier.name}\r\n              size=\"sm\"\r\n            />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{supplier.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {supplier.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'email',\r\n      label: 'Email',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'phone',\r\n      label: 'Phone',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <PhoneIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'verificationStatus',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => {\r\n        // Handle undefined or null values\r\n        if (!value) {\r\n          return (\r\n            <div className=\"flex items-center\">\r\n              <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />\r\n              <span>Unknown</span>\r\n            </div>\r\n          );\r\n        }\r\n\r\n        let icon;\r\n        switch(value) {\r\n          case 'verified':\r\n            icon = <CheckCircleIcon className=\"w-4 h-4 text-green-500 mr-1\" />;\r\n            break;\r\n          case 'pending':\r\n            icon = <ClockIcon className=\"w-4 h-4 text-yellow-500 mr-1\" />;\r\n            break;\r\n          case 'rejected':\r\n            icon = <XCircleIcon className=\"w-4 h-4 text-red-500 mr-1\" />;\r\n            break;\r\n          default:\r\n            icon = <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />;\r\n        }\r\n        return (\r\n          <div className=\"flex items-center\">\r\n            {icon}\r\n            <span>{value ? value.charAt(0).toUpperCase() + value.slice(1) : 'Unknown'}</span>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: any, supplier: Supplier) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onViewSupplier(supplier);\r\n            }}\r\n            title=\"View supplier details\"\r\n          >\r\n            <EyeIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDeleteSupplier(supplier);\r\n            }}\r\n            title=\"Delete supplier\"\r\n          >\r\n            <TrashIcon className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      columns={columns}\r\n      data={suppliers}\r\n      onRowClick={handleRowClick}\r\n      title={title}\r\n      pagination={true}\r\n    />\r\n  );\r\n};\r\n\r\nexport default SupplierList;\r\n", "/**\r\n * Supplier Details Component\r\n * \r\n * This component displays detailed information about a supplier.\r\n */\r\n\r\nimport React from 'react';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ Supplier } from '../types/index';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface SupplierDetailsProps {\r\n  supplier: Supplier;\r\n}\r\n\r\nconst SupplierDetails: React.FC<SupplierDetailsProps> = ({ supplier }) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Avatar\r\n            {...(supplier.logo && { src: supplier.logo })}\r\n            alt={supplier.name}\r\n            name={supplier.name}\r\n            size=\"xl\"\r\n          />\r\n          <div>\r\n            <h3 className=\"text-lg font-medium text-gray-900\">{supplier.name}</h3>\r\n            <p className=\"text-sm text-gray-500\">ID: {supplier.id}</p>\r\n            <div className=\"mt-1\">\r\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                supplier.verificationStatus === 'verified'\r\n                  ? 'bg-green-100 text-green-800'\r\n                  : supplier.verificationStatus === 'pending'\r\n                    ? 'bg-yellow-100 text-yellow-800'\r\n                    : 'bg-red-100 text-red-800'\r\n              }`}>\r\n                {supplier.verificationStatus ?\r\n                  supplier.verificationStatus.charAt(0).toUpperCase() + supplier.verificationStatus.slice(1) :\r\n                  'Unknown'\r\n                }\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {supplier.website && (\r\n          <a\r\n            href={supplier.website}\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"text-primary hover:underline text-sm\"\r\n          >\r\n            Visit Website\r\n          </a>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4\">\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Contact Information</h4>\r\n          <dl className=\"space-y-3\">\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Contact Person</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.contactPerson}</dd>\r\n            </div>\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Email</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.email}</dd>\r\n            </div>\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Phone</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.phone}</dd>\r\n            </div>\r\n          </dl>\r\n        </div>\r\n\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Address</h4>\r\n          <address className=\"not-italic text-sm text-gray-900\">\r\n            {supplier.address}\r\n          </address>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Categories</h4>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {supplier.categories && supplier.categories.length > 0 ? (\r\n            supplier.categories.map((category, index) => (\r\n              <span\r\n                key={index}\r\n                className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\r\n              >\r\n                {category}\r\n              </span>\r\n            ))\r\n          ) : (\r\n            <span className=\"text-gray-500 text-sm\">No categories assigned</span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Verification Status</h4>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {supplier.verificationStatus === 'verified' ? (\r\n            <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />\r\n          ) : supplier.verificationStatus === 'pending' ? (\r\n            <ClockIcon className=\"w-5 h-5 text-yellow-500\" />\r\n          ) : (\r\n            <XCircleIcon className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n          <span className=\"text-sm text-gray-700\">\r\n            {supplier.verificationStatus === 'verified'\r\n              ? 'Verified'\r\n              : supplier.verificationStatus === 'pending'\r\n                ? 'Pending verification'\r\n                : 'Rejected'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierDetails;\r\n", "/**\r\n * Add Supplier Form Component\r\n *\r\n * This component provides a form for adding new suppliers.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport FormField from '../../../components/common/FormField';\r\nimport ImageUpload from '../../../components/common/ImageUpload';\r\nimport type{ SupplierFormData } from '../types/index';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport type { Category } from '../../categories/types';\r\n\r\ninterface AddSupplierFormProps {\r\n  onSubmit: (supplierData: SupplierFormData, setFieldError?: (field: string, message: string) => void) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddSupplierForm: React.FC<AddSupplierFormProps> = ({\r\n  onSubmit,\r\n  onCancel,\r\n  isLoading = false\r\n}) => {\r\n  const [formData, setFormData] = useState<SupplierFormData>({\r\n    supplierName: '',\r\n    email: '',\r\n    phone: '',\r\n    address: '',\r\n    businessType: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    contactPerson: '', // Required field for backend\r\n    image: null\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [loadingCategories, setLoadingCategories] = useState(true);\r\n\r\n  // Fetch categories for business type dropdown\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        setLoadingCategories(true);\r\n        // For now, use mock data. In production, you would use the categories API\r\n        const mockCategories: Category[] = [\r\n          {\r\n            id: '1',\r\n            name: 'Retail',\r\n            description: 'Retail business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '2',\r\n            name: 'Wholesale',\r\n            description: 'Wholesale business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '3',\r\n            name: 'Manufacturing',\r\n            description: 'Manufacturing business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '4',\r\n            name: 'Technology',\r\n            description: 'Technology business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '5',\r\n            name: 'Healthcare',\r\n            description: 'Healthcare business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '6',\r\n            name: 'Food & Beverage',\r\n            description: 'Food & Beverage business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '7',\r\n            name: 'Automotive',\r\n            description: 'Automotive business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          }\r\n        ];\r\n        setCategories(mockCategories);\r\n      } catch (error) {\r\n        console.error('Error fetching categories:', error);\r\n      } finally {\r\n        setLoadingCategories(false);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (file: File | null) => {\r\n    setFormData(prev => ({ ...prev, image: file }));\r\n\r\n    // Clear error when field is edited\r\n    if (errors.image) {\r\n      setErrors(prev => ({ ...prev, image: '' }));\r\n    }\r\n  };\r\n\r\n  const validateFormData = () => {\r\n    const formValidationRules = {\r\n      supplierName: [validationRules.required('Supplier name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      phone: [validationRules.required('Phone number is required'), validationRules.phone()],\r\n      address: [validationRules.required('Address is required')],\r\n      businessType: [validationRules.required('Business type is required')],\r\n      password: [validationRules.required('Password is required'), validationRules.password()],\r\n      confirmPassword: [validationRules.required('Confirm password is required'), validationRules.passwordMatch()],\r\n    };\r\n\r\n    const validationErrors = validateForm(formData, formValidationRules);\r\n    setErrors(validationErrors);\r\n    return Object.keys(validationErrors).length === 0;\r\n  };\r\n\r\n  const setFieldError = (field: string, message: string) => {\r\n    setErrors(prev => ({ ...prev, [field]: message }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateFormData()) {\r\n      onSubmit(formData, setFieldError);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n        <FormField\r\n          label=\"Supplier Name\"\r\n          name=\"supplierName\"\r\n          value={formData.supplierName}\r\n          onChange={handleChange}\r\n          error={errors.supplierName}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Business Type\"\r\n          name=\"businessType\"\r\n          type=\"select\"\r\n          value={formData.businessType}\r\n          onChange={handleChange}\r\n          error={errors.businessType}\r\n          required\r\n          loading={loadingCategories}\r\n          options={[\r\n            { value: '', label: 'Select a business type' },\r\n            ...categories.map((category) => ({\r\n              value: category.name,\r\n              label: category.name\r\n            }))\r\n          ]}\r\n        />\r\n\r\n        <FormField\r\n          label=\"Email Address\"\r\n          name=\"email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          error={errors.email}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Phone Number\"\r\n          name=\"phone\"\r\n          type=\"tel\"\r\n          value={formData.phone}\r\n          onChange={handleChange}\r\n          error={errors.phone}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Password\"\r\n          name=\"password\"\r\n          type=\"password\"\r\n          value={formData.password}\r\n          onChange={handleChange}\r\n          error={errors.password}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Confirm Password\"\r\n          name=\"confirmPassword\"\r\n          type=\"password\"\r\n          value={formData.confirmPassword}\r\n          onChange={handleChange}\r\n          error={errors.confirmPassword}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Address\"\r\n          name=\"address\"\r\n          value={formData.address}\r\n          onChange={handleChange}\r\n          error={errors.address}\r\n          required\r\n          className=\"sm:col-span-2\"\r\n        />\r\n      </div>\r\n\r\n      {/* Image Upload Field */}\r\n      <ImageUpload\r\n        label=\"Supplier Image\"\r\n        name=\"image\"\r\n        value={formData.image || null}\r\n        onChange={handleImageChange}\r\n        error={errors.image}\r\n        maxSize={5 * 1024 * 1024} // 5MB\r\n        allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\r\n      />\r\n\r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button \r\n          type=\"button\" \r\n          variant=\"outline\" \r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button \r\n          type=\"submit\" \r\n          loading={isLoading}\r\n        >\r\n          Add Supplier\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddSupplierForm;\r\n\r\n\r\n\r\n", "/**\r\n * Suppliers Page\r\n *\r\n * This page displays and manages suppliers in the system.\r\n */\r\n\r\nimport React, { useState, useMemo, useCallback } from 'react';\r\nimport Button from '../components/common/Button';\r\nimport Card from '../components/common/Card';\r\nimport Modal from '../components/common/Modal';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport { BuildingOffice2Icon } from '@heroicons/react/24/outline';\r\nimport SupplierList from '../features/suppliers/components/SupplierList';\r\nimport SupplierDetails from '../features/suppliers/components/SupplierDetails';\r\nimport AddSupplierForm from '../features/suppliers/components/AddSupplierForm';\r\nimport type { Supplier, SupplierFormData } from '../features/suppliers/types';\r\nimport { useSuppliers } from '../features/suppliers/hooks/useSuppliers';\r\nimport useErrorHandler from '../hooks/useErrorHandler';\r\n\r\nconst SuppliersPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'verified' | 'rejected'>('all');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isAddSupplierModalOpen, setIsAddSupplierModalOpen] = useState(false);\r\n  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);\r\n  const [isSupplierDetailsModalOpen, setIsSupplierDetailsModalOpen] = useState(false);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  // Use the useSuppliers hook for API integration\r\n  const {\r\n    suppliers,\r\n    isLoading: suppliersLoading,\r\n    createEntity: createSupplier,\r\n    deleteEntity: deleteSupplier,\r\n    updateVerificationStatus\r\n  } = useSuppliers();\r\n\r\n  // Error handling\r\n  const {\r\n    withFormErrorHandling,\r\n    clearError\r\n  } = useErrorHandler({\r\n    enableNotifications: true,\r\n    enableReporting: true\r\n  });\r\n\r\n  // Memoize filtered suppliers to prevent unnecessary recalculations\r\n  const filteredSuppliers = useMemo(() => {\r\n    if (activeTab === 'all') return suppliers;\r\n    return suppliers.filter(supplier => supplier.verificationStatus === activeTab);\r\n  }, [suppliers, activeTab]);\r\n\r\n  const handleSupplierClick = (supplier: Supplier) => {\r\n    handleViewSupplier(supplier);\r\n  };\r\n\r\n  const handleViewSupplier = (supplier: Supplier) => {\r\n    setSelectedSupplier(supplier);\r\n    setIsSupplierDetailsModalOpen(true);\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteSupplier = useCallback((supplier: Supplier) => {\r\n    setSupplierToDelete(supplier);\r\n    setIsDeleteModalOpen(true);\r\n  }, []);\r\n\r\n  const confirmDeleteSupplier = useCallback(async () => {\r\n    if (!supplierToDelete) return;\r\n\r\n    setIsDeleting(true);\r\n    const result = await withFormErrorHandling(async () => {\r\n      await deleteSupplier(supplierToDelete.id);\r\n      return true;\r\n    }, undefined, `Delete supplier ${supplierToDelete.name}`);\r\n\r\n    setIsDeleting(false);\r\n    if (result) {\r\n      setIsDeleteModalOpen(false);\r\n      setSupplierToDelete(null);\r\n    } else {\r\n      console.error('Failed to delete supplier');\r\n    }\r\n  }, [supplierToDelete, withFormErrorHandling, deleteSupplier]);\r\n\r\n  const handleAddSupplier = useCallback(async (supplierData: SupplierFormData, setFieldError?: (field: string, message: string) => void) => {\r\n    setIsLoading(true);\r\n    clearError();\r\n\r\n    const result = await withFormErrorHandling(async () => {\r\n      const newSupplier = await createSupplier(supplierData, false); // Don't show notifications from hook\r\n      setIsAddSupplierModalOpen(false);\r\n      return newSupplier;\r\n    }, setFieldError, 'Add Supplier');\r\n\r\n    setIsLoading(false);\r\n\r\n    if (result) {\r\n      console.log('Supplier added successfully:', result);\r\n    }\r\n  }, [withFormErrorHandling, createSupplier, clearError]);\r\n\r\n  const handleVerifySupplier = async (supplierId: string, newStatus: 'verified' | 'pending') => {\r\n    try {\r\n      await updateVerificationStatus(supplierId, newStatus);\r\n      setIsSupplierDetailsModalOpen(false);\r\n    } catch (error) {\r\n      console.error('Error updating verification status:', error);\r\n      // Error notification is handled by the hook\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">Suppliers</h1>\r\n          <p className=\"mt-1 text-sm text-gray-500\">Manage your suppliers and verify applications</p>\r\n        </div>\r\n        <Button\r\n          icon={<BuildingOffice2Icon className=\"h-5 w-5\" />}\r\n          onClick={() => setIsAddSupplierModalOpen(true)}\r\n        >\r\n          Add Supplier\r\n        </Button>\r\n      </div>\r\n\r\n      <Card>\r\n        <div className=\"flex flex-wrap gap-3 mb-6\">\r\n          <Button\r\n            variant={activeTab === 'all' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('all')}\r\n          >\r\n            All Suppliers\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'pending' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('pending')}\r\n          >\r\n            Pending Verification\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'verified' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('verified')}\r\n          >\r\n            Verified\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'rejected' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('rejected')}\r\n          >\r\n            Rejected\r\n          </Button>\r\n        </div>\r\n\r\n        {suppliersLoading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner />\r\n          </div>\r\n        ) : (\r\n          <SupplierList\r\n            suppliers={filteredSuppliers}\r\n            onSupplierClick={handleSupplierClick}\r\n            onViewSupplier={handleViewSupplier}\r\n            onDeleteSupplier={handleDeleteSupplier}\r\n            title={`${activeTab ? activeTab.charAt(0).toUpperCase() + activeTab.slice(1) : 'All'} Suppliers (${filteredSuppliers.length})`}\r\n          />\r\n        )}\r\n      </Card>\r\n\r\n      {/* Add Supplier Modal */}\r\n      <Modal\r\n        isOpen={isAddSupplierModalOpen}\r\n        onClose={() => setIsAddSupplierModalOpen(false)}\r\n        title=\"Add New Supplier\"\r\n        size=\"lg\"\r\n      >\r\n        <AddSupplierForm\r\n          onSubmit={handleAddSupplier}\r\n          onCancel={() => setIsAddSupplierModalOpen(false)}\r\n          isLoading={isLoading}\r\n        />\r\n      </Modal>\r\n\r\n      {/* Supplier Details Modal */}\r\n      {selectedSupplier && (\r\n        <Modal\r\n          isOpen={isSupplierDetailsModalOpen}\r\n          onClose={() => setIsSupplierDetailsModalOpen(false)}\r\n          title=\"Supplier Details\"\r\n          size=\"lg\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsSupplierDetailsModalOpen(false)}\r\n              >\r\n                Close\r\n              </Button>\r\n              {selectedSupplier.verificationStatus === 'pending' && (\r\n                <Button\r\n                  variant=\"success\"\r\n                  onClick={() => handleVerifySupplier(selectedSupplier.id, 'verified')}\r\n                >\r\n                  Verify\r\n                </Button>\r\n              )}\r\n              {selectedSupplier.verificationStatus === 'verified' && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => handleVerifySupplier(selectedSupplier.id, 'pending')}\r\n                >\r\n                  Set to Pending\r\n                </Button>\r\n              )}\r\n            </>\r\n          }\r\n        >\r\n          <SupplierDetails supplier={selectedSupplier} />\r\n        </Modal>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {supplierToDelete && (\r\n        <Modal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          title=\"Delete Supplier\"\r\n          size=\"sm\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsDeleteModalOpen(false)}\r\n                disabled={isDeleting}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={confirmDeleteSupplier}\r\n                loading={isDeleting}\r\n              >\r\n                Delete Supplier\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"text-sm text-gray-500\">\r\n            Are you sure you want to delete \"{supplierToDelete.name}\"? This action cannot be undone.\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuppliersPage;"], "names": ["options", "arguments", "length", "undefined", "enableNotifications", "enableReporting", "onError", "showNotification", "useNotification", "errorState", "setErrorState", "useState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorType", "clearError", "useCallback", "handleApiErrorWithState", "context", "apiError", "handleApiError", "notification", "type", "title", "message", "Error", "reportError", "handleValidationErrorWithState", "field", "code", "validationError", "handleValidationError", "handleFormErrorWithState", "setFieldError", "handleFormError", "handleGeneralError", "errorObj", "String", "logError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "operation", "withFormError<PERSON>andling", "isApiError", "isValidationError", "PhotoIcon", "_ref", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "label", "name", "value", "onChange", "required", "placeholder", "className", "disabled", "loading", "inputClasses", "_jsxs", "children", "htmlFor", "_jsx", "renderField", "map", "option", "checked", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "trim", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "validateForm", "values", "validationRules", "errors", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "formData", "ruleArray", "rule", "validator", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "maxSize", "allowedTypes", "isDragOver", "setIsDragOver", "preview", "setPreview", "fileInputRef", "useRef", "File", "createObjectURL", "revokeObjectURL", "handleFileSelect", "file", "validation", "validateFile", "valid", "console", "onDragOver", "e", "preventDefault", "onDragLeave", "onDrop", "_e$dataTransfer$files", "dataTransfer", "files", "onClick", "handleClick", "current", "click", "accept", "join", "_e$target$files", "target", "src", "alt", "stopPropagation", "XMarkIcon", "Math", "round", "Modal", "isOpen", "onClose", "size", "footer", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "testId", "modalRef", "useEffect", "handleEscape", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "modalContent", "Fragment", "xs", "sm", "md", "lg", "xl", "full", "createPortal", "memo", "EnvelopeIcon", "TrashIcon", "EyeIcon", "PhoneIcon", "BuildingOffice2Icon", "suppliers", "onViewSupplier", "onDeleteSupplier", "onSupplierClick", "_onSupplierClick", "navigate", "useNavigate", "columns", "sortable", "render", "_value", "supplier", "Avatar", "logo", "ClockIcon", "icon", "CheckCircleIcon", "XCircleIcon", "char<PERSON>t", "toUpperCase", "slice", "_", "DataTable", "data", "onRowClick", "ROUTES", "getSupplierProfileRoute", "pagination", "verificationStatus", "website", "href", "rel", "<PERSON><PERSON><PERSON>", "address", "categories", "category", "index", "onSubmit", "onCancel", "isLoading", "setFormData", "supplierName", "businessType", "image", "setErrors", "setCategories", "loadingCategories", "setLoadingCategories", "mockCategories", "description", "productCount", "subcategoryCount", "status", "createdAt", "toISOString", "updatedAt", "visibleInSupplierApp", "visibleInCustomerApp", "fetchCategories", "handleChange", "prev", "validateFormData", "formValidationRules", "validationErrors", "keys", "FormField", "ImageUpload", "<PERSON><PERSON>", "variant", "SuppliersPage", "activeTab", "setActiveTab", "setIsLoading", "isAddSupplierModalOpen", "setIsAddSupplierModalOpen", "selectedSupplier", "setSelectedSupplier", "isSupplierDetailsModalOpen", "setIsSupplierDetailsModalOpen", "isDeleteModalOpen", "setIsDeleteModalOpen", "supplierToDelete", "setSupplierToDelete", "isDeleting", "setIsDeleting", "suppliersLoading", "createEntity", "createSupplier", "deleteEntity", "deleteSupplier", "updateVerificationStatus", "useSuppliers", "useErrorHandler", "filteredSuppliers", "useMemo", "filter", "handleViewSupplier", "handleDeleteSupplier", "confirmDeleteSupplier", "result", "handleAddSupplier", "supplierData", "newSupplier", "log", "handleVerifySupplier", "supplierId", "newStatus", "Card", "LoadingSpinner", "SupplierList", "AddSupplierForm", "_Fragment", "SupplierDetails"], "sourceRoot": ""}